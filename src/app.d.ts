// See https://kit.svelte.dev/docs/types#app
// for information about these interfaces
declare global {
	namespace App {
		// interface Error {}
		// interface Locals {}
		// interface PageData {}
		// interface Platform {}
	}

	// Tauri 전역 객체 타입 정의
	interface Window {
		__TAURI__?: {
			invoke: (cmd: string, args?: Record<string, unknown>) => Promise<any>;
			convertFileSrc: (filePath: string, protocol?: string) => string;
			[key: string]: any;
		};
		__TAURI_INTERNALS__?: {
			[key: string]: any;
		};
		__TAURI_METADATA__?: {
			[key: string]: any;
		};
	}

	// 환경 변수 타입 정의
	interface ImportMetaEnv {
		// API 설정
		readonly VITE_NODE_ENV: string;
		readonly VITE_HOME_URL: string;
		readonly VITE_API_ENDPOINT: string;
		readonly VITE_SSE_ENDPOINT: string;
		readonly VITE_SSE_NOTIFICATION_ENDPOINT: string;
		readonly VITE_DELETE_URL_STRING: string;
		readonly VITE_SESSION_NAME: string;

		// Pusher <PERSON>ams 설정
		readonly VITE_PUSHER_BEAMS_INSTANCE_ID: string;
		readonly VITE_PUSHER_APP_KEY: string;
		readonly VITE_PUSHER_HOST: string;
		readonly VITE_PUSHER_PORT: string;
		readonly VITE_PUSHER_SCHEME: string;
		readonly VITE_PUSHER_APP_CLUSTER: string;

		// Firebase 설정
		readonly VITE_FIREBASE_PROJECT_ID: string;
		readonly VITE_FIREBASE_SENDER_ID: string;
		readonly VITE_FIREBASE_API_KEY: string;
	}

	interface ImportMeta {
		readonly env: ImportMetaEnv;
	}

	interface User {
		id: number;
		company_id: string;
		role: string;
		username: string;
		name: string;
		email: string;
		cellphone: string;
		status: number;
		login_at: Date | null;
		login_ip: string | null;
		login_os: string | null;
		created_at: Date;
		updated_at: Date;
	}

	// drag and drop
	type Item = import('svelte-dnd-action').Item;
	type DndEvent<ItemType = Item> = import('svelte-dnd-action').DndEvent<ItemType>;
	namespace svelteHTML {
		interface HTMLAttributes<T> {
			'on:consider'?: (
				event: CustomEvent<DndEvent<ItemType>> & { target: EventTarget & T }
			) => void;
			'on:finalize'?: (
				event: CustomEvent<DndEvent<ItemType>> & { target: EventTarget & T }
			) => void;
		}
	}
}

export {};
