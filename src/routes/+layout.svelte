<script lang="ts">
    import '../app.pcss';
    import UpdateModal from '$lib/components/UpdateModal.svelte';
    import PageRestorer from '$lib/components/PageRestorer.svelte';

    interface Props {
        children?: import('svelte').Snippet;
    }

    let { children }: Props = $props();
</script>

{@render children?.()}

<!-- 통합 업데이트 관리 컴포넌트 (자동 + 수동 모달) -->
<UpdateModal />

<!-- 페이지 복원 컴포넌트 -->
<PageRestorer />