<script lang="ts">
	import { onDestroy, onMount } from 'svelte';
	import { page } from '$app/state';

	import { Toasts } from 'svoast';

	import { executeAsk, executeMessage } from '$lib/Functions';
	import { getUser, type User } from '$lib/User';
	import { redirectToLogin } from '$lib/utils/authHelpers';
	import { goto } from '$app/navigation';
	import { tokenAutoRefresh } from '$lib/utils/tokenAutoRefresh';
	import { getPushServiceManager } from '$lib/services/pushServiceManager';
	import { notifications } from '$lib/stores/notificationRunes.svelte';
	import type {
		NotificationData,
		LegacyPusherMessage,
		NotificationPriority
	} from '$lib/types/notification';
	import { NotificationUtils } from '$lib/types/notification';

	interface Props {
		children?: import('svelte').Snippet;
	}

	let { children }: Props = $props();

	let user: boolean | User = false;

	/**
	 * 기존 메시지 처리 로직과 호환되는 알림 핸들러
	 */
	async function messageHandler(notification: NotificationData) {
		try {
			// 기존 Pusher 메시지 포맷과 호환성 유지
			const data = {
				success: notification.success ?? true,
				message: notification.message || notification.content
			};

			if (data.success) {
				let msg = data.message.replace(/\\n/g, '\n').replace(/<br>/g, '\n');
				await executeMessage(msg);
			}

			// 알림을 읽음으로 표시
			await notifications.markAsRead(notification.id);
		} catch (error) {
			console.error('알림 처리 중 오류:', error);
			await executeMessage('알림 처리 중 오류가 발생했습니다.', 'warning');
		}
	}

	/**
	 * 기존 Pusher 메시지 포맷을 새로운 알림 포맷으로 변환
	 */
	function convertLegacyMessage(legacyMessage: LegacyPusherMessage): NotificationData {
		// 우선순위 결정 (기존 로직 기반)
		let priority: NotificationPriority = 'normal';

		// 관리자 메시지인 경우 높은 우선순위 설정
		if (legacyMessage.message?.includes('[관리자]')) {
			priority = 'high';
		}

		// CreateNotificationData로 변환
		const createData = NotificationUtils.convertFromPusherMessage(legacyMessage, priority);

		// 완전한 NotificationData로 변환
		return NotificationUtils.createNotification(createData);
	}

	/**
	 * 알림 수신 콜백 (새로운 푸시 알림)
	 */
	function onNotificationReceived(notification: NotificationData) {
		console.log('새 알림 수신:', notification);

		// 알림 스토어에 추가
		notifications.addNotification(notification);

		// 기존 메시지 처리 로직 실행
		messageHandler(notification);
	}

	/**
	 * 기존 Pusher 메시지 수신 콜백 (호환성 유지)
	 */
	function onLegacyMessageReceived(legacyMessage: LegacyPusherMessage) {
		console.log('기존 형식 메시지 수신:', legacyMessage);

		// 기존 메시지를 새로운 알림 포맷으로 변환
		const notification = convertLegacyMessage(legacyMessage);

		// 새로운 알림 처리 로직으로 전달
		onNotificationReceived(notification);
	}

	onMount(async () => {
		try {
			user = getUser();

			// 개발 환경에서 디버거들 초기화
			if (import.meta.env.DEV) {
				import('$lib/utils/tokenDebugger');
				import('$lib/utils/quickTokenCheck');

				// 페이지 복원 디버그 함수 등록
				import('$lib/utils/debugPageRestore').then(module => {
					module.registerPageRestoreDebugFunctions();
				});

				// Pusher Beams 디버그 함수 등록
				import('$lib/utils/debugPusherBeams').then(module => {
					module.registerPusherBeamsDebugFunctions();
				});
			}

			// 알림 스토어 초기화
			await notifications.initialize();

			// 사용자 정보 설정
			if (user && typeof user === 'object') {
				const userId = user.id?.toString() || user.username || 'unknown';
				notifications.setUser(userId);
			}

			// Push Service Manager 초기화
			const pushServiceManager = getPushServiceManager();
			await pushServiceManager.initialize();

			// 권한 요청
			const permissionStatus = await pushServiceManager.requestPermission();
			if (permissionStatus === 'granted') {
				console.log('푸시 알림 권한 승인됨');
			} else {
				console.warn('푸시 알림 권한 거부됨:', permissionStatus);
			}

			// 알림 수신 콜백 등록
			pushServiceManager.onNotificationReceived(onNotificationReceived);

			// 디바이스 등록 및 사용자 로그인 처리
			if (user && typeof user === 'object') {
				console.log('사용자 로그인 처리 시작');
				await pushServiceManager.onUserLogin();

				const serviceStatus = await pushServiceManager.getServiceStatus();
				console.log('푸시 서비스 상태:', serviceStatus);
			} else {
				console.log('사용자 정보 없음 - 디바이스만 등록');
				await pushServiceManager.registerDevice();
			}

			// 개발 환경에서 테스트 함수 등록
			if (import.meta.env.DEV && typeof window !== 'undefined') {
				(window as any).testLegacyMessage = (message: string) => {
					const legacyMessage: LegacyPusherMessage = {
						success: true,
						message
					};
					onLegacyMessageReceived(legacyMessage);
				};

				(window as any).testHighPriorityMessage = (message: string) => {
					const notification: NotificationData = {
						id: `test_high_${Date.now()}`,
						content: message,
						expire_day: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString().split('T')[0],
						created_at: new Date().toISOString(),
						read: false,
						success: true,
						priority: 'high'
					};
					onNotificationReceived(notification);
				};

				(window as any).testPushServiceStatus = async () => {
					const status = await pushServiceManager.getServiceStatus();
					console.log('푸시 서비스 상태:', status);
					return status;
				};

				console.log('개발 환경 테스트 함수 등록 완료:');
				console.log('- window.testLegacyMessage(message)');
				console.log('- window.testHighPriorityMessage(message)');
				console.log('- window.testPushServiceStatus()');
			}

			console.log('Push Notification Service 초기화 완료');
		} catch (error) {
			console.error('Push Notification Service 초기화 실패:', error);
			await executeMessage('푸시 알림 서비스 초기화에 실패했습니다.', 'warning');

			// 사용자 인증 실패 시 로그인 페이지로 리다이렉트
			if (error && typeof error === 'object' && 'message' in error) {
				if ((error as any).message.includes('회원정보')) {
					await redirectToLogin(page.url.pathname);
				}
			}
		}
	});

	onDestroy(() => {
		console.log('레이아웃 정리 중...');

		// 알림 수신 콜백 제거
		const pushServiceManager = getPushServiceManager();
		pushServiceManager.offNotificationReceived(onNotificationReceived);

		// Push Service Manager 정리
		pushServiceManager.cleanup();

		// 알림 스토어 정리
		notifications.destroy();
	});
</script>

<Toasts position="bottom-right" />

<div id="container" use:tokenAutoRefresh={{ warningMinutes: 5 }}>
	{@render children?.()}
</div>
