import type { SvelteComponent } from 'svelte';

export interface PasswordToggleButtonProps {
	/** Controls the visibility state of the password */
	isVisible: boolean;
	/** Callback function to handle toggle events */
	onToggle: () => void;
	/** Allows customization of the padding class (defaults to "pr-4") */
	paddingClass?: string;
	/** Additional CSS classes for the button */
	class?: string;
	/** Tooltip text for accessibility (optional) */
	title?: string;
}

declare class PasswordToggleButton extends SvelteComponent<PasswordToggleButtonProps> {}

export default PasswordToggleButton;
