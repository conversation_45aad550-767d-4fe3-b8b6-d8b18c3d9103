<script lang="ts">
	import Icon from 'svelte-awesome';
	import { faEye } from '@fortawesome/free-solid-svg-icons/faEye';
	import { faEyeSlash } from '@fortawesome/free-solid-svg-icons/faEyeSlash';

	interface Props {
		/** Controls the visibility state of the password */
		isVisible: boolean;
		/** Callback function to handle toggle events */
		onToggle: () => void;
		/** Allows customization of the padding class (defaults to "pr-4") */
		paddingClass?: string;
		/** Additional CSS classes for the button */
		class?: string;
		/** Tooltip text for accessibility (optional) */
		title?: string;
	}

	let { 
		isVisible, 
		onToggle, 
		paddingClass = 'pr-4',
		class: additionalClasses = '',
		title = isVisible ? '비밀번호 숨기기' : '비밀번호 보기'
	}: Props = $props();

	// Reactive title that updates based on visibility state
	const buttonTitle = $derived(isVisible ? '비밀번호 숨기기' : '비밀번호 보기');
</script>

<button
	class="absolute inset-y-0 right-0 {paddingClass} flex items-center text-gray-400 hover:text-gray-600 transition-colors {additionalClasses}"
	onclick={onToggle}
	type="button"
	title={title || buttonTitle}
	aria-label={title || buttonTitle}
>
	{#if isVisible}
		<Icon data={faEyeSlash} />
	{:else}
		<Icon data={faEye} />
	{/if}
</button>
