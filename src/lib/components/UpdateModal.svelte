<script lang="ts">
	import { onMount, onDestroy } from 'svelte';
	import { browser } from '$app/environment';
	import { page } from '$app/state';
	import {
		getCurrentVersion,
		checkForUpdates,
		updateState,
		updateModalState,
		setupUpdateListeners,
		acceptUpdate,
		deferUpdate,
		pageRestore,
		hideUpdateModal
	} from '$lib/services/updateService';

	// Props 제거 - 이제 전역 상태로 관리
	let currentVersion = $state('');
	let isCheckingUpdate = $state(false);
	let lastCheckTime = $state<Date | null>(null);
	let checkResult = $state<string>('');
	let cleanupListeners: (() => Promise<void>) | null = null;

	// 업데이트 수락 처리
	function handleAcceptUpdate() {
		// 현재 페이지 저장 (업데이트 후 복원용)
		if (browser) {
			const currentUrl = page.url.pathname + page.url.search;
			if (import.meta.env.VITE_NODE_ENV === 'development') {
				console.log('💾 UpdateModal: 업데이트 전 페이지 저장:', currentUrl);
			}
			pageRestore.save(currentUrl);
		}
		acceptUpdate();
	}

	// 업데이트 연기 처리
	function handleDeferUpdate() {
		deferUpdate();
	}

	// 수동 모달 닫기 처리
	function handleCloseManualModal() {
		hideUpdateModal();
	}

	onMount(async () => {
		try {
			currentVersion = await getCurrentVersion();
		} catch (error) {
			console.error('버전 정보 가져오기 실패:', error);
			currentVersion = 'Unknown';
		}

		// 항상 업데이트 리스너 설정 (단일 인스턴스이므로)
		if (browser) {
			cleanupListeners = await setupUpdateListeners();
		}
	});

	onDestroy(async () => {
		if (cleanupListeners) {
			await cleanupListeners();
		}
	});

	async function handleCheckUpdate() {
		isCheckingUpdate = true;
		checkResult = '';

		try {
			const result = await checkForUpdates();
			checkResult = result;
			lastCheckTime = new Date();
		} catch (error) {
			console.error('업데이트 확인 실패:', error);
			checkResult = '업데이트 확인 중 오류가 발생했습니다.';
		} finally {
			isCheckingUpdate = false;
		}
	}

	function handleBackdropClick(event: MouseEvent) {
		if (event.target === event.currentTarget) {
			handleCloseManualModal();
		}
	}

	function formatTime(date: Date): string {
		return date.toLocaleTimeString('ko-KR', {
			hour: '2-digit',
			minute: '2-digit',
			second: '2-digit'
		});
	}
</script>

<!-- 자동 업데이트 알림 모달 -->
{#if $updateState.isAvailable && $updateState.userChoice === 'pending'}
	<div class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
		<div class="bg-base-100 rounded-lg shadow-xl p-6 max-w-md w-full mx-4">
			<div class="flex items-center mb-4">
				<div class="flex-shrink-0">
					<svg
						class="w-8 h-8 text-info"
						fill="none"
						stroke="currentColor"
						viewBox="0 0 24 24"
					>
						<path
							stroke-linecap="round"
							stroke-linejoin="round"
							stroke-width="2"
							d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"
						/>
					</svg>
				</div>
				<div class="ml-3">
					<h3 class="text-lg font-medium text-base-content">업데이트 사용 가능</h3>
					<p class="text-sm text-base-content/70">
						새로운 버전 {$updateState.version}이 사용 가능합니다.
					</p>
				</div>
			</div>

			<div class="mb-4">
				<p class="text-sm text-base-content/70">
					지금 업데이트하시겠습니까? 업데이트 후 현재 작업 중인 페이지로 자동으로 돌아갑니다.
				</p>
			</div>

			<div class="flex justify-end space-x-3">
				<button class="btn btn-ghost btn-sm" onclick={handleDeferUpdate}>
					나중에
				</button>
				<button class="btn btn-primary btn-sm" onclick={handleAcceptUpdate}>
					지금 업데이트
				</button>
			</div>
		</div>
	</div>
{/if}

<!-- 업데이트 진행 상태 표시 -->
{#if ($updateState.isDownloading || $updateState.isInstalling) && $updateState.userChoice === 'accepted'}
	<div class="fixed top-4 right-4 z-40">
		<div class="bg-base-100 rounded-lg shadow-lg p-4 border border-base-300 min-w-80">
			<div class="flex items-center space-x-3">
				<div class="loading loading-spinner loading-sm"></div>
				<div class="flex-1">
					{#if $updateState.isDownloading}
						<div class="text-sm font-medium">업데이트 다운로드 중...</div>
						<div class="flex justify-between text-xs text-base-content/70 mb-1">
							<span>진행률</span>
							<span>{$updateState.downloadProgress}%</span>
						</div>
						<div class="w-full bg-base-200 rounded-full h-2">
							<div
								class="bg-primary h-2 rounded-full transition-all duration-300"
								style="width: {$updateState.downloadProgress}%"
							></div>
						</div>
					{:else if $updateState.isInstalling}
						<div class="text-sm font-medium">업데이트 설치 중...</div>
						<div class="text-xs text-base-content/70">잠시만 기다려주세요.</div>
					{/if}
				</div>
			</div>
		</div>
	</div>
{/if}

<!-- 수동 업데이트 모달 -->
{#if $updateModalState.showManualModal}
<!-- svelte-ignore a11y_click_events_have_key_events, a11y_no_static_element_interactions -->
<!-- A11y: Elements with the 'dialog' interactive role must have a tabindex value. -->
<div
	class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50"
	role="dialog"
	aria-modal="true"
	aria-labelledby="update-modal-title"
	tabindex="-1"
	onclick={handleBackdropClick}
	onkeydown={(e) => e.key === 'Escape' && handleCloseManualModal()}
>
	<!-- 모달 컨텐츠 -->
	<div class="bg-base-100 rounded-lg shadow-xl max-w-md w-full mx-4 max-h-[90vh] overflow-y-auto">
		<!-- 모달 헤더 -->
		<div class="flex items-center justify-between p-6 border-b border-base-200">
			<h2 id="update-modal-title" class="text-xl font-semibold text-base-content flex items-center">
				<svg class="w-6 h-6 mr-2 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"/>
				</svg>
				앱 업데이트
			</h2>
			<button
				class="btn btn-ghost btn-sm btn-circle"
				aria-label="모달 닫기"
				onclick={onclose}
			>
				<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
				</svg>
			</button>
		</div>

		<!-- 모달 바디 -->
		<div class="p-6 space-y-6">
			<!-- 현재 버전 정보 -->
			<div class="bg-base-200 rounded-lg p-4">
				<h3 class="font-medium text-base-content mb-2">현재 버전</h3>
				<div class="flex items-center justify-between">
					<span class="text-lg font-mono text-primary">v{currentVersion}</span>
					<span class="badge badge-outline badge-sm">설치됨</span>
				</div>
			</div>

			<!-- 업데이트 상태 -->
			{#if $updateState.isAvailable}
				<div class="bg-success bg-opacity-10 border border-success rounded-lg p-4">
					<h3 class="font-medium text-success mb-2 flex items-center">
						<svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"/>
						</svg>
						새 버전 사용 가능
					</h3>
					<div class="flex items-center justify-between">
						<span class="text-lg font-mono text-success">v{$updateState.version}</span>
						<span class="badge badge-success badge-sm">다운로드 가능</span>
					</div>

					{#if $updateState.userChoice === 'deferred'}
						<div class="mt-3">
							<p class="text-sm text-base-content/70 mb-2">
								업데이트가 연기되었습니다. 언제든지 설치할 수 있습니다.
							</p>
							<div class="flex justify-end space-x-2 mt-3">
								<button class="btn btn-primary btn-sm" onclick={handleAcceptUpdate}>
									지금 설치
								</button>
							</div>
						</div>
					{/if}
				</div>
			{/if}

			<!-- 업데이트 확인 -->
			<div class="space-y-3">
				<button
					class="btn btn-primary w-full"
					class:loading={isCheckingUpdate}
					onclick={handleCheckUpdate}
					disabled={isCheckingUpdate}
				>
					{#if isCheckingUpdate}
						업데이트 확인 중...
					{:else}
						업데이트 확인
					{/if}
				</button>

				{#if checkResult}
					<div class="text-sm text-center">
						<p class="text-base-content/70">{checkResult}</p>
						{#if lastCheckTime}
							<p class="text-xs text-base-content/50 mt-1">
								마지막 확인: {formatTime(lastCheckTime)}
							</p>
						{/if}
					</div>
				{/if}
			</div>

			<!-- 업데이트 진행 상태 -->
			{#if $updateState.isDownloading || $updateState.isInstalling}
				<div class="bg-info bg-opacity-10 border border-info rounded-lg p-4">
					<div class="flex items-center space-x-3">
						<div class="loading loading-spinner loading-sm text-info"></div>
						<div class="flex-1">
							{#if $updateState.isDownloading}
								<h3 class="font-medium text-info">업데이트 다운로드 중...</h3>
								<div class="flex justify-between text-xs text-base-content/70 mb-2">
									<span>진행률</span>
									<span>{$updateState.downloadProgress}%</span>
								</div>
								<div class="w-full bg-base-200 rounded-full h-2">
									<div
										class="bg-info h-2 rounded-full transition-all duration-300"
										style="width: {$updateState.downloadProgress}%"
									></div>
								</div>
							{:else if $updateState.isInstalling}
								<h3 class="font-medium text-info">업데이트 설치 중...</h3>
								<p class="text-xs text-base-content/70">잠시만 기다려주세요.</p>
							{/if}
						</div>
					</div>
				</div>
			{/if}

			<!-- 도움말 -->
			<div class="text-xs text-base-content/60 space-y-1">
				<p>• 업데이트는 백그라운드에서 자동으로 확인됩니다.</p>
				<p>• 업데이트 설치 후 앱이 자동으로 재시작됩니다.</p>
				<p>• 작업 중인 내용은 업데이트 후 복원됩니다.</p>
			</div>
		</div>

		<!-- 모달 푸터 -->
		<div class="flex justify-end p-6 border-t border-base-200">
			<button class="btn btn-ghost" onclick={handleCloseManualModal}>
				닫기
			</button>
		</div>
	</div>
</div>
{/if}
