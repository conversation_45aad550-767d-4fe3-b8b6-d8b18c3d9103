<!--
알림 목록 모달 컴포넌트
전체 알림 목록을 표시하고 읽음/삭제 기능을 제공합니다.
개선된 NotificationList 컴포넌트를 사용합니다.

Requirements: 4.3 - 알림 클릭 시 전체 알림 목록 모달 표시, 읽음 처리 및 삭제 기능, 만료일 기반 필터링
Requirements: 2.1.5, 3.3.1, 3.3.2 - 우선순위별 시각적 구분, 삭제 가능한 알림에 삭제 버튼 표시
Requirements: 2.2.3, 3.2.3 - 우선순위별 필터링 UI, 읽음/안읽음 필터링 UI, 카운트 표시
-->

<script lang="ts">
	import { useNotificationList } from '$lib/stores/notificationRunes.svelte';
	import type { NotificationData } from '$lib/types/notification';
	import NotificationList from '$lib/components/Notifications/NotificationList.svelte';

	import { Icon } from 'svelte-awesome';
	import { faTimes } from '@fortawesome/free-solid-svg-icons/faTimes';
	import { faCheck } from '@fortawesome/free-solid-svg-icons/faCheck';
	import { faTrash } from '@fortawesome/free-solid-svg-icons/faTrash';
	import { faBell } from '@fortawesome/free-solid-svg-icons/faBell';
	import { faCheckDouble } from '@fortawesome/free-solid-svg-icons/faCheckDouble';

	export interface Props {
		onclose?: () => void;
	}

	// Props (Svelte 5 방식)
	let { onclose }: Props = $props();

	// 알림 관리 훅
	const {
		userNotifications,
		unreadNotifications,
		isLoading,
		error,
		markAsRead,
		markAllAsRead,
		removeNotification
	} = useNotificationList();

	// 로컬 상태
	let selectedNotifications = $state<string[]>([]);

	// 선택된 알림 수
	const selectedCount = $derived(selectedNotifications.length);

	// 선택 상태 변경 처리
	function handleSelect(ids: string[], selected: boolean) {
		selectedNotifications = ids;
	}

	// 선택된 알림들 읽음 처리
	async function markSelectedAsRead() {
		const promises = selectedNotifications.map((id) => markAsRead(id));
		await Promise.all(promises);
		selectedNotifications = [];
	}

	// 선택된 알림들 삭제
	async function deleteSelected() {
		if (!confirm(`선택된 ${selectedCount}개의 알림을 삭제하시겠습니까?`)) return;

		const promises = selectedNotifications.map((id) => removeNotification(id));
		await Promise.all(promises);
		selectedNotifications = [];
	}

	// 개별 알림 읽음 처리
	async function handleMarkAsRead(id: string) {
		await markAsRead(id);
	}

	// 개별 알림 삭제
	async function handleDelete(id: string) {
		if (!confirm('이 알림을 삭제하시겠습니까?')) return;
		await removeNotification(id);
	}

	// 알림 클릭 처리
	function handleNotificationClick(notification: NotificationData) {
		// 읽지 않은 알림인 경우 자동으로 읽음 처리
		if (!notification.read) {
			handleMarkAsRead(notification.id);
		}
	}

	// 모달 닫기
	function handleClose() {
		onclose?.();
	}

	// 키보드 단축키 처리
	function handleKeydown(event: KeyboardEvent) {
		if (event.key === 'Escape') {
			handleClose();
		} else if (event.key === 'Delete' && selectedCount > 0) {
			event.preventDefault();
			deleteSelected();
		}
	}

	// 전체 알림 삭제 (개발/테스트용)
	async function deleteAllNotifications() {
		if (
			!confirm(
				`모든 알림 ${userNotifications.length}개를 삭제하시겠습니까? 이 작업은 되돌릴 수 없습니다.`
			)
		)
			return;

		const promises = userNotifications.map((n) => removeNotification(n.id));
		await Promise.all(promises);
		selectedNotifications = [];
	}
</script>

<!-- 모달 배경 -->
<!-- svelte-ignore a11y_click_events_have_key_events, a11y_no_static_element_interactions -->
<div class="modal modal-open" onkeydown={handleKeydown}>
	<div class="modal-box w-11/12 max-w-4xl max-h-[80vh] p-0" tabindex="-1">
		<!-- 헤더 -->
		<div class="sticky top-0 bg-base-100 border-b border-base-200 p-4 z-10">
			<div class="flex justify-between items-center">
				<div class="flex items-center gap-2">
					<Icon data={faBell} class="w-5 h-5 text-info" />
					<h3 class="font-bold text-lg">알림 목록</h3>
					{#if unreadNotifications.length > 0}
						<span class="badge badge-error badge-sm">{unreadNotifications.length}</span>
					{/if}
				</div>
				<button class="btn btn-ghost btn-sm" onclick={handleClose}>
					<Icon data={faTimes} class="w-4 h-4" />
				</button>
			</div>

			<!-- 액션 버튼들 -->
			<div class="flex gap-2 mt-3">
				{#if selectedCount > 0}
					<button class="btn btn-sm btn-ghost" onclick={markSelectedAsRead}>
						<Icon data={faCheck} class="w-3 h-3" />
						읽음 처리 ({selectedCount})
					</button>
					<button class="btn btn-sm btn-error btn-outline" onclick={deleteSelected}>
						<Icon data={faTrash} class="w-3 h-3" />
						삭제 ({selectedCount})
					</button>
				{/if}

				{#if unreadNotifications.length > 0}
					<button class="btn btn-sm btn-primary" onclick={markAllAsRead}>
						<Icon data={faCheckDouble} class="w-3 h-3" />
						모두 읽음
					</button>
				{/if}

				{#if import.meta.env.DEV && userNotifications.length > 0}
					<button class="btn btn-sm btn-error btn-outline" onclick={deleteAllNotifications}>
						<Icon data={faTrash} class="w-3 h-3" />
						전체 삭제
					</button>
				{/if}
			</div>
		</div>

		<!-- 알림 목록 -->
		<div class="p-4 overflow-y-auto max-h-[60vh]">
			{#if isLoading}
				<div class="flex justify-center items-center py-8">
					<span class="loading loading-spinner loading-md"></span>
					<span class="ml-2">알림을 불러오는 중...</span>
				</div>
			{:else if error}
				<div class="alert alert-error">
					<span>알림을 불러오는데 실패했습니다: {error}</span>
				</div>
			{:else}
				<NotificationList
					notifications={userNotifications}
					unreadCount={unreadNotifications.length}
					showFilters={true}
					showSearch={true}
					showPagination={true}
					itemsPerPage={10}
					compact={false}
					selectable={true}
					onread={handleMarkAsRead}
					ondelete={handleDelete}
					onselect={handleSelect}
					onclick={handleNotificationClick}
				/>
			{/if}
		</div>

		<!-- 푸터 -->
		<div class="sticky bottom-0 bg-base-100 border-t border-base-200 p-4">
			<div class="flex justify-between items-center text-sm text-base-content/60">
				<div class="flex flex-col gap-1">
					<span>
						총 {userNotifications.length}개 알림
						{#if unreadNotifications.length > 0}
							(읽지 않음: {unreadNotifications.length}개)
						{/if}
					</span>
					{#if selectedCount > 0}
						<span class="text-primary">선택됨: {selectedCount}개</span>
					{/if}
				</div>
				<button class="btn btn-sm" onclick={handleClose}>닫기</button>
			</div>
		</div>
	</div>

	<!-- 모달 배경 클릭으로 닫기 -->
	<div class="modal-backdrop" onclick={handleClose}></div>
</div>
