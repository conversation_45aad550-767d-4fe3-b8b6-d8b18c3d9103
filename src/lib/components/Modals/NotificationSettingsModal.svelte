<!--
알림 설정 모달 컴포넌트
NotificationSettings 컴포넌트를 모달로 감싸서 제공합니다.

Requirements: 1.1.2, 1.2.1, 4.1 - 푸시 알림 권한 상태 표시, 권한 요청 버튼, 서비스 상태 표시
-->

<script lang="ts">
	import NotificationSettings from '$lib/components/Notifications/NotificationSettings.svelte';

	export interface Props {
		onclose?: () => void;
		onpermissionrequest?: () => Promise<void>;
		onservicerestart?: () => Promise<void>;
	}

	// Props (Svelte 5 방식)
	let { onclose, onpermissionrequest, onservicerestart }: Props = $props();

	/**
	 * 모달 닫기
	 */
	function handleClose() {
		onclose?.();
	}

	/**
	 * 키보드 단축키 처리
	 */
	function handleKeydown(event: KeyboardEvent) {
		if (event.key === 'Escape') {
			handleClose();
		}
	}

	/**
	 * 권한 요청 처리
	 */
	async function handlePermissionRequest() {
		if (onpermissionrequest) {
			await onpermissionrequest();
		}
	}

	/**
	 * 서비스 재시작 처리
	 */
	async function handleServiceRestart() {
		if (onservicerestart) {
			await onservicerestart();
		}
	}
</script>

<!-- 모달 배경 -->
<!-- svelte-ignore a11y_click_events_have_key_events, a11y_no_static_element_interactions -->
<div class="modal modal-open" onkeydown={handleKeydown}>
	<div class="modal-box max-w-lg" tabindex="-1">
		<NotificationSettings
			onclose={handleClose}
			onpermissionrequest={handlePermissionRequest}
			onservicerestart={handleServiceRestart}
		/>
	</div>

	<!-- 모달 배경 클릭으로 닫기 -->
	<div class="modal-backdrop" onclick={handleClose}></div>
</div>
