<!--
개별 알림 아이템 컴포넌트
우선순위별 시각적 구분과 삭제 기능을 제공합니다.

Requirements: 2.1.5, 3.3.1, 3.3.2 - 우선순위별 시각적 구분, 삭제 가능한 알림에 삭제 버튼 표시
-->

<script lang="ts">
	import type { NotificationData } from '$lib/types/notification';
	import { NotificationUtils } from '$lib/types/notification';
	import { Icon } from 'svelte-awesome';
	import { faCheck } from '@fortawesome/free-solid-svg-icons/faCheck';
	import { faTrash } from '@fortawesome/free-solid-svg-icons/faTrash';
	import { faExclamationTriangle } from '@fortawesome/free-solid-svg-icons/faExclamationTriangle';
	import { faExclamationCircle } from '@fortawesome/free-solid-svg-icons/faExclamationCircle';
	import { faInfoCircle } from '@fortawesome/free-solid-svg-icons/faInfoCircle';
	import { faChevronDown } from '@fortawesome/free-solid-svg-icons/faChevronDown';

	export interface Props {
		notification: NotificationData;
		showActions?: boolean;
		compact?: boolean;
		selectable?: boolean;
		selected?: boolean;
		searchQuery?: string;
		onread?: (id: string) => void;
		ondelete?: (id: string) => void;
		onselect?: (id: string, selected: boolean) => void;
		onclick?: (notification: NotificationData) => void;
	}

	// Props (Svelte 5 방식)
	let {
		notification,
		showActions = true,
		compact = false,
		selectable = false,
		selected = false,
		searchQuery = '',
		onread,
		ondelete,
		onselect,
		onclick
	}: Props = $props();

	// 로컬 상태
	let isExpanded = $state(false);

	// 파생된 상태들
	const isExpired = $derived(NotificationUtils.isExpired(notification));
	const isDeletable = $derived(notification.deletable !== false);
	const priority = $derived(notification.priority || 'normal');

	/**
	 * 우선순위에 따른 색상 클래스 반환
	 */
	function getPriorityClasses(): string {
		const baseClasses = 'border-l-4 transition-all duration-200';

		switch (priority) {
			case 'urgent':
				return `${baseClasses} border-l-error bg-error/5 hover:bg-error/10`;
			case 'high':
				return `${baseClasses} border-l-warning bg-warning/5 hover:bg-warning/10`;
			case 'low':
				return `${baseClasses} border-l-base-300 bg-base-100/50 hover:bg-base-200/50`;
			default: // normal
				return `${baseClasses} border-l-info bg-info/5 hover:bg-info/10`;
		}
	}

	/**
	 * 우선순위에 따른 아이콘 반환
	 */
	function getPriorityIcon() {
		switch (priority) {
			case 'urgent':
				return { icon: faExclamationTriangle, class: 'text-error' };
			case 'high':
				return { icon: faExclamationCircle, class: 'text-warning' };
			case 'low':
				return { icon: faInfoCircle, class: 'text-base-content/50' };
			default: // normal
				return { icon: faInfoCircle, class: 'text-info' };
		}
	}

	/**
	 * 우선순위 텍스트 반환
	 */
	function getPriorityText(): string {
		switch (priority) {
			case 'urgent':
				return '긴급';
			case 'high':
				return '높음';
			case 'low':
				return '낮음';
			default:
				return '보통';
		}
	}

	/**
	 * 날짜 포맷팅
	 */
	function formatDate(dateString: string): string {
		const date = new Date(dateString);
		return date.toLocaleDateString('ko-KR', {
			year: 'numeric',
			month: 'short',
			day: 'numeric',
			hour: '2-digit',
			minute: '2-digit'
		});
	}

	/**
	 * 검색어 하이라이트
	 */
	function highlightSearchQuery(text: string, query: string): string {
		if (!query.trim()) return text;

		const regex = new RegExp(`(${query.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi');
		return text.replace(
			regex,
			'<mark class="bg-yellow-200 text-yellow-900 px-1 rounded">$1</mark>'
		);
	}

	/**
	 * 읽음 처리
	 */
	function handleMarkAsRead() {
		if (!notification.read) {
			onread?.(notification.id);
		}
	}

	/**
	 * 삭제 처리
	 */
	function handleDelete() {
		if (isDeletable) {
			ondelete?.(notification.id);
		}
	}

	/**
	 * 선택 처리
	 */
	function handleSelect() {
		if (selectable) {
			onselect?.(notification.id, !selected);
		}
	}

	/**
	 * 클릭 처리
	 */
	function handleClick() {
		onclick?.(notification);
	}

	/**
	 * 확장/축소 토글
	 */
	function toggleExpanded() {
		isExpanded = !isExpanded;
	}

	// 우선순위 아이콘 정보
	const priorityIconInfo = $derived(getPriorityIcon());
</script>

<!-- svelte-ignore a11y_no_noninteractive_tabindex -->
<div
	class="card card-compact {getPriorityClasses()}"
	class:opacity-60={isExpired}
	class:bg-base-200={notification.read}
	class:cursor-pointer={!!onclick}
	onclick={onclick ? handleClick : undefined}
	role={onclick ? 'button' : undefined}
	tabindex={onclick ? 0 : undefined}
>
	<div class="card-body">
		<div class="flex items-start gap-3">
			<!-- 선택 체크박스 (선택 가능한 경우) -->
			{#if selectable}
				<input
					type="checkbox"
					class="checkbox checkbox-sm mt-1"
					checked={selected}
					onclick={(e) => {
						e.stopPropagation();
						handleSelect();
					}}
				/>
			{/if}

			<!-- 우선순위 아이콘 -->
			<div class="flex-shrink-0 mt-1">
				<Icon data={priorityIconInfo.icon} class="w-4 h-4 {priorityIconInfo.class}" />
			</div>

			<!-- 알림 내용 -->
			<div class="flex-1 min-w-0">
				<div class="flex items-start justify-between gap-2">
					<!-- 제목과 내용 -->
					<div class="flex-1 min-w-0">
						{#if notification.message && !compact}
							<h4
								class="font-medium text-sm mb-1"
								class:font-semibold={!notification.read}
								class:opacity-70={notification.read}
							>
								{#if searchQuery.trim()}
									{@html highlightSearchQuery(notification.message, searchQuery)}
								{:else}
									{notification.message}
								{/if}
							</h4>
						{/if}

						<p
							class="text-sm leading-relaxed"
							class:font-semibold={!notification.read && !notification.message}
							class:opacity-70={notification.read}
							class:line-clamp-2={compact && !isExpanded}
						>
							{#if searchQuery.trim()}
								{@html highlightSearchQuery(notification.content, searchQuery)}
							{:else}
								{notification.content}
							{/if}
						</p>

						<!-- 확장 버튼 (컴팩트 모드에서 긴 내용인 경우) -->
						{#if compact && notification.content.length > 100}
							<button
								class="btn btn-ghost btn-xs mt-1"
								onclick={(e) => {
									e.stopPropagation();
									toggleExpanded();
								}}
							>
								<Icon
									data={faChevronDown}
									class="w-3 h-3 transition-transform {isExpanded ? 'rotate-180' : ''}"
								/>
								{isExpanded ? '접기' : '더보기'}
							</button>
						{/if}
					</div>

					<!-- 액션 버튼들 -->
					{#if showActions}
						<div class="flex gap-1 flex-shrink-0">
							{#if !notification.read}
								<button
									class="btn btn-ghost btn-xs"
									onclick={(e) => {
										e.stopPropagation();
										handleMarkAsRead();
									}}
									title="읽음 처리"
								>
									<Icon data={faCheck} class="w-3 h-3" />
								</button>
							{/if}

							{#if isDeletable}
								<button
									class="btn btn-ghost btn-xs text-error"
									onclick={(e) => {
										e.stopPropagation();
										handleDelete();
									}}
									title="삭제"
								>
									<Icon data={faTrash} class="w-3 h-3" />
								</button>
							{/if}
						</div>
					{/if}
				</div>

				<!-- 메타 정보 -->
				<div class="flex flex-wrap items-center gap-2 mt-2 text-xs text-base-content/60">
					<span>{formatDate(notification.created_at)}</span>

					{#if priority !== 'normal'}
						<span
							class="badge badge-xs"
							class:badge-error={priority === 'urgent'}
							class:badge-warning={priority === 'high'}
							class:badge-info={priority === 'low'}
						>
							{getPriorityText()}
						</span>
					{/if}

					{#if notification.category}
						<span class="badge badge-ghost badge-xs">{notification.category}</span>
					{/if}

					{#if isExpired}
						<span class="badge badge-ghost badge-xs text-error">만료됨</span>
					{:else}
						<span class="text-success">만료: {notification.expire_day}</span>
					{/if}

					{#if notification.read}
						<span class="badge badge-success badge-xs">읽음</span>
					{:else}
						<span class="badge badge-primary badge-xs">읽지 않음</span>
					{/if}

					{#if !isDeletable}
						<span class="badge badge-ghost badge-xs">삭제 불가</span>
					{/if}
				</div>

				<!-- 이미지 (있는 경우) -->
				{#if notification.image_url && !compact}
					<div class="mt-2">
						<img
							src={notification.image_url}
							alt="알림 이미지"
							class="max-w-full h-auto rounded-lg max-h-32 object-cover"
							loading="lazy"
						/>
					</div>
				{/if}
			</div>
		</div>
	</div>
</div>

<style>
	.line-clamp-2 {
		display: -webkit-box;
		-webkit-line-clamp: 2;
		-webkit-box-orient: vertical;
		overflow: hidden;
	}
</style>
