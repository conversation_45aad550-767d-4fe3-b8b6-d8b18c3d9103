<!--
알림 설정 UI 컴포넌트
푸시 알림 권한 상태 표시, 권한 요청, 서비스 상태 표시 기능을 제공합니다.

Requirements: 1.1.2, 1.2.1, 4.1 - 푸시 알림 권한 상태 표시, 권한 요청 버튼, 서비스 상태 표시
-->

<script lang="ts">
	import { getCurrentPlatform, isDesktop, isAndroid, isTauri } from '$lib/services/platformService';
	import { Icon } from 'svelte-awesome';
	import { faBell } from '@fortawesome/free-solid-svg-icons/faBell';
	import { faCheck } from '@fortawesome/free-solid-svg-icons/faCheck';
	import { faTimes } from '@fortawesome/free-solid-svg-icons/faTimes';
	import { faExclamationTriangle } from '@fortawesome/free-solid-svg-icons/faExclamationTriangle';
	import { faDesktop } from '@fortawesome/free-solid-svg-icons/faDesktop';
	import { faMobile } from '@fortawesome/free-solid-svg-icons/faMobile';
	import { faGlobe } from '@fortawesome/free-solid-svg-icons/faGlobe';
	import { faRefresh } from '@fortawesome/free-solid-svg-icons/faRefresh';
	import { faCog } from '@fortawesome/free-solid-svg-icons/faCog';

	export interface Props {
		onpermissionrequest?: () => Promise<void>;
		onservicerestart?: () => Promise<void>;
		onclose?: () => void;
	}

	// Props (Svelte 5 방식)
	let { onpermissionrequest, onservicerestart, onclose }: Props = $props();

	// 로컬 상태
	let isLoading = $state(false);
	let permissionStatus = $state<'default' | 'granted' | 'denied'>('default');
	let serviceStatus = $state<'initializing' | 'ready' | 'error' | 'disabled'>('initializing');
	let platformInfo = $state({
		platform: 'unknown',
		isDesktop: false,
		isAndroid: false,
		isTauri: false
	});

	// 초기화
	$effect(() => {
		initializeSettings();
	});

	/**
	 * 설정 초기화
	 */
	async function initializeSettings() {
		try {
			isLoading = true;

			// 플랫폼 정보 수집
			platformInfo = {
				platform: getCurrentPlatform(),
				isDesktop: isDesktop(),
				isAndroid: isAndroid(),
				isTauri: isTauri()
			};

			// 권한 상태 확인
			await checkPermissionStatus();

			// 서비스 상태 확인
			await checkServiceStatus();
		} catch (error) {
			console.error('알림 설정 초기화 실패:', error);
			serviceStatus = 'error';
		} finally {
			isLoading = false;
		}
	}

	/**
	 * 권한 상태 확인
	 */
	async function checkPermissionStatus() {
		if (typeof window === 'undefined') return;

		try {
			if ('Notification' in window) {
				// 웹 브라우저 환경
				permissionStatus = Notification.permission as 'default' | 'granted' | 'denied';
			} else if (platformInfo.isAndroid && platformInfo.isTauri) {
				// 안드로이드 Tauri 환경 - 임시로 granted로 설정
				permissionStatus = 'granted';
			} else {
				permissionStatus = 'default';
			}
		} catch (error) {
			console.error('권한 상태 확인 실패:', error);
			permissionStatus = 'default';
		}
	}

	/**
	 * 서비스 상태 확인
	 */
	async function checkServiceStatus() {
		try {
			// 실제 푸시 서비스 상태를 확인하는 로직
			// 현재는 플랫폼에 따라 기본 상태 설정
			if (platformInfo.isDesktop) {
				// 데스크탑: 브라우저 지원 여부 확인
				if ('serviceWorker' in navigator && 'PushManager' in window) {
					serviceStatus = 'ready';
				} else {
					serviceStatus = 'error';
				}
			} else if (platformInfo.isAndroid) {
				// 안드로이드: FCM 지원 가정
				serviceStatus = 'ready';
			} else {
				serviceStatus = 'disabled';
			}
		} catch (error) {
			console.error('서비스 상태 확인 실패:', error);
			serviceStatus = 'error';
		}
	}

	/**
	 * 권한 요청
	 */
	async function handlePermissionRequest() {
		if (!onpermissionrequest) return;

		try {
			isLoading = true;
			await onpermissionrequest();
			await checkPermissionStatus();
		} catch (error) {
			console.error('권한 요청 실패:', error);
		} finally {
			isLoading = false;
		}
	}

	/**
	 * 서비스 재시작
	 */
	async function handleServiceRestart() {
		if (!onservicerestart) return;

		try {
			isLoading = true;
			serviceStatus = 'initializing';
			await onservicerestart();
			await checkServiceStatus();
		} catch (error) {
			console.error('서비스 재시작 실패:', error);
			serviceStatus = 'error';
		} finally {
			isLoading = false;
		}
	}

	/**
	 * 설정 새로고침
	 */
	async function handleRefresh() {
		await initializeSettings();
	}

	/**
	 * 플랫폼 아이콘 반환
	 */
	function getPlatformIcon() {
		if (platformInfo.isDesktop) {
			return { icon: faDesktop, label: '데스크탑' };
		} else if (platformInfo.isAndroid) {
			return { icon: faMobile, label: '안드로이드' };
		} else {
			return { icon: faGlobe, label: '웹' };
		}
	}

	/**
	 * 권한 상태 정보 반환
	 */
	function getPermissionInfo() {
		switch (permissionStatus) {
			case 'granted':
				return {
					icon: faCheck,
					class: 'text-success',
					label: '허용됨',
					description: '알림을 받을 수 있습니다.'
				};
			case 'denied':
				return {
					icon: faTimes,
					class: 'text-error',
					label: '거부됨',
					description: '브라우저 설정에서 알림을 허용해주세요.'
				};
			default:
				return {
					icon: faExclamationTriangle,
					class: 'text-warning',
					label: '미설정',
					description: '알림 권한을 요청해주세요.'
				};
		}
	}

	/**
	 * 서비스 상태 정보 반환
	 */
	function getServiceInfo() {
		switch (serviceStatus) {
			case 'ready':
				return {
					icon: faCheck,
					class: 'text-success',
					label: '정상',
					description: '푸시 알림 서비스가 정상 작동 중입니다.'
				};
			case 'error':
				return {
					icon: faTimes,
					class: 'text-error',
					label: '오류',
					description: '서비스에 문제가 발생했습니다.'
				};
			case 'disabled':
				return {
					icon: faExclamationTriangle,
					class: 'text-warning',
					label: '비활성화',
					description: '이 플랫폼에서는 지원되지 않습니다.'
				};
			default:
				return {
					icon: faCog,
					class: 'text-info',
					label: '초기화 중',
					description: '서비스를 초기화하고 있습니다.'
				};
		}
	}

	// 파생된 상태들
	const platformIcon = $derived(getPlatformIcon());
	const permissionInfo = $derived(getPermissionInfo());
	const serviceInfo = $derived(getServiceInfo());
	const canRequestPermission = $derived(permissionStatus !== 'granted' && !isLoading);
	const canRestartService = $derived(serviceStatus === 'error' && !isLoading);
</script>

<div class="notification-settings">
	<div class="card bg-base-100 shadow-lg border border-base-200">
		<div class="card-body">
			<!-- 헤더 -->
			<div class="flex items-center justify-between mb-4">
				<div class="flex items-center gap-2">
					<Icon data={faBell} class="w-5 h-5 text-info" />
					<h3 class="font-bold text-lg">알림 설정</h3>
				</div>
				{#if onclose}
					<button class="btn btn-ghost btn-sm" onclick={onclose}>
						<Icon data={faTimes} class="w-4 h-4" />
					</button>
				{/if}
			</div>

			{#if isLoading}
				<div class="flex justify-center items-center py-8">
					<span class="loading loading-spinner loading-md"></span>
					<span class="ml-2">설정을 불러오는 중...</span>
				</div>
			{:else}
				<!-- 플랫폼 정보 -->
				<div class="mb-6">
					<h4 class="font-semibold mb-2">플랫폼 정보</h4>
					<div class="flex items-center gap-3 p-3 bg-base-200 rounded-lg">
						<Icon data={platformIcon.icon} class="w-5 h-5 text-info" />
						<div>
							<div class="font-medium">{platformIcon.label}</div>
							<div class="text-sm text-base-content/60">
								{platformInfo.platform}
								{#if platformInfo.isTauri}
									(Tauri)
								{/if}
							</div>
						</div>
					</div>
				</div>

				<!-- 권한 상태 -->
				<div class="mb-6">
					<h4 class="font-semibold mb-2">알림 권한</h4>
					<div class="flex items-center justify-between p-3 bg-base-200 rounded-lg">
						<div class="flex items-center gap-3">
							<Icon data={permissionInfo.icon} class="w-5 h-5 {permissionInfo.class}" />
							<div>
								<div class="font-medium">{permissionInfo.label}</div>
								<div class="text-sm text-base-content/60">{permissionInfo.description}</div>
							</div>
						</div>
						{#if canRequestPermission}
							<button class="btn btn-sm btn-primary" onclick={handlePermissionRequest}>
								권한 요청
							</button>
						{/if}
					</div>
				</div>

				<!-- 서비스 상태 -->
				<div class="mb-6">
					<h4 class="font-semibold mb-2">서비스 상태</h4>
					<div class="flex items-center justify-between p-3 bg-base-200 rounded-lg">
						<div class="flex items-center gap-3">
							<Icon data={serviceInfo.icon} class="w-5 h-5 {serviceInfo.class}" />
							<div>
								<div class="font-medium">{serviceInfo.label}</div>
								<div class="text-sm text-base-content/60">{serviceInfo.description}</div>
							</div>
						</div>
						{#if canRestartService}
							<button class="btn btn-sm btn-warning" onclick={handleServiceRestart}>
								재시작
							</button>
						{/if}
					</div>
				</div>

				<!-- 기술 정보 (개발 모드에서만) -->
				{#if import.meta.env.DEV}
					<div class="mb-6">
						<h4 class="font-semibold mb-2">기술 정보</h4>
						<div class="p-3 bg-base-200 rounded-lg">
							<div class="grid grid-cols-2 gap-2 text-sm">
								<div class="flex justify-between">
									<span>Service Worker:</span>
									<span class="text-base-content/60">
										{'serviceWorker' in navigator ? '지원됨' : '미지원'}
									</span>
								</div>
								<div class="flex justify-between">
									<span>Push Manager:</span>
									<span class="text-base-content/60">
										{'PushManager' in window ? '지원됨' : '미지원'}
									</span>
								</div>
								<div class="flex justify-between">
									<span>Notification API:</span>
									<span class="text-base-content/60">
										{'Notification' in window ? '지원됨' : '미지원'}
									</span>
								</div>
								<div class="flex justify-between">
									<span>Tauri:</span>
									<span class="text-base-content/60">
										{platformInfo.isTauri ? '활성화' : '비활성화'}
									</span>
								</div>
							</div>
						</div>
					</div>
				{/if}

				<!-- 액션 버튼들 -->
				<div class="flex gap-2">
					<button class="btn btn-outline flex-1" onclick={handleRefresh}>
						<Icon data={faRefresh} class="w-4 h-4" />
						새로고침
					</button>
					{#if onclose}
						<button class="btn btn-primary" onclick={onclose}> 확인 </button>
					{/if}
				</div>

				<!-- 도움말 -->
				<div class="mt-4 p-3 bg-info/10 rounded-lg">
					<div class="text-sm text-info-content">
						<strong>도움말:</strong>
						{#if permissionStatus === 'denied'}
							브라우저 주소창 옆의 알림 아이콘을 클릭하여 권한을 허용해주세요.
						{:else if permissionStatus === 'default'}
							알림을 받으려면 권한을 허용해주세요.
						{:else if serviceStatus === 'error'}
							서비스에 문제가 있습니다. 페이지를 새로고침하거나 재시작 버튼을 눌러주세요.
						{:else}
							모든 설정이 정상입니다. 알림을 받을 수 있습니다.
						{/if}
					</div>
				</div>
			{/if}
		</div>
	</div>
</div>

<style>
	.notification-settings {
		min-width: 400px;
		max-width: 500px;
	}
</style>
