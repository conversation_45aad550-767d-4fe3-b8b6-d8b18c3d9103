<!--
알림 카운터 컴포넌트
읽지 않은 알림 수를 표시하고 우선순위별 카운트를 제공합니다.

Requirements: 2.2.3, 3.2.3 - 카운트 표시, 우선순위별 필터링 UI
-->

<script lang="ts">
	import type { NotificationPriority } from '$lib/types/notification';
	import { useNotificationCounter } from '$lib/stores/notificationRunes.svelte';
	import { Icon } from 'svelte-awesome';
	import { faBell } from '@fortawesome/free-solid-svg-icons/faBell';
	import { faExclamationTriangle } from '@fortawesome/free-solid-svg-icons/faExclamationTriangle';
	import { faExclamationCircle } from '@fortawesome/free-solid-svg-icons/faExclamationCircle';
	import { faInfoCircle } from '@fortawesome/free-solid-svg-icons/faInfoCircle';

	export interface Props {
		showDetails?: boolean;
		compact?: boolean;
		onclick?: () => void;
	}

	// Props (Svelte 5 방식)
	let { showDetails = false, compact = false, onclick }: Props = $props();

	// 알림 카운터 훅
	const { unreadCount, hasUnread, stats } = useNotificationCounter();

	/**
	 * 우선순위에 따른 아이콘 반환
	 */
	function getPriorityIcon(priority: NotificationPriority) {
		switch (priority) {
			case 'urgent':
				return { icon: faExclamationTriangle, class: 'text-error' };
			case 'high':
				return { icon: faExclamationCircle, class: 'text-warning' };
			case 'low':
				return { icon: faInfoCircle, class: 'text-base-content/50' };
			default: // normal
				return { icon: faInfoCircle, class: 'text-info' };
		}
	}

	/**
	 * 우선순위 텍스트 반환
	 */
	function getPriorityText(priority: NotificationPriority): string {
		switch (priority) {
			case 'urgent':
				return '긴급';
			case 'high':
				return '높음';
			case 'low':
				return '낮음';
			default:
				return '보통';
		}
	}

	/**
	 * 클릭 처리
	 */
	function handleClick() {
		onclick?.();
	}
</script>

{#if hasUnread}
	<!-- svelte-ignore a11y_no_noninteractive_tabindex -->
	<div
		class="notification-counter"
		class:cursor-pointer={!!onclick}
		onclick={onclick ? handleClick : undefined}
		role={onclick ? 'button' : undefined}
		tabindex={onclick ? 0 : undefined}
	>
		{#if compact}
			<!-- 컴팩트 모드: 간단한 카운터만 -->
			<div class="flex items-center gap-1">
				<Icon data={faBell} class="w-4 h-4 text-info" />
				<span class="badge badge-error badge-sm">{unreadCount}</span>
			</div>
		{:else}
			<!-- 일반 모드: 상세 정보 포함 -->
			<div class="card card-compact bg-base-100 shadow-sm border border-base-200">
				<div class="card-body">
					<div class="flex items-center justify-between">
						<div class="flex items-center gap-2">
							<Icon data={faBell} class="w-5 h-5 text-info" />
							<span class="font-medium">알림</span>
						</div>
						<span class="badge badge-error">{unreadCount}</span>
					</div>

					{#if showDetails && stats}
						<div class="mt-2 space-y-1">
							{#each Object.entries(stats.by_priority) as [priority, count]}
								{#if count > 0}
									{@const priorityIcon = getPriorityIcon(priority as NotificationPriority)}
									<div class="flex items-center justify-between text-sm">
										<div class="flex items-center gap-2">
											<Icon data={priorityIcon.icon} class="w-3 h-3 {priorityIcon.class}" />
											<span>{getPriorityText(priority as NotificationPriority)}</span>
										</div>
										<span class="text-base-content/60">{count}</span>
									</div>
								{/if}
							{/each}
						</div>
					{/if}
				</div>
			</div>
		{/if}
	</div>
{:else}
	<!-- 읽지 않은 알림이 없는 경우 -->
	<div class="notification-counter opacity-50">
		{#if compact}
			<div class="flex items-center gap-1">
				<Icon data={faBell} class="w-4 h-4 text-base-content/50" />
				<span class="text-xs text-base-content/50">0</span>
			</div>
		{:else}
			<div class="card card-compact bg-base-100 shadow-sm border border-base-200">
				<div class="card-body">
					<div class="flex items-center justify-between">
						<div class="flex items-center gap-2">
							<Icon data={faBell} class="w-5 h-5 text-base-content/50" />
							<span class="font-medium text-base-content/50">알림</span>
						</div>
						<span class="text-sm text-base-content/50">없음</span>
					</div>
				</div>
			</div>
		{/if}
	</div>
{/if}

<style>
	.notification-counter {
		transition: all 0.2s ease;
	}

	.notification-counter:hover {
		transform: translateY(-1px);
	}

	.notification-counter[role='button']:hover {
		filter: brightness(1.05);
	}
</style>
