<!--
알림 요약 컴포넌트
우선순위별 알림 통계와 빠른 필터링 기능을 제공합니다.

Requirements: 2.2.3, 3.2.3 - 우선순위별 필터링 UI, 읽음/안읽음 필터링 UI, 카운트 표시
-->

<script lang="ts">
	import type { NotificationPriority } from '$lib/types/notification';
	import { useNotificationCounter } from '$lib/stores/notificationRunes.svelte';
	import { Icon } from 'svelte-awesome';
	import { faBell } from '@fortawesome/free-solid-svg-icons/faBell';
	import { faExclamationTriangle } from '@fortawesome/free-solid-svg-icons/faExclamationTriangle';
	import { faExclamationCircle } from '@fortawesome/free-solid-svg-icons/faExclamationCircle';
	import { faInfoCircle } from '@fortawesome/free-solid-svg-icons/faInfoCircle';
	import { faFilter } from '@fortawesome/free-solid-svg-icons/faFilter';

	export interface Props {
		selectedPriority?: NotificationPriority | 'all';
		onprioritychange?: (priority: NotificationPriority | 'all') => void;
		onshowallclick?: () => void;
	}

	// Props (Svelte 5 방식)
	let { selectedPriority = 'all', onprioritychange, onshowallclick }: Props = $props();

	// 알림 카운터 훅
	const { unreadCount, hasUnread, stats } = useNotificationCounter();

	/**
	 * 우선순위에 따른 아이콘 반환
	 */
	function getPriorityIcon(priority: NotificationPriority) {
		switch (priority) {
			case 'urgent':
				return { icon: faExclamationTriangle, class: 'text-error' };
			case 'high':
				return { icon: faExclamationCircle, class: 'text-warning' };
			case 'low':
				return { icon: faInfoCircle, class: 'text-base-content/50' };
			default: // normal
				return { icon: faInfoCircle, class: 'text-info' };
		}
	}

	/**
	 * 우선순위 텍스트 반환
	 */
	function getPriorityText(priority: NotificationPriority): string {
		switch (priority) {
			case 'urgent':
				return '긴급';
			case 'high':
				return '높음';
			case 'low':
				return '낮음';
			default:
				return '보통';
		}
	}

	/**
	 * 우선순위 배지 클래스 반환
	 */
	function getPriorityBadgeClass(priority: NotificationPriority): string {
		switch (priority) {
			case 'urgent':
				return 'badge-error';
			case 'high':
				return 'badge-warning';
			case 'low':
				return 'badge-ghost';
			default: // normal
				return 'badge-info';
		}
	}

	/**
	 * 우선순위 필터 변경
	 */
	function handlePriorityChange(priority: NotificationPriority | 'all') {
		onprioritychange?.(priority);
	}

	/**
	 * 전체 보기 클릭
	 */
	function handleShowAllClick() {
		onshowallclick?.();
	}

	// 우선순위 목록 (카운트가 있는 것만)
	const prioritiesWithCount = $derived.by(() => {
		if (!stats) return [];

		return (['urgent', 'high', 'normal', 'low'] as NotificationPriority[])
			.filter((priority) => stats.by_priority[priority] > 0)
			.map((priority) => ({
				priority,
				count: stats.by_priority[priority],
				...getPriorityIcon(priority)
			}));
	});
</script>

<div class="notification-summary">
	<div class="card bg-base-100 shadow-sm border border-base-200">
		<div class="card-body p-4">
			<!-- 헤더 -->
			<div class="flex items-center justify-between mb-3">
				<div class="flex items-center gap-2">
					<Icon data={faBell} class="w-5 h-5 text-info" />
					<h3 class="font-semibold">알림 요약</h3>
				</div>
				{#if hasUnread}
					<span class="badge badge-error">{unreadCount}</span>
				{:else}
					<span class="badge badge-ghost">0</span>
				{/if}
			</div>

			{#if hasUnread && stats}
				<!-- 우선순위별 카운트 -->
				<div class="space-y-2 mb-4">
					{#each prioritiesWithCount as { priority, count, icon, class: iconClass }}
						<button
							class="w-full flex items-center justify-between p-2 rounded-lg hover:bg-base-200 transition-colors"
							class:bg-base-200={selectedPriority === priority}
							onclick={() => handlePriorityChange(priority)}
						>
							<div class="flex items-center gap-2">
								<Icon data={icon} class="w-4 h-4 {iconClass}" />
								<span class="text-sm">{getPriorityText(priority)}</span>
							</div>
							<span class="badge badge-sm {getPriorityBadgeClass(priority)}">{count}</span>
						</button>
					{/each}
				</div>

				<!-- 필터 버튼들 -->
				<div class="flex gap-2">
					<button
						class="btn btn-sm btn-outline flex-1"
						class:btn-active={selectedPriority === 'all'}
						onclick={() => handlePriorityChange('all')}
					>
						<Icon data={faFilter} class="w-3 h-3" />
						전체
					</button>

					{#if onshowallclick}
						<button class="btn btn-sm btn-primary" onclick={handleShowAllClick}> 상세보기 </button>
					{/if}
				</div>

				<!-- 통계 정보 -->
				{#if stats}
					<div class="mt-3 pt-3 border-t border-base-200">
						<div class="grid grid-cols-2 gap-2 text-xs text-base-content/60">
							<div class="flex justify-between">
								<span>총 알림:</span>
								<span>{stats.total}</span>
							</div>
							<div class="flex justify-between">
								<span>읽지 않음:</span>
								<span class="text-error">{stats.unread}</span>
							</div>
							{#if stats.expired > 0}
								<div class="flex justify-between col-span-2">
									<span>만료됨:</span>
									<span class="text-warning">{stats.expired}</span>
								</div>
							{/if}
						</div>
					</div>
				{/if}
			{:else}
				<!-- 알림이 없는 경우 -->
				<div class="text-center py-4 text-base-content/60">
					<Icon data={faBell} class="w-8 h-8 mx-auto mb-2 opacity-50" />
					<p class="text-sm">새로운 알림이 없습니다</p>
				</div>
			{/if}
		</div>
	</div>
</div>

<style>
	.notification-summary {
		min-width: 250px;
	}
</style>
