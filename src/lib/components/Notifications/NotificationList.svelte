<!--
알림 목록 컴포넌트
우선순위별 필터링과 읽음/안읽음 필터링 기능을 제공합니다.

Requirements: 2.2.3, 3.2.3 - 우선순위별 필터링 UI, 읽음/안읽음 필터링 UI, 카운트 표시
-->

<script lang="ts">
	import type { NotificationData, NotificationPriority } from '$lib/types/notification';
	import { NotificationUtils } from '$lib/types/notification';
	import NotificationItem from './NotificationItem.svelte';
	import { Icon } from 'svelte-awesome';
	import { faBell } from '@fortawesome/free-solid-svg-icons/faBell';
	import { faFilter } from '@fortawesome/free-solid-svg-icons/faFilter';
	import { faSearch } from '@fortawesome/free-solid-svg-icons/faSearch';
	import { faSort } from '@fortawesome/free-solid-svg-icons/faSort';

	export interface Props {
		notifications: NotificationData[];
		unreadCount?: number;
		showFilters?: boolean;
		showSearch?: boolean;
		showPagination?: boolean;
		itemsPerPage?: number;
		compact?: boolean;
		selectable?: boolean;
		onread?: (id: string) => void;
		ondelete?: (id: string) => void;
		onselect?: (ids: string[], selected: boolean) => void;
		onclick?: (notification: NotificationData) => void;
	}

	// Props (Svelte 5 방식)
	let {
		notifications,
		unreadCount = 0,
		showFilters = true,
		showSearch = true,
		showPagination = true,
		itemsPerPage = 10,
		compact = false,
		selectable = false,
		onread,
		ondelete,
		onselect,
		onclick
	}: Props = $props();

	// 로컬 상태
	let showExpired = $state(false);
	let priorityFilter = $state<'all' | NotificationPriority>('all');
	let readFilter = $state<'all' | 'read' | 'unread'>('all');
	let searchQuery = $state('');
	let sortBy = $state<'date' | 'priority' | 'read'>('date');
	let sortOrder = $state<'asc' | 'desc'>('desc');
	let currentPage = $state(1);
	let selectedNotifications = $state<Set<string>>(new Set());

	// 필터링된 알림 목록
	const filteredNotifications = $derived.by(() => {
		let filtered = [...notifications];

		// 만료 필터
		if (!showExpired) {
			filtered = filtered.filter((n) => !NotificationUtils.isExpired(n));
		}

		// 우선순위 필터
		if (priorityFilter !== 'all') {
			filtered = filtered.filter((n) => {
				const priority = n.priority || 'normal';
				return priority === priorityFilter;
			});
		}

		// 읽음 상태 필터
		if (readFilter !== 'all') {
			filtered = filtered.filter((n) => {
				return readFilter === 'read' ? n.read : !n.read;
			});
		}

		// 검색 필터
		if (searchQuery.trim()) {
			const query = searchQuery.toLowerCase().trim();
			filtered = filtered.filter(
				(n) =>
					n.content.toLowerCase().includes(query) ||
					(n.message && n.message.toLowerCase().includes(query))
			);
		}

		// 정렬
		filtered.sort((a, b) => {
			let comparison = 0;

			switch (sortBy) {
				case 'date':
					comparison = new Date(a.created_at).getTime() - new Date(b.created_at).getTime();
					break;
				case 'priority':
					const priorityOrder = { urgent: 4, high: 3, normal: 2, low: 1 };
					const aPriority = priorityOrder[a.priority || 'normal'];
					const bPriority = priorityOrder[b.priority || 'normal'];
					comparison = aPriority - bPriority;
					break;
				case 'read':
					comparison = (a.read ? 1 : 0) - (b.read ? 1 : 0);
					break;
			}

			return sortOrder === 'desc' ? -comparison : comparison;
		});

		return filtered;
	});

	// 페이지네이션 계산
	const totalPages = $derived(Math.ceil(filteredNotifications.length / itemsPerPage));
	const startIndex = $derived((currentPage - 1) * itemsPerPage);
	const endIndex = $derived(startIndex + itemsPerPage);
	const displayedNotifications = $derived(
		showPagination ? filteredNotifications.slice(startIndex, endIndex) : filteredNotifications
	);

	// 선택된 알림 수
	const selectedCount = $derived(selectedNotifications.size);

	/**
	 * 우선순위별 카운트 계산
	 */
	const priorityCounts = $derived.by(() => {
		const counts = {
			all: filteredNotifications.length,
			urgent: 0,
			high: 0,
			normal: 0,
			low: 0
		};

		filteredNotifications.forEach((n) => {
			const priority = n.priority || 'normal';
			counts[priority]++;
		});

		return counts;
	});

	/**
	 * 읽음 상태별 카운트 계산
	 */
	const readCounts = $derived.by(() => {
		const counts = {
			all: filteredNotifications.length,
			read: filteredNotifications.filter((n) => n.read).length,
			unread: filteredNotifications.filter((n) => !n.read).length
		};

		return counts;
	});

	/**
	 * 전체 선택/해제
	 */
	function toggleSelectAll() {
		const currentDisplayedIds = displayedNotifications.map((n) => n.id);
		const allCurrentSelected = currentDisplayedIds.every((id) => selectedNotifications.has(id));

		if (allCurrentSelected) {
			// 현재 표시된 모든 항목 선택 해제
			currentDisplayedIds.forEach((id) => selectedNotifications.delete(id));
		} else {
			// 현재 표시된 모든 항목 선택
			currentDisplayedIds.forEach((id) => selectedNotifications.add(id));
		}
		selectedNotifications = selectedNotifications; // 반응성 트리거

		// 부모에게 선택 상태 알림
		onselect?.(Array.from(selectedNotifications), !allCurrentSelected);
	}

	/**
	 * 개별 선택/해제
	 */
	function handleSelect(id: string, selected: boolean) {
		if (selected) {
			selectedNotifications.add(id);
		} else {
			selectedNotifications.delete(id);
		}
		selectedNotifications = selectedNotifications; // 반응성 트리거

		// 부모에게 선택 상태 알림
		onselect?.(Array.from(selectedNotifications), selected);
	}

	/**
	 * 페이지 변경
	 */
	function goToPage(page: number) {
		if (page >= 1 && page <= totalPages) {
			currentPage = page;
		}
	}

	/**
	 * 필터 변경 시 첫 페이지로 이동
	 */
	$effect(() => {
		showExpired; // 의존성 추적
		priorityFilter; // 의존성 추적
		readFilter; // 의존성 추적
		searchQuery; // 의존성 추적
		sortBy; // 의존성 추적
		sortOrder; // 의존성 추적
		currentPage = 1;
		selectedNotifications.clear(); // 필터 변경 시 선택 초기화
	});

	/**
	 * 우선순위 필터 라벨
	 */
	function getPriorityLabel(priority: string): string {
		switch (priority) {
			case 'urgent':
				return '긴급';
			case 'high':
				return '높음';
			case 'normal':
				return '보통';
			case 'low':
				return '낮음';
			default:
				return '전체';
		}
	}

	/**
	 * 읽음 상태 필터 라벨
	 */
	function getReadFilterLabel(filter: string): string {
		switch (filter) {
			case 'read':
				return '읽음';
			case 'unread':
				return '안읽음';
			default:
				return '전체';
		}
	}

	/**
	 * 정렬 라벨
	 */
	function getSortLabel(sort: string): string {
		switch (sort) {
			case 'date':
				return '날짜순';
			case 'priority':
				return '우선순위순';
			case 'read':
				return '읽음상태순';
			default:
				return '날짜순';
		}
	}
</script>

<div class="notification-list">
	<!-- 필터 및 검색 영역 -->
	{#if showFilters || showSearch}
		<div class="mb-4 space-y-3">
			<!-- 검색 입력 -->
			{#if showSearch}
				<div class="relative">
					<Icon
						data={faSearch}
						class="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-base-content/50"
					/>
					<input
						type="text"
						placeholder="알림 내용 검색..."
						class="input input-bordered w-full pl-10"
						bind:value={searchQuery}
					/>
				</div>
			{/if}

			<!-- 필터 옵션들 -->
			{#if showFilters}
				<div class="flex flex-wrap gap-3 items-center">
					<!-- 체크박스 필터들 -->
					<div class="form-control">
						<label class="label cursor-pointer gap-2">
							<input type="checkbox" class="checkbox checkbox-sm" bind:checked={showExpired} />
							<span class="label-text">만료된 알림 포함</span>
						</label>
					</div>

					<!-- 우선순위 필터 -->
					<div class="flex items-center gap-2">
						<Icon data={faFilter} class="w-4 h-4 text-base-content/50" />
						<select class="select select-sm select-bordered" bind:value={priorityFilter}>
							<option value="all">모든 우선순위 ({priorityCounts.all})</option>
							<option value="urgent">긴급 ({priorityCounts.urgent})</option>
							<option value="high">높음 ({priorityCounts.high})</option>
							<option value="normal">보통 ({priorityCounts.normal})</option>
							<option value="low">낮음 ({priorityCounts.low})</option>
						</select>
					</div>

					<!-- 읽음 상태 필터 -->
					<div class="flex items-center gap-2">
						<select class="select select-sm select-bordered" bind:value={readFilter}>
							<option value="all">모든 상태 ({readCounts.all})</option>
							<option value="unread">안읽음 ({readCounts.unread})</option>
							<option value="read">읽음 ({readCounts.read})</option>
						</select>
					</div>

					<!-- 정렬 옵션 -->
					<div class="flex items-center gap-2">
						<Icon data={faSort} class="w-4 h-4 text-base-content/50" />
						<select class="select select-sm select-bordered" bind:value={sortBy}>
							<option value="date">날짜순</option>
							<option value="priority">우선순위순</option>
							<option value="read">읽음상태순</option>
						</select>

						<button
							class="btn btn-sm btn-outline"
							onclick={() => (sortOrder = sortOrder === 'desc' ? 'asc' : 'desc')}
							title={sortOrder === 'desc' ? '내림차순' : '오름차순'}
						>
							{sortOrder === 'desc' ? '↓' : '↑'}
						</button>
					</div>
				</div>
			{/if}

			<!-- 선택 관련 정보 -->
			{#if selectable && displayedNotifications.length > 0}
				{@const currentDisplayedIds = displayedNotifications.map((n) => n.id)}
				{@const allCurrentSelected = currentDisplayedIds.every((id) =>
					selectedNotifications.has(id)
				)}
				{@const someCurrentSelected = currentDisplayedIds.some((id) =>
					selectedNotifications.has(id)
				)}

				<div class="flex items-center gap-2 p-2 bg-base-200 rounded-lg">
					<input
						type="checkbox"
						class="checkbox checkbox-sm"
						checked={allCurrentSelected}
						onclick={toggleSelectAll}
					/>
					<span class="text-sm text-base-content/70">
						현재 페이지 선택 ({displayedNotifications.length}개)
						{#if someCurrentSelected && !allCurrentSelected}
							<span class="text-primary">(일부 선택됨)</span>
						{/if}
						{#if selectedCount > 0}
							<span class="text-primary ml-2">전체 선택: {selectedCount}개</span>
						{/if}
					</span>
				</div>
			{/if}
		</div>
	{/if}

	<!-- 알림 목록 -->
	<div class="space-y-2">
		{#if displayedNotifications.length === 0}
			<div class="text-center py-8 text-base-content/60">
				<Icon data={faBell} class="w-12 h-12 mx-auto mb-2 opacity-50" />
				<p>표시할 알림이 없습니다.</p>
				{#if !showExpired}
					<p class="text-sm mt-1">만료된 알림을 보려면 위의 체크박스를 선택하세요.</p>
				{/if}
			</div>
		{:else}
			{#each displayedNotifications as notification (notification.id)}
				<NotificationItem
					{notification}
					{compact}
					{selectable}
					selected={selectedNotifications.has(notification.id)}
					{searchQuery}
					{onread}
					{ondelete}
					{onclick}
					onselect={handleSelect}
				/>
			{/each}
		{/if}
	</div>

	<!-- 페이지네이션 -->
	{#if showPagination && totalPages > 1}
		<div class="flex justify-center mt-6">
			<div class="join">
				<!-- 이전 페이지 -->
				<button
					class="join-item btn btn-sm"
					class:btn-disabled={currentPage === 1}
					onclick={() => goToPage(currentPage - 1)}
				>
					이전
				</button>

				<!-- 페이지 번호들 -->
				{#each Array.from({ length: totalPages }, (_, i) => i + 1) as page}
					{#if totalPages <= 7 || page === 1 || page === totalPages || Math.abs(page - currentPage) <= 2}
						<button
							class="join-item btn btn-sm"
							class:btn-active={page === currentPage}
							onclick={() => goToPage(page)}
						>
							{page}
						</button>
					{:else if page === currentPage - 3 || page === currentPage + 3}
						<button class="join-item btn btn-sm btn-disabled">...</button>
					{/if}
				{/each}

				<!-- 다음 페이지 -->
				<button
					class="join-item btn btn-sm"
					class:btn-disabled={currentPage === totalPages}
					onclick={() => goToPage(currentPage + 1)}
				>
					다음
				</button>
			</div>
		</div>
	{/if}

	<!-- 하단 정보 -->
	<div class="mt-4 text-sm text-base-content/60 text-center">
		<span>
			총 {filteredNotifications.length}개 알림
			{#if unreadCount > 0}
				(읽지 않음: {unreadCount}개)
			{/if}
		</span>
		{#if showPagination && totalPages > 1}
			<span class="ml-2">
				페이지 {currentPage} / {totalPages}
				({startIndex + 1}-{Math.min(endIndex, filteredNotifications.length)})
			</span>
		{/if}
	</div>
</div>
