<script lang="ts">
	import { onMount } from 'svelte';
	import { getCurrentVersion, updateState, showUpdateModal } from '$lib/services/updateService';
	import { useNotificationRotation } from '$lib/stores/notificationRunes.svelte';
	import NotificationListModal from '$lib/components/Modals/NotificationListModal.svelte';

	import { Icon } from 'svelte-awesome';
	import { faCheck } from '@fortawesome/free-solid-svg-icons/faCheck';
	import { faBell } from '@fortawesome/free-solid-svg-icons/faBell';

	const isDevelopment = import.meta.env.VITE_NODE_ENV === 'development';

	let currentVersion = $state('');
	let showNotificationModal = $state(false);

	// 알림 로테이션 관련 상태
	const { isRotating, currentNotification, hasNotifications } = useNotificationRotation();

	// Footer 전용 로테이션 설정
	const MAX_DISPLAY_LENGTH = 50; // 최대 표시 길이

	// 알림 내용 표시 길이 제한 함수
	function truncateContent(content: string, maxLength: number = MAX_DISPLAY_LENGTH): string {
		if (content.length <= maxLength) return content;
		return content.substring(0, maxLength - 3) + '...';
	}

	/**
	 * 우선순위에 따른 아이콘 색상 클래스 반환
	 */
	function getPriorityIconClass(priority: string): string {
		switch (priority) {
			case 'urgent':
				return 'text-error';
			case 'high':
				return 'text-warning';
			case 'low':
				return 'text-base-content/50';
			default: // normal
				return 'text-info';
		}
	}

	/**
	 * 우선순위에 따른 배경 색상 클래스 반환
	 */
	function getPriorityBgClass(priority: string): string {
		switch (priority) {
			case 'urgent':
				return 'hover:bg-error/10';
			case 'high':
				return 'hover:bg-warning/10';
			case 'low':
				return 'hover:bg-base-300/50';
			default: // normal
				return 'hover:bg-info/10';
		}
	}

	/**
	 * 우선순위에 따른 배지 클래스 반환
	 */
	function getPriorityBadgeClass(priority: string): string {
		switch (priority) {
			case 'urgent':
				return 'badge-error';
			case 'high':
				return 'badge-warning';
			case 'low':
				return 'badge-ghost';
			default: // normal
				return 'badge-info';
		}
	}

	/**
	 * 우선순위 텍스트 반환
	 */
	function getPriorityText(priority: string): string {
		switch (priority) {
			case 'urgent':
				return '긴급';
			case 'high':
				return '높음';
			case 'low':
				return '낮음';
			default:
				return '보통';
		}
	}

	onMount(async () => {
		try {
			currentVersion = await getCurrentVersion();
		} catch (error) {
			console.error('버전 정보 가져오기 실패:', error);
			currentVersion = 'Unknown';
		}
	});

	function handleVersionClick() {
		showUpdateModal();
	}

	function handleNotificationClick() {
		showNotificationModal = true;
	}

	function handleNotificationModalClose() {
		showNotificationModal = false;
	}
</script>

<footer
	class="relative footer items-center p-2 bg-base-300 text-base-content border-t border-base-200"
>
	<div class="flex justify-between w-full items-center min-h-[2rem]">
		<!-- 왼쪽: 알림 표시 영역 -->
		<div class="flex-1 min-w-0">
			{#if hasNotifications && currentNotification}
				{@const priority = currentNotification.priority || 'normal'}
				{@const priorityClass = getPriorityIconClass(priority)}
				{@const priorityBgClass = getPriorityBgClass(priority)}

				<button
					class="btn btn-ghost btn-xs text-xs hover:bg-base-200 transition-all duration-300 w-full justify-start min-w-0 {priorityBgClass}"
					class:animate-pulse={isRotating}
					onclick={handleNotificationClick}
					title="알림 목록 보기 - {getPriorityText(priority)} - {currentNotification.content}"
				>
					<Icon
						data={faBell}
						class="w-3 h-3 mr-1 flex-shrink-0 {priorityClass} transition-colors {isRotating
							? 'animate-bounce'
							: ''}"
					/>
					{#if priority !== 'normal'}
						<span class="badge badge-xs {getPriorityBadgeClass(priority)} mr-1">
							{getPriorityText(priority)}
						</span>
					{/if}
					<span class="truncate text-left transition-opacity duration-300">
						{truncateContent(currentNotification.content)}
					</span>
				</button>
			{:else}
				<!-- 알림이 없을 때는 빈 공간 유지 (레이아웃 안정성) -->
				<div class="h-6 flex items-center">
					<span class="text-xs text-base-content/50 ml-2">알림 없음</span>
				</div>
			{/if}
		</div>

		<!-- 중앙: 개발 버전 배지 -->
		<div class="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2">
			{#if isDevelopment}
				<span class="badge badge-warning badge-xs">개발 버전</span>
			{/if}
		</div>

		<!-- 오른쪽: 버전 정보 -->
		<div class="flex-1 flex justify-end">
			<div class="relative">
				<button
					class="btn btn-ghost btn-xs text-xs hover:bg-base-200 transition-colors"
					class:btn-outline={$updateState.isAvailable}
					class:text-primary={$updateState.isAvailable}
					onclick={handleVersionClick}
					title={$updateState.isAvailable
						? '새 업데이트 사용 가능 - 클릭하여 확인'
						: '업데이트 확인'}
				>
					<Icon data={faCheck} class="w-3 h-3 mr-1" />
					v{currentVersion}
				</button>

				<!-- 업데이트 알림 배지 -->
				{#if $updateState.isAvailable}
					<div
						class="absolute -top-2 -right-2 flex items-center justify-center"
						aria-label="업데이트 사용 가능"
						title="새 버전 {$updateState.version} 사용 가능"
					>
						<!-- 펄스 애니메이션 배경 -->
						<div class="absolute w-4 h-4 bg-error rounded-full animate-ping opacity-75"></div>
						<!-- 메인 배지 (UP 텍스트) -->
						<div
							class="relative bg-error text-error-content text-[8px] font-bold px-1 py-0.5 rounded-full border border-base-100 leading-none"
						>
							UP
						</div>
					</div>
				{/if}
			</div>
		</div>
	</div>
</footer>

<!-- 알림 목록 모달 -->
{#if showNotificationModal}
	<NotificationListModal onclose={handleNotificationModalClose} />
{/if}
