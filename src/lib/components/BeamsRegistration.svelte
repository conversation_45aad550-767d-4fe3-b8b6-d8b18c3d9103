<script lang="ts">
	/**
	 * Pusher Beams 등록 컴포넌트
	 * 애플리케이션에서 Beams 등록을 관리하는 컴포넌트
	 */

	import { onMount } from 'svelte';
	import { createDesktopPushService } from '$lib/services/desktopPushService';
	import {
		autoRegisterOnAppStart,
		registerBeamsOnLogin,
		cleanupOnLogout
	} from '$lib/services/examples/beamsRegistrationExample';
	import type { BeamsRegistrationState } from '$lib/types/pushNotificationTypes';

	// Props
	let {
		userId = null,
		userRole = null,
		autoRegister = true
	}: {
		userId?: string | null;
		userRole?: 'admin' | 'manager' | 'operator' | 'viewer' | null;
		autoRegister?: boolean;
	} = $props();

	// 상태
	let registrationState = $state<BeamsRegistrationState | null>(null);
	let isRegistering = $state(false);
	let registrationError = $state<string | null>(null);
	let pushService = $state<ReturnType<typeof createDesktopPushService> | null>(null);

	// 등록 상태 업데이트
	async function updateRegistrationState() {
		if (pushService) {
			try {
				registrationState = await pushService.getRegistrationState();
			} catch (error) {
				console.error('[BeamsRegistration] 상태 업데이트 실패:', error);
			}
		}
	}

	// 수동 등록
	async function manualRegister() {
		if (!userId || isRegistering) return;

		isRegistering = true;
		registrationError = null;

		try {
			const result = await registerBeamsOnLogin(userId);
			if (result.success && result.pushService) {
				pushService = result.pushService;
				await updateRegistrationState();
				console.log('[BeamsRegistration] 수동 등록 완료');
			} else {
				throw new Error(result.error?.message || '등록 실패');
			}
		} catch (error) {
			registrationError = error instanceof Error ? error.message : '알 수 없는 오류';
			console.error('[BeamsRegistration] 수동 등록 실패:', error);
		} finally {
			isRegistering = false;
		}
	}

	// 등록 해제
	async function unregister() {
		if (!pushService) return;

		try {
			await pushService.clearBeamsData();
			await pushService.cleanup();
			pushService = null;
			registrationState = null;
			console.log('[BeamsRegistration] 등록 해제 완료');
		} catch (error) {
			console.error('[BeamsRegistration] 등록 해제 실패:', error);
		}
	}

	// 컴포넌트 마운트 시 자동 등록
	onMount(async () => {
		if (autoRegister) {
			try {
				const result = await autoRegisterOnAppStart();
				if (result.success && result.pushService) {
					pushService = result.pushService;
					await updateRegistrationState();
				}
			} catch (error) {
				console.error('[BeamsRegistration] 자동 등록 실패:', error);
			}
		}
	});

	// 사용자 변경 시 처리
	$effect(() => {
		if (userId && !pushService && !isRegistering) {
			manualRegister();
		}
	});

	// 로그아웃 시 정리
	$effect(() => {
		if (!userId && pushService) {
			cleanupOnLogout(pushService);
		}
	});

	// 상태 텍스트 변환
	function getStateText(state: BeamsRegistrationState | null): string {
		switch (state) {
			case 'NOT_INITIALIZED':
				return '초기화되지 않음';
			case 'PERMISSION_PROMPT_REQUIRED':
				return '권한 요청 필요';
			case 'PERMISSION_DENIED':
				return '권한 거부됨';
			case 'PERMISSION_GRANTED_NOT_REGISTERED_WITH_BEAMS':
				return '권한 승인됨 (등록 필요)';
			case 'PERMISSION_GRANTED_REGISTERED_WITH_BEAMS':
				return '등록 완료';
			default:
				return '알 수 없음';
		}
	}

	// 상태 색상
	function getStateColor(state: BeamsRegistrationState | null): string {
		switch (state) {
			case 'PERMISSION_GRANTED_REGISTERED_WITH_BEAMS':
				return 'text-success';
			case 'PERMISSION_DENIED':
				return 'text-error';
			case 'PERMISSION_PROMPT_REQUIRED':
				return 'text-warning';
			default:
				return 'text-info';
		}
	}
</script>

<!-- 개발 환경에서만 표시되는 디버그 정보 -->
{#if import.meta.env.DEV}
	<div class="card bg-base-200 shadow-sm mb-4">
		<div class="card-body p-4">
			<h3 class="card-title text-sm">🔔 Pusher Beams 등록 상태</h3>

			<div class="grid grid-cols-2 gap-4 text-sm">
				<div>
					<span class="font-medium">사용자:</span>
					<span class="ml-2">{userId || '미로그인'}</span>
				</div>

				<div>
					<span class="font-medium">역할:</span>
					<span class="ml-2">{userRole || '없음'}</span>
				</div>

				<div>
					<span class="font-medium">등록 상태:</span>
					<span class="ml-2 {getStateColor(registrationState)}">
						{getStateText(registrationState)}
					</span>
				</div>

				<div>
					<span class="font-medium">서비스:</span>
					<span class="ml-2">{pushService ? '활성' : '비활성'}</span>
				</div>
			</div>

			{#if registrationError}
				<div class="alert alert-error mt-2">
					<span class="text-xs">{registrationError}</span>
				</div>
			{/if}

			<div class="card-actions justify-end mt-4">
				{#if !pushService}
					<button
						class="btn btn-primary btn-sm"
						class:loading={isRegistering}
						onclick={manualRegister}
						disabled={!userId || isRegistering}
					>
						{isRegistering ? '등록 중...' : '수동 등록'}
					</button>
				{:else}
					<button class="btn btn-secondary btn-sm" onclick={updateRegistrationState}>
						상태 새로고침
					</button>
					<button class="btn btn-error btn-sm" onclick={unregister}> 등록 해제 </button>
				{/if}
			</div>

			{#if pushService}
				<div class="mt-2 text-xs opacity-70">
					브라우저 콘솔에서 <code>beamsTest.test()</code> 실행 가능
				</div>
			{/if}
		</div>
	</div>
{/if}
