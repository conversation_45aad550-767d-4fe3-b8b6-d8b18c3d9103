<script lang="ts">
	import { onMount } from 'svelte';
	import { browser } from '$app/environment';
	import { goto } from '$app/navigation';
	import { page } from '$app/state';
	import { pageRestore } from '$lib/services/updateService';

	let showRestoreNotification = $state(false);
	let savedUrl = $state('');
	let isRestoring = $state(false);

	onMount(() => {
		if (!browser) return;

		// 현재 페이지 정보
		const currentUrl = page.url.pathname + page.url.search;

		// 저장된 페이지 정보 확인
		const shouldRestore = pageRestore.shouldRestore();
		const savedPageUrl = pageRestore.get();

		// 복원 로직: 저장된 페이지가 있으면 항상 복원 알림 표시
		if (shouldRestore && savedPageUrl) {
			// 저장된 URL이 현재 URL과 다른 경우에만 복원 알림 표시
			if (savedPageUrl !== currentUrl) {
				savedUrl = savedPageUrl;
				showRestoreNotification = true;
			} else {
				pageRestore.clear();
			}
		} else {
			// 저장된 데이터가 있지만 유효하지 않은 경우 정리
			if (savedPageUrl === null && pageRestore.get(0) !== null) {
				pageRestore.clear();
			}
		}
	});

	async function handleRestore() {
		if (savedUrl && !isRestoring) {
			isRestoring = true;

			try {
				pageRestore.clear();
				showRestoreNotification = false;
				await goto(savedUrl);
			} catch (error) {
				// 복원 실패 시 알림 다시 표시
				showRestoreNotification = true;
			} finally {
				isRestoring = false;
			}
		}
	}

	function handleDismiss() {
		pageRestore.clear();
		showRestoreNotification = false;
	}
</script>



<!-- 페이지 복원 알림 -->
{#if showRestoreNotification}
	<div class="fixed top-4 left-1/2 transform -translate-x-1/2 z-50 animate-in slide-in-from-top-2 duration-300">
		<div class="card bg-base-100 shadow-xl border border-primary/20 w-96 max-w-[90vw]">
			<div class="card-body p-6">
				<!-- 헤더 -->
				<div class="flex items-center gap-3 mb-4">
					<div class="flex-shrink-0">
						<svg
							class="w-8 h-8 text-primary"
							fill="none"
							viewBox="0 0 24 24"
							stroke="currentColor"
						>
							<path
								stroke-linecap="round"
								stroke-linejoin="round"
								stroke-width="2"
								d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
							/>
						</svg>
					</div>
					<div class="flex-1">
						<h3 class="font-bold text-lg text-base-content">업데이트 완료!</h3>
						<p class="text-sm text-base-content/70 mt-1">
							앱이 성공적으로 업데이트되었습니다.
						</p>
					</div>
				</div>

				<!-- 메시지 -->
				<div class="bg-primary/5 rounded-lg p-4 mb-4">
					<p class="text-base text-base-content font-medium">
						마지막 작업 중이던 페이지로 돌아가시겠습니까?
					</p>
					<p class="text-sm text-base-content/60 mt-2">
						업데이트 전에 보고 계시던 페이지로 자동으로 이동할 수 있습니다.
					</p>
				</div>

				<!-- 버튼 영역 -->
				<div class="flex flex-col sm:flex-row gap-3 w-full">
					<button
						class="btn btn-primary flex-1 h-12"
						onclick={handleRestore}
						disabled={isRestoring}
						aria-label="이전 페이지로 돌아가기"
					>
						{#if isRestoring}
							<span class="loading loading-spinner loading-sm"></span>
							복원 중...
						{:else}
							<svg class="w-5 h-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
							</svg>
							돌아가기
						{/if}
					</button>
					<button
						class="btn btn-outline flex-1 h-12"
						onclick={handleDismiss}
						aria-label="알림 닫기"
					>
						<svg class="w-5 h-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
						</svg>
						현재 페이지 유지
					</button>
				</div>
			</div>
		</div>
	</div>
{/if}
