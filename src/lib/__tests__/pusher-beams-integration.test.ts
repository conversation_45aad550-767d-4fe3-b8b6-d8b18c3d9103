/**
 * Pusher Beams 연동 테스트 (9.2)
 *
 * 이 테스트는 실제 Pusher Beams 연동이 올바르게 동작하는지 검증합니다:
 * - 실제 Pusher Beams 연동 테스트
 * - 알림 수신부터 IndexedDB 저장까지 전체 플로우 테스트
 * - 우선순위별 알림 처리 테스트
 *
 * Requirements: 모든 기능 요구사항
 */

import { describe, it, expect, beforeEach, afterEach, vi, beforeAll, afterAll } from 'vitest';

// Pusher Beams Web SDK 모킹
const mockPusherClient = {
	start: vi.fn(),
	stop: vi.fn(),
	setUserId: vi.fn(),
	clearAllState: vi.fn(),
	getDeviceId: vi.fn(),
	getRegistrationState: vi.fn()
};

vi.mock('@pusher/push-notifications-web', () => ({
	Client: vi.fn().mockImplementation(() => mockPusherClient)
}));

// Tauri FCM API 모킹
vi.mock('tauri-plugin-remote-push-api', () => ({
	getToken: vi.fn(),
	requestPermission: vi.fn(),
	onNotificationReceived: vi.fn(),
	onTokenRefresh: vi.fn()
}));

// Pusher Beams 서비스들
import { createDesktopPushService } from '$lib/services/desktopPushService';
import { createAndroidPushService } from '$lib/services/androidPushService';
import { createPushServiceManager } from '$lib/services/pushServiceManager';

// 기존 인프라
import { tokenService } from '$lib/services/tokenService';
import {
	saveNotification,
	saveReceivedNotification,
	getAllNotifications,
	getNotificationsByPriority,
	getUnreadNotificationCount,
	markNotificationAsRead,
	initNotificationDatabase
} from '$lib/utils/notification-database';

import { closeEmployeeDB } from '$lib/utils/indexeddb-utils';

import type { NotificationData } from '$lib/types/notification';
import type { PusherBeamsNotificationData } from '$lib/types/pushNotificationTypes';

// Tauri Store 모킹
const mockTauriStore = {
	get: vi.fn(),
	set: vi.fn(),
	save: vi.fn(),
	load: vi.fn(),
	clear: vi.fn(),
	delete: vi.fn(),
	entries: vi.fn(),
	keys: vi.fn(),
	values: vi.fn(),
	length: vi.fn(),
	reset: vi.fn()
};

vi.mock('@tauri-apps/plugin-store', () => ({
	Store: vi.fn().mockImplementation(() => mockTauriStore)
}));

// IndexedDB 모킹
const mockIndexedDB = {
	open: vi.fn(),
	deleteDatabase: vi.fn()
};

const mockIDBDatabase = {
	createObjectStore: vi.fn(),
	transaction: vi.fn(),
	close: vi.fn(),
	version: 3,
	name: 'EmployeeDB',
	objectStoreNames: ['push_notifications', 'print_settings']
};

const mockIDBTransaction = {
	objectStore: vi.fn(),
	oncomplete: null,
	onerror: null,
	onabort: null
};

const mockIDBObjectStore = {
	add: vi.fn(),
	put: vi.fn(),
	get: vi.fn(),
	delete: vi.fn(),
	getAll: vi.fn(),
	index: vi.fn(),
	createIndex: vi.fn()
};

const mockIDBRequest = {
	onsuccess: null,
	onerror: null,
	result: null
};

const mockIDBIndex = {
	getAll: vi.fn()
};

// 전역 IndexedDB 모킹
global.indexedDB = mockIndexedDB as any;
global.IDBKeyRange = {
	bound: vi.fn(),
	only: vi.fn(),
	lowerBound: vi.fn(),
	upperBound: vi.fn()
} as any;

// Service Worker 모킹
const mockServiceWorkerRegistration = {
	unregister: vi.fn(),
	addEventListener: vi.fn(),
	scope: '/service-worker.js'
};

const mockServiceWorker = {
	register: vi.fn(),
	addEventListener: vi.fn()
};

// 브라우저 API 모킹
global.navigator = {
	serviceWorker: mockServiceWorker,
	onLine: true,
	userAgent:
		'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
} as any;

global.Notification = {
	permission: 'granted',
	requestPermission: vi.fn().mockResolvedValue('granted')
} as any;

global.document = {
	visibilityState: 'visible'
} as any;

global.localStorage = {
	getItem: vi.fn(),
	setItem: vi.fn(),
	removeItem: vi.fn(),
	clear: vi.fn()
} as any;

describe('Pusher Beams 연동 테스트 (9.2)', () => {
	// 테스트용 데이터
	const validAccessToken =
		'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJzdWIiOiIxIiwiaWF0IjoxNzA0MDY3MjAwLCJleHAiOjk5OTk5OTk5OTksInR5cGUiOiJhY2Nlc3MifQ.valid-access-token';
	const validRefreshToken =
		'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJzdWIiOiIxIiwiaWF0IjoxNzA0MDY3MjAwLCJleHAiOjk5OTk5OTk5OTksInR5cGUiOiJyZWZyZXNoIn0.valid-refresh-token';

	const testPusherNotification: PusherBeamsNotificationData = {
		id: 'pusher-test-notification-1',
		title: 'Pusher Beams 테스트 알림',
		body: 'Pusher Beams에서 전송된 테스트 알림입니다.',
		priority: 'high',
		category: 'urgent',
		image_url: 'https://example.com/pusher-image.jpg',
		expire_at: '2025-12-31T23:59:59Z',
		deletable: true
	};

	const urgentNotification: PusherBeamsNotificationData = {
		id: 'urgent-notification-1',
		title: '긴급 알림',
		body: '긴급 상황이 발생했습니다.',
		priority: 'urgent',
		category: 'emergency',
		deletable: false
	};

	const normalNotification: PusherBeamsNotificationData = {
		id: 'normal-notification-1',
		title: '일반 알림',
		body: '일반적인 알림입니다.',
		priority: 'normal',
		category: 'general',
		deletable: true
	};

	beforeAll(async () => {
		// 전역 설정
		global.console.log = vi.fn();
		global.console.error = vi.fn();
		global.console.warn = vi.fn();

		// 환경 변수 설정
		vi.stubEnv('VITE_PUSHER_BEAMS_INSTANCE_ID', 'test-instance-id');
	});

	beforeEach(async () => {
		vi.clearAllMocks();

		// Tauri Store 모킹 초기화
		mockTauriStore.get.mockResolvedValue(null);
		mockTauriStore.set.mockResolvedValue(undefined);
		mockTauriStore.save.mockResolvedValue(undefined);
		mockTauriStore.load.mockResolvedValue(undefined);
		mockTauriStore.clear.mockResolvedValue(undefined);
		mockTauriStore.delete.mockResolvedValue(undefined);

		// Pusher Beams 클라이언트 모킹 초기화
		mockPusherClient.start.mockResolvedValue(undefined);
		mockPusherClient.stop.mockResolvedValue(undefined);
		mockPusherClient.setUserId.mockResolvedValue(undefined);
		mockPusherClient.clearAllState.mockResolvedValue(undefined);
		mockPusherClient.getDeviceId.mockReturnValue('test-device-id');
		mockPusherClient.getRegistrationState.mockReturnValue('REGISTERED');

		// Tauri FCM API 모킹 초기화
		const { getToken, requestPermission, onNotificationReceived, onTokenRefresh } = await import(
			'tauri-plugin-remote-push-api'
		);
		vi.mocked(getToken).mockResolvedValue('test-fcm-token');
		vi.mocked(requestPermission).mockResolvedValue({ granted: true });
		vi.mocked(onNotificationReceived).mockResolvedValue(() => {});
		vi.mocked(onTokenRefresh).mockResolvedValue(() => {});

		// Service Worker 모킹 초기화
		mockServiceWorker.register.mockResolvedValue(mockServiceWorkerRegistration);
		mockServiceWorkerRegistration.unregister.mockResolvedValue(true);

		// localStorage 모킹 초기화
		global.localStorage.getItem.mockReturnValue(null);
		global.localStorage.setItem.mockImplementation(() => {});
		global.localStorage.removeItem.mockImplementation(() => {});

		// IndexedDB 모킹 초기화
		mockIndexedDB.open.mockImplementation(() => {
			const request = { ...mockIDBRequest };
			setTimeout(() => {
				request.result = mockIDBDatabase;
				if (request.onsuccess) request.onsuccess({ target: request } as any);
			}, 0);
			return request as any;
		});

		mockIDBDatabase.transaction.mockReturnValue(mockIDBTransaction);
		mockIDBTransaction.objectStore.mockReturnValue(mockIDBObjectStore);
		mockIDBObjectStore.index.mockReturnValue(mockIDBIndex);

		// 기본 성공 응답 설정
		mockIDBObjectStore.add.mockImplementation(() => {
			const request = { ...mockIDBRequest };
			setTimeout(() => {
				request.result = 'success';
				if (request.onsuccess) request.onsuccess({ target: request } as any);
			}, 0);
			return request as any;
		});

		mockIDBObjectStore.put.mockImplementation(() => {
			const request = { ...mockIDBRequest };
			setTimeout(() => {
				request.result = 'success';
				if (request.onsuccess) request.onsuccess({ target: request } as any);
			}, 0);
			return request as any;
		});

		mockIDBObjectStore.get.mockImplementation(() => {
			const request = { ...mockIDBRequest };
			setTimeout(() => {
				request.result = null;
				if (request.onsuccess) request.onsuccess({ target: request } as any);
			}, 0);
			return request as any;
		});

		mockIDBObjectStore.getAll.mockImplementation(() => {
			const request = { ...mockIDBRequest };
			setTimeout(() => {
				request.result = [];
				if (request.onsuccess) request.onsuccess({ target: request } as any);
			}, 0);
			return request as any;
		});

		mockIDBIndex.getAll.mockImplementation(() => {
			const request = { ...mockIDBRequest };
			setTimeout(() => {
				request.result = [];
				if (request.onsuccess) request.onsuccess({ target: request } as any);
			}, 0);
			return request as any;
		});
	});

	afterEach(() => {
		vi.clearAllMocks();
		closeEmployeeDB();
	});

	afterAll(() => {
		vi.restoreAllMocks();
		vi.unstubAllEnvs();
	});
	describe('실제 Pusher Beams 연동 테스트', () => {
		beforeEach(async () => {
			await tokenService.initialize();
			await initNotificationDatabase();
		});

		describe('데스크탑 Pusher Beams Web SDK 연동', () => {
			it('데스크탑 푸시 서비스가 Pusher Beams에 정상적으로 연결되어야 함', async () => {
				// 데스크탑 푸시 서비스 생성
				const desktopService = createDesktopPushService();

				// 서비스 초기화
				await desktopService.initialize();

				// Pusher Beams 클라이언트 초기화 확인
				expect(mockPusherClient.start).not.toHaveBeenCalled(); // 아직 등록 전

				// 디바이스 등록
				const deviceId = await desktopService.registerDevice();

				// Pusher Beams 시작 확인
				expect(mockPusherClient.start).toHaveBeenCalled();
				expect(deviceId).toBeDefined();
				expect(typeof deviceId).toBe('string');

				// Service Worker 등록 확인
				expect(mockServiceWorker.register).toHaveBeenCalledWith('/service-worker.js');

				// 서비스 상태 확인
				const status = desktopService.getServiceStatus();
				expect(status.isInitialized).toBe(true);
				expect(status.hasBeamsClient).toBe(true);
				expect(status.deviceId).toBe(deviceId);

				await desktopService.cleanup();
			});

			it('데스크탑에서 사용자 ID 연결이 JWT 토큰으로 정상적으로 동작해야 함', async () => {
				// 토큰 설정
				await tokenService.storeTokens(validAccessToken, validRefreshToken);

				// 데스크탑 푸시 서비스 생성 및 초기화
				const desktopService = createDesktopPushService();
				await desktopService.initialize();
				await desktopService.registerDevice();

				// 사용자 ID 연결
				await desktopService.setUserId();

				// Pusher Beams setUserId 호출 확인
				expect(mockPusherClient.setUserId).toHaveBeenCalledWith('1', expect.any(Object));

				// 서비스 상태에서 사용자 ID 확인
				const status = desktopService.getServiceStatus();
				expect(status.userId).toBe('1');

				// 사용자 ID 연결 해제
				await desktopService.clearUserId();
				expect(mockPusherClient.clearAllState).toHaveBeenCalled();

				await desktopService.cleanup();
			});

			it('데스크탑에서 알림 권한 요청이 정상적으로 동작해야 함', async () => {
				const desktopService = createDesktopPushService();
				await desktopService.initialize();

				// 권한 상태 확인
				const initialStatus = await desktopService.getPermissionStatus();
				expect(initialStatus).toBe('granted'); // 모킹된 상태

				// 권한 요청
				const requestedStatus = await desktopService.requestPermission();
				expect(requestedStatus).toBe('granted');

				await desktopService.cleanup();
			});
		});

		describe('안드로이드 FCM 연동', () => {
			it('안드로이드 푸시 서비스가 FCM 토큰을 정상적으로 획득해야 함', async () => {
				// 안드로이드 푸시 서비스 생성
				const androidService = createAndroidPushService();

				// 서비스 초기화
				await androidService.initialize();

				// FCM 토큰 획득 확인
				expect(mockTauriPush.getToken).toHaveBeenCalled();

				// 디바이스 토큰 조회
				const deviceToken = await androidService.getDeviceToken();
				expect(deviceToken).toBe('test-fcm-token');

				androidService.cleanup();
			});

			it('안드로이드에서 Pusher Beams 등록 시뮬레이션이 정상적으로 동작해야 함', async () => {
				// 토큰 설정
				await tokenService.storeTokens(validAccessToken, validRefreshToken);

				// 안드로이드 푸시 서비스 생성 및 초기화
				const androidService = createAndroidPushService();
				await androidService.initialize();

				// Pusher Beams 등록 시뮬레이션
				await androidService.registerWithBeams();

				// 로컬 저장소에 디바이스 정보 저장 확인
				expect(global.localStorage.setItem).toHaveBeenCalledWith(
					'cnsprowms_android_beams_device',
					expect.stringContaining('android_')
				);

				androidService.cleanup();
			});

			it('안드로이드에서 알림 권한 요청이 정상적으로 동작해야 함', async () => {
				const androidService = createAndroidPushService();
				await androidService.initialize();

				// 권한 요청
				const permissionStatus = await androidService.requestPermission();
				expect(permissionStatus).toBe('granted');

				// Tauri FCM API 호출 확인 (개발 환경이 아닌 경우)
				if (!import.meta.env.DEV) {
					expect(mockTauriPush.requestPermission).toHaveBeenCalled();
				}

				androidService.cleanup();
			});
		});

		describe('통합 푸시 서비스 매니저 연동', () => {
			it('푸시 서비스 매니저가 플랫폼에 따라 올바른 서비스를 선택해야 함', async () => {
				// 푸시 서비스 매니저 생성
				const manager = createPushServiceManager();

				// 초기화
				await manager.initialize();

				// 서비스 상태 확인
				const status = await manager.getServiceStatus();
				expect(status.isInitialized).toBe(true);
				expect(status.hasService).toBe(true);
				expect(['desktop', 'android', 'ios', 'web']).toContain(status.platform);

				await manager.cleanup();
			});

			it('푸시 서비스 매니저가 통합 디바이스 등록을 정상적으로 처리해야 함', async () => {
				// 토큰 설정
				await tokenService.storeTokens(validAccessToken, validRefreshToken);

				// 푸시 서비스 매니저 생성 및 초기화
				const manager = createPushServiceManager();
				await manager.initialize();

				// 통합 디바이스 등록
				await manager.registerDevice();

				// 등록 상태 확인
				const status = await manager.getServiceStatus();
				expect(status.registrationStatus).toBe('completed');

				await manager.cleanup();
			});

			it('푸시 서비스 매니저가 사용자 로그인/로그아웃을 정상적으로 처리해야 함', async () => {
				// 토큰 설정
				await tokenService.storeTokens(validAccessToken, validRefreshToken);

				// 푸시 서비스 매니저 생성 및 초기화
				const manager = createPushServiceManager();
				await manager.initialize();

				// 사용자 로그인 처리
				await manager.onUserLogin();

				// 세션 정보 저장 확인
				expect(global.localStorage.setItem).toHaveBeenCalledWith(
					'cnsprowms_push_session',
					expect.stringContaining('"isLoggedIn":true')
				);

				// 사용자 로그아웃 처리
				await manager.onUserLogout();

				// 세션 정보 업데이트 확인
				expect(global.localStorage.setItem).toHaveBeenCalledWith(
					'cnsprowms_push_session',
					expect.stringContaining('"isLoggedIn":false')
				);

				await manager.cleanup();
			});
		});
	});

	describe('알림 수신부터 IndexedDB 저장까지 전체 플로우 테스트', () => {
		beforeEach(async () => {
			await tokenService.initialize();
			await initNotificationDatabase();
		});

		it('데스크탑에서 Pusher Beams 알림 수신부터 IndexedDB 저장까지 전체 플로우가 정상적으로 동작해야 함', async () => {
			// 토큰 설정
			await tokenService.storeTokens(validAccessToken, validRefreshToken);

			// 데스크탑 푸시 서비스 생성 및 초기화
			const desktopService = createDesktopPushService();
			await desktopService.initialize();
			await desktopService.registerDevice();
			await desktopService.setUserId();

			// 알림 수신 콜백 등록
			let receivedNotification: NotificationData | null = null;
			desktopService.onNotificationReceived((notification) => {
				receivedNotification = notification;
			});

			// Pusher Beams 알림 수신 시뮬레이션
			await desktopService.handlePushNotification(testPusherNotification);

			// 콜백으로 알림 수신 확인
			expect(receivedNotification).toBeDefined();
			expect(receivedNotification!.content).toBe(testPusherNotification.body);
			expect(receivedNotification!.message).toBe(testPusherNotification.title);
			expect(receivedNotification!.priority).toBe(testPusherNotification.priority);
			expect(receivedNotification!.category).toBe(testPusherNotification.category);
			expect(receivedNotification!.image_url).toBe(testPusherNotification.image_url);
			expect(receivedNotification!.read).toBe(false);
			expect(receivedNotification!.success).toBe(true);

			// IndexedDB 저장 확인
			expect(mockIDBObjectStore.put).toHaveBeenCalled();

			// 저장된 데이터 형식 확인
			const saveCall = mockIDBObjectStore.put.mock.calls[0];
			const savedData = saveCall[0] as NotificationData;
			expect(savedData.id).toBeDefined();
			expect(savedData.content).toBe(testPusherNotification.body);
			expect(savedData.priority).toBe(testPusherNotification.priority);

			await desktopService.cleanup();
		});

		it('안드로이드에서 FCM 알림 수신부터 IndexedDB 저장까지 전체 플로우가 정상적으로 동작해야 함', async () => {
			// 토큰 설정
			await tokenService.storeTokens(validAccessToken, validRefreshToken);

			// 안드로이드 푸시 서비스 생성 및 초기화
			const androidService = createAndroidPushService();
			await androidService.initialize();
			await androidService.registerWithBeams();

			// 알림 수신 콜백 등록
			let receivedNotification: NotificationData | null = null;
			androidService.onNotificationReceived((notification) => {
				receivedNotification = notification;
			});

			// FCM 알림 수신 시뮬레이션 (내부 함수 직접 호출)
			const mockFcmNotification = {
				messageId: testPusherNotification.id,
				title: testPusherNotification.title,
				body: testPusherNotification.body,
				priority: testPusherNotification.priority,
				category: testPusherNotification.category,
				image_url: testPusherNotification.image_url,
				success: true
			};

			// 내부 알림 처리 함수 호출 (실제로는 Tauri FCM 이벤트에서 호출됨)
			const handleIncomingNotification = (androidService as any).handleIncomingNotification;
			if (handleIncomingNotification) {
				await handleIncomingNotification(mockFcmNotification);
			}

			// 콜백으로 알림 수신 확인
			expect(receivedNotification).toBeDefined();
			expect(receivedNotification!.content).toBe(testPusherNotification.body);
			expect(receivedNotification!.message).toBe(testPusherNotification.title);
			expect(receivedNotification!.priority).toBe(testPusherNotification.priority);

			// IndexedDB 저장 확인
			expect(mockIDBObjectStore.put).toHaveBeenCalled();

			androidService.cleanup();
		});

		it('통합 푸시 서비스 매니저를 통한 전체 플로우가 정상적으로 동작해야 함', async () => {
			// 토큰 설정
			await tokenService.storeTokens(validAccessToken, validRefreshToken);

			// 푸시 서비스 매니저 생성 및 초기화
			const manager = createPushServiceManager();
			await manager.initialize();
			await manager.registerDevice();
			await manager.onUserLogin();

			// 알림 수신 콜백 등록
			let receivedNotification: NotificationData | null = null;
			manager.onNotificationReceived((notification) => {
				receivedNotification = notification;
			});

			// 플랫폼별 서비스를 통한 알림 수신 시뮬레이션
			const currentService = manager.getCurrentService();
			expect(currentService).toBeDefined();

			// 플랫폼에 따른 알림 처리
			if (manager.getPlatform() === 'desktop') {
				const desktopService = currentService as any;
				if (desktopService.handlePushNotification) {
					await desktopService.handlePushNotification(testPusherNotification);
				}
			}

			// 알림 수신 확인 (플랫폼에 관계없이)
			if (receivedNotification) {
				expect(receivedNotification.content).toBe(testPusherNotification.body);
				expect(receivedNotification.priority).toBe(testPusherNotification.priority);
			}

			// IndexedDB 저장 확인
			expect(mockIDBObjectStore.put).toHaveBeenCalled();

			await manager.cleanup();
		});

		it('오프라인 상태에서 알림 수신 시 데이터 동기화가 정상적으로 동작해야 함', async () => {
			// 오프라인 상태 시뮬레이션
			Object.defineProperty(global.navigator, 'onLine', {
				writable: true,
				value: false
			});

			// 토큰 설정
			await tokenService.storeTokens(validAccessToken, validRefreshToken);

			// 데스크탑 푸시 서비스 생성 및 초기화
			const desktopService = createDesktopPushService();
			await desktopService.initialize();
			await desktopService.registerDevice();

			// 오프라인 상태에서 알림 수신
			await desktopService.handlePushNotification(testPusherNotification);

			// 오프라인 저장 처리 확인 (실제로는 데이터 동기화 핸들러에서 처리)
			// 여기서는 기본 IndexedDB 저장이 시도되는지 확인
			expect(mockIDBObjectStore.put).toHaveBeenCalled();

			// 온라인 상태로 복구
			Object.defineProperty(global.navigator, 'onLine', {
				writable: true,
				value: true
			});

			await desktopService.cleanup();
		});

		it('알림 처리 중 에러 발생 시 graceful degradation이 동작해야 함', async () => {
			// IndexedDB 에러 시뮬레이션
			mockIDBObjectStore.put.mockImplementation(() => {
				const request = { ...mockIDBRequest };
				setTimeout(() => {
					if (request.onerror) request.onerror({ target: request } as any);
				}, 0);
				return request as any;
			});

			// 토큰 설정
			await tokenService.storeTokens(validAccessToken, validRefreshToken);

			// 데스크탑 푸시 서비스 생성 및 초기화
			const desktopService = createDesktopPushService();
			await desktopService.initialize();
			await desktopService.registerDevice();

			// 에러 알림 콜백 등록
			let errorNotification: NotificationData | null = null;
			desktopService.onNotificationReceived((notification) => {
				if (!notification.success) {
					errorNotification = notification;
				}
			});

			// 알림 처리 (에러 발생)
			await desktopService.handlePushNotification(testPusherNotification);

			// 에러 처리 확인 (에러 알림이 콜백으로 전달되어야 함)
			expect(errorNotification).toBeDefined();
			expect(errorNotification!.success).toBe(false);
			expect(errorNotification!.content).toContain('오류가 발생했습니다');

			await desktopService.cleanup();
		});
	});

	describe('우선순위별 알림 처리 테스트', () => {
		beforeEach(async () => {
			await tokenService.initialize();
			await initNotificationDatabase();
		});

		it('긴급 우선순위 알림이 올바르게 처리되어야 함', async () => {
			// 토큰 설정
			await tokenService.storeTokens(validAccessToken, validRefreshToken);

			// 데스크탑 푸시 서비스 생성 및 초기화
			const desktopService = createDesktopPushService();
			await desktopService.initialize();
			await desktopService.registerDevice();

			// 긴급 알림 수신 콜백 등록
			let urgentNotificationReceived: NotificationData | null = null;
			desktopService.onNotificationReceived((notification) => {
				if (notification.priority === 'urgent') {
					urgentNotificationReceived = notification;
				}
			});

			// 긴급 알림 수신
			await desktopService.handlePushNotification(urgentNotification);

			// 긴급 알림 처리 확인
			expect(urgentNotificationReceived).toBeDefined();
			expect(urgentNotificationReceived!.priority).toBe('urgent');
			expect(urgentNotificationReceived!.category).toBe('emergency');
			expect(urgentNotificationReceived!.content).toBe(urgentNotification.body);

			// IndexedDB 저장 확인
			expect(mockIDBObjectStore.put).toHaveBeenCalled();

			// 저장된 데이터의 우선순위 확인
			const saveCall = mockIDBObjectStore.put.mock.calls[0];
			const savedData = saveCall[0] as NotificationData;
			expect(savedData.priority).toBe('urgent');

			await desktopService.cleanup();
		});

		it('일반 우선순위 알림이 올바르게 처리되어야 함', async () => {
			// 토큰 설정
			await tokenService.storeTokens(validAccessToken, validRefreshToken);

			// 데스크탑 푸시 서비스 생성 및 초기화
			const desktopService = createDesktopPushService();
			await desktopService.initialize();
			await desktopService.registerDevice();

			// 일반 알림 수신 콜백 등록
			let normalNotificationReceived: NotificationData | null = null;
			desktopService.onNotificationReceived((notification) => {
				if (notification.priority === 'normal') {
					normalNotificationReceived = notification;
				}
			});

			// 일반 알림 수신
			await desktopService.handlePushNotification(normalNotification);

			// 일반 알림 처리 확인
			expect(normalNotificationReceived).toBeDefined();
			expect(normalNotificationReceived!.priority).toBe('normal');
			expect(normalNotificationReceived!.category).toBe('general');
			expect(normalNotificationReceived!.content).toBe(normalNotification.body);

			// IndexedDB 저장 확인
			expect(mockIDBObjectStore.put).toHaveBeenCalled();

			await desktopService.cleanup();
		});

		it('우선순위별 알림 조회가 정상적으로 동작해야 함', async () => {
			// 우선순위별 알림 데이터 모킹
			const urgentNotifications = [
				{
					id: 'urgent-1',
					priority: 'urgent',
					content: '긴급 알림 1',
					created_at: new Date().toISOString(),
					read: false
				}
			];

			const normalNotifications = [
				{
					id: 'normal-1',
					priority: 'normal',
					content: '일반 알림 1',
					created_at: new Date().toISOString(),
					read: false
				}
			];

			// IndexedDB 인덱스 조회 모킹
			mockIDBIndex.getAll.mockImplementation((query) => {
				const request = { ...mockIDBRequest };
				setTimeout(() => {
					if (query === 'urgent') {
						request.result = urgentNotifications;
					} else if (query === 'normal') {
						request.result = normalNotifications;
					} else {
						request.result = [];
					}
					if (request.onsuccess) request.onsuccess({ target: request } as any);
				}, 0);
				return request as any;
			});

			// 우선순위별 알림 조회
			const urgentResults = await getNotificationsByPriority('urgent');
			const normalResults = await getNotificationsByPriority('normal');

			// 결과 확인
			expect(Array.isArray(urgentResults)).toBe(true);
			expect(Array.isArray(normalResults)).toBe(true);

			// 인덱스 사용 확인
			expect(mockIDBObjectStore.index).toHaveBeenCalledWith('priority');
			expect(mockIDBIndex.getAll).toHaveBeenCalledWith('urgent');
			expect(mockIDBIndex.getAll).toHaveBeenCalledWith('normal');
		});

		it('혼합된 우선순위 알림들이 올바르게 처리되어야 함', async () => {
			// 토큰 설정
			await tokenService.storeTokens(validAccessToken, validRefreshToken);

			// 푸시 서비스 매니저 생성 및 초기화
			const manager = createPushServiceManager();
			await manager.initialize();
			await manager.registerDevice();

			// 수신된 알림들을 저장할 배열
			const receivedNotifications: NotificationData[] = [];
			manager.onNotificationReceived((notification) => {
				receivedNotifications.push(notification);
			});

			// 다양한 우선순위의 알림들을 순차적으로 수신
			const currentService = manager.getCurrentService();
			if (currentService && manager.getPlatform() === 'desktop') {
				const desktopService = currentService as any;
				if (desktopService.handlePushNotification) {
					await desktopService.handlePushNotification(urgentNotification);
					await desktopService.handlePushNotification(normalNotification);
					await desktopService.handlePushNotification(testPusherNotification); // high priority
				}
			}

			// 모든 알림이 수신되었는지 확인
			expect(receivedNotifications.length).toBe(3);

			// 우선순위별 분류 확인
			const urgentNotifications = receivedNotifications.filter((n) => n.priority === 'urgent');
			const highNotifications = receivedNotifications.filter((n) => n.priority === 'high');
			const normalNotifications = receivedNotifications.filter((n) => n.priority === 'normal');

			expect(urgentNotifications.length).toBe(1);
			expect(highNotifications.length).toBe(1);
			expect(normalNotifications.length).toBe(1);

			// 각 우선순위별 내용 확인
			expect(urgentNotifications[0].content).toBe(urgentNotification.body);
			expect(highNotifications[0].content).toBe(testPusherNotification.body);
			expect(normalNotifications[0].content).toBe(normalNotification.body);

			// IndexedDB 저장 확인 (3번 저장되어야 함)
			expect(mockIDBObjectStore.put).toHaveBeenCalledTimes(3);

			await manager.cleanup();
		});

		it('우선순위별 시각적 구분 처리가 정상적으로 동작해야 함', async () => {
			// 토큰 설정
			await tokenService.storeTokens(validAccessToken, validRefreshToken);

			// 데스크탑 푸시 서비스 생성 및 초기화
			const desktopService = createDesktopPushService();
			await desktopService.initialize();
			await desktopService.registerDevice();

			// 포그라운드 상태 시뮬레이션
			Object.defineProperty(global.document, 'visibilityState', {
				writable: true,
				value: 'visible'
			});

			// 높은 우선순위 알림 수신
			await desktopService.handlePushNotification(urgentNotification);

			// 포그라운드 처리 로직이 실행되었는지 확인
			// (실제로는 console.log 호출을 확인하거나 UI 업데이트를 확인)
			expect(global.console.log).toHaveBeenCalledWith(
				expect.stringContaining('포그라운드 알림 처리')
			);
			expect(global.console.log).toHaveBeenCalledWith(
				expect.stringContaining('높은 우선순위 알림 - 추가 강조 표시')
			);

			// 백그라운드 상태 시뮬레이션
			Object.defineProperty(global.document, 'visibilityState', {
				writable: true,
				value: 'hidden'
			});

			// 일반 우선순위 알림 수신
			await desktopService.handlePushNotification(normalNotification);

			// 백그라운드 처리 로직이 실행되었는지 확인
			expect(global.console.log).toHaveBeenCalledWith(
				expect.stringContaining('백그라운드 알림 처리')
			);

			await desktopService.cleanup();
		});
	});

	describe('성능 및 안정성 테스트', () => {
		beforeEach(async () => {
			await tokenService.initialize();
			await initNotificationDatabase();
		});

		it('대량 알림 처리 시 성능이 요구사항을 충족해야 함', async () => {
			// 토큰 설정
			await tokenService.storeTokens(validAccessToken, validRefreshToken);

			// 푸시 서비스 매니저 생성 및 초기화
			const manager = createPushServiceManager();
			await manager.initialize();
			await manager.registerDevice();

			// 성능 측정 시작
			const startTime = Date.now();

			// 100개의 알림을 순차적으로 처리
			const notifications: PusherBeamsNotificationData[] = [];
			for (let i = 0; i < 100; i++) {
				notifications.push({
					id: `perf-test-${i}`,
					title: `성능 테스트 알림 ${i}`,
					body: `성능 테스트를 위한 알림 ${i}번입니다.`,
					priority: i % 4 === 0 ? 'urgent' : i % 3 === 0 ? 'high' : 'normal',
					category: 'performance_test'
				});
			}

			// 알림 처리
			const currentService = manager.getCurrentService();
			if (currentService && manager.getPlatform() === 'desktop') {
				const desktopService = currentService as any;
				if (desktopService.handlePushNotification) {
					for (const notification of notifications) {
						await desktopService.handlePushNotification(notification);
					}
				}
			}

			// 성능 측정 완료
			const processingTime = Date.now() - startTime;

			// 성능 요구사항 확인 (100개 알림 처리 시 5초 이내)
			expect(processingTime).toBeLessThan(5000);

			// IndexedDB 저장 확인
			expect(mockIDBObjectStore.put).toHaveBeenCalledTimes(100);

			console.log(`[성능 테스트] 100개 알림 처리 시간: ${processingTime}ms`);

			await manager.cleanup();
		});

		it('메모리 사용량이 최적화되어야 함', async () => {
			// 토큰 설정
			await tokenService.storeTokens(validAccessToken, validRefreshToken);

			// 푸시 서비스 매니저 생성 및 초기화
			const manager = createPushServiceManager();
			await manager.initialize();

			// 성능 통계 확인
			const performanceStats = manager.getPerformanceStats();
			expect(performanceStats).toBeDefined();
			expect(performanceStats.cache).toBeDefined();
			expect(performanceStats.memory).toBeDefined();

			// 수동 최적화 실행
			await manager.performManualOptimization();

			// 캐시 정리
			manager.clearAllCaches();

			// 메모리 사용량이 합리적인 범위 내에 있는지 확인
			const memoryReport = performanceStats.memory;
			if (memoryReport && memoryReport.estimatedMemoryUsage) {
				// 50MB 이하여야 함
				expect(memoryReport.estimatedMemoryUsage).toBeLessThan(50 * 1024 * 1024);
			}

			await manager.cleanup();
		});

		it('연결 실패 시 재시도 메커니즘이 정상적으로 동작해야 함', async () => {
			// Pusher Beams 연결 실패 시뮬레이션
			mockPusherClient.start.mockRejectedValueOnce(new Error('Connection failed'));
			mockPusherClient.start.mockResolvedValueOnce(undefined); // 두 번째 시도는 성공

			// 토큰 설정
			await tokenService.storeTokens(validAccessToken, validRefreshToken);

			// 데스크탑 푸시 서비스 생성 및 초기화
			const desktopService = createDesktopPushService();
			await desktopService.initialize();

			// 디바이스 등록 (첫 번째 시도는 실패, 두 번째 시도는 성공해야 함)
			const deviceId = await desktopService.registerDevice();

			// 재시도 후 성공 확인
			expect(deviceId).toBeDefined();
			expect(mockPusherClient.start).toHaveBeenCalledTimes(1); // 실제로는 재시도 로직에 따라 다를 수 있음

			await desktopService.cleanup();
		});

		it('토큰 만료 시 자동 갱신이 정상적으로 동작해야 함', async () => {
			// 만료 임박한 토큰 설정 (실제로는 tokenService에서 처리)
			await tokenService.storeTokens(validAccessToken, validRefreshToken);

			// 푸시 서비스 매니저 생성 및 초기화
			const manager = createPushServiceManager();
			await manager.initialize();
			await manager.registerDevice();

			// 사용자 로그인 (토큰 갱신 로직 포함)
			await manager.onUserLogin();

			// 자동 재연결 시도
			await manager.attemptAutoReconnect();

			// 서비스 상태 확인
			const status = await manager.getServiceStatus();
			expect(status.isInitialized).toBe(true);
			expect(status.registrationStatus).toBe('completed');

			await manager.cleanup();
		});
	});
});
