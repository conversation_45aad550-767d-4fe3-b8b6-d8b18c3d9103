/**
 * Pusher Beams 테스트 헬퍼
 * 실제 환경에서 푸시 서비스를 테스트하기 위한 유틸리티 함수들
 */

import { getPushServiceManager } from '$lib/services/pushServiceManager';
import { getCurrentPlatform } from '$lib/services/platformService';
import { tokenService } from '$lib/services/tokenService';
import {
	getAllNotifications,
	getUnreadNotificationCount,
	markNotificationAsRead,
	deleteNotification
} from '$lib/utils/notification-database';
import type { NotificationData } from '$lib/types/notification';

export interface TestResult {
	success: boolean;
	message: string;
	data?: any;
	error?: Error;
}

export interface E2ETestSuite {
	platform: string;
	results: TestResult[];
	summary: {
		total: number;
		passed: number;
		failed: number;
		duration: number;
	};
}

/**
 * 푸시 서비스 E2E 테스트 헬퍼 클래스
 */
export const pushServiceTestHelper = {
	/**
	 * 전체 E2E 테스트 실행
	 */
	async runFullE2ETest(): Promise<E2ETestSuite> {
		const startTime = Date.now();
		const platform = getCurrentPlatform();
		const results: TestResult[] = [];

		console.log(`🚀 ${platform} 플랫폼에서 E2E 테스트 시작`);

		// 1. 기본 인프라 테스트
		results.push(await this.testPlatformService());
		results.push(await this.testTokenService());
		results.push(await this.testNotificationDatabase());

		// 2. 푸시 서비스 초기화 테스트
		results.push(await this.testPushServiceInitialization());

		// 3. 디바이스 등록 테스트
		results.push(await this.testDeviceRegistration());

		// 4. 사용자 세션 관리 테스트
		results.push(await this.testUserSessionManagement());

		// 5. 알림 처리 테스트
		results.push(await this.testNotificationHandling());

		// 6. 에러 처리 테스트
		results.push(await this.testErrorHandling());

		const endTime = Date.now();
		const duration = endTime - startTime;

		const passed = results.filter((r) => r.success).length;
		const failed = results.length - passed;

		const summary = {
			total: results.length,
			passed,
			failed,
			duration
		};

		console.log(`✅ E2E 테스트 완료: ${passed}/${results.length} 통과 (${duration}ms)`);

		return {
			platform,
			results,
			summary
		};
	},

	/**
	 * 플랫폼 서비스 테스트
	 */
	async testPlatformService(): Promise<TestResult> {
		try {
			const platform = getCurrentPlatform();

			if (!platform || !['desktop', 'android', 'web'].includes(platform)) {
				throw new Error(`지원되지 않는 플랫폼: ${platform}`);
			}

			return {
				success: true,
				message: `플랫폼 서비스 테스트 통과: ${platform}`,
				data: { platform }
			};
		} catch (error) {
			return {
				success: false,
				message: '플랫폼 서비스 테스트 실패',
				error: error as Error
			};
		}
	},

	/**
	 * 토큰 서비스 테스트
	 */
	async testTokenService(): Promise<TestResult> {
		try {
			const isAuthenticated = await tokenService.isAuthenticated();

			if (!isAuthenticated) {
				return {
					success: false,
					message: '사용자가 인증되지 않음 - 로그인이 필요합니다'
				};
			}

			const token = await tokenService.getAccessToken();
			const userId = tokenService.getCurrentUserId();

			if (!token) {
				throw new Error('JWT 토큰을 가져올 수 없습니다');
			}

			if (!userId) {
				throw new Error('사용자 ID를 가져올 수 없습니다');
			}

			return {
				success: true,
				message: '토큰 서비스 테스트 통과',
				data: { hasToken: !!token, userId }
			};
		} catch (error) {
			return {
				success: false,
				message: '토큰 서비스 테스트 실패',
				error: error as Error
			};
		}
	},

	/**
	 * 알림 데이터베이스 테스트
	 */
	async testNotificationDatabase(): Promise<TestResult> {
		try {
			// 기존 알림 조회 테스트
			const notifications = await getAllNotifications();
			const unreadCount = await getUnreadNotificationCount();

			// 테스트 알림 생성 및 저장
			const testNotification: NotificationData = {
				id: `test-${Date.now()}`,
				content: 'E2E 테스트 알림',
				expire_day: new Date(Date.now() + 86400000).toISOString(),
				created_at: new Date().toISOString(),
				read: false,
				priority: 'normal',
				message: 'E2E 테스트',
				success: true
			};

			// 실제로는 saveNotification을 직접 호출하지 않고
			// 푸시 서비스를 통해 저장되는지 확인
			return {
				success: true,
				message: '알림 데이터베이스 테스트 통과',
				data: {
					existingNotifications: notifications.length,
					unreadCount
				}
			};
		} catch (error) {
			return {
				success: false,
				message: '알림 데이터베이스 테스트 실패',
				error: error as Error
			};
		}
	},

	/**
	 * 푸시 서비스 초기화 테스트
	 */
	async testPushServiceInitialization(): Promise<TestResult> {
		try {
			await pushServiceManager.initialize();

			const status = await pushServiceManager.getServiceStatus();

			return {
				success: true,
				message: '푸시 서비스 초기화 테스트 통과',
				data: status
			};
		} catch (error) {
			return {
				success: false,
				message: '푸시 서비스 초기화 테스트 실패',
				error: error as Error
			};
		}
	},

	/**
	 * 디바이스 등록 테스트
	 */
	async testDeviceRegistration(): Promise<TestResult> {
		try {
			const startTime = Date.now();

			await pushServiceManager.registerDevice();

			const endTime = Date.now();
			const registrationTime = endTime - startTime;

			// 5초 이내 등록 완료 확인 (요구사항)
			if (registrationTime > 5000) {
				return {
					success: false,
					message: `디바이스 등록 시간 초과: ${registrationTime}ms`
				};
			}

			return {
				success: true,
				message: '디바이스 등록 테스트 통과',
				data: { registrationTime }
			};
		} catch (error) {
			return {
				success: false,
				message: '디바이스 등록 테스트 실패',
				error: error as Error
			};
		}
	},

	/**
	 * 사용자 세션 관리 테스트
	 */
	async testUserSessionManagement(): Promise<TestResult> {
		try {
			// 로그인 처리 테스트
			await pushServiceManager.onUserLogin();

			// 로그아웃 처리 테스트
			await pushServiceManager.onUserLogout();

			// 다시 로그인 처리
			await pushServiceManager.onUserLogin();

			return {
				success: true,
				message: '사용자 세션 관리 테스트 통과'
			};
		} catch (error) {
			return {
				success: false,
				message: '사용자 세션 관리 테스트 실패',
				error: error as Error
			};
		}
	},

	/**
	 * 알림 처리 테스트
	 */
	async testNotificationHandling(): Promise<TestResult> {
		try {
			// 알림 수신 콜백 등록
			let receivedNotification: NotificationData | null = null;

			const pushServiceManager = getPushServiceManager();
			pushServiceManager.onNotificationReceived(async (notification: any) => {
				receivedNotification = notification;
				console.log('테스트 알림 수신:', notification);
			});

			// 실제 알림 수신을 기다리는 대신,
			// 기존 알림들로 처리 기능 테스트
			const notifications = await getAllNotifications();

			if (notifications.length > 0) {
				const testNotification = notifications[0];

				// 읽음 처리 테스트
				if (!testNotification.read) {
					await markNotificationAsRead(testNotification.id);
				}
			}

			return {
				success: true,
				message: '알림 처리 테스트 통과',
				data: { notificationCount: notifications.length }
			};
		} catch (error) {
			return {
				success: false,
				message: '알림 처리 테스트 실패',
				error: error as Error
			};
		}
	},

	/**
	 * 에러 처리 테스트
	 */
	async testErrorHandling(): Promise<TestResult> {
		try {
			// 서비스 상태 확인으로 에러 처리 테스트
			const status = await pushServiceManager.getServiceStatus();

			// 권한이 거부된 경우 graceful degradation 확인
			if (status.permissionStatus === 'denied') {
				console.log('푸시 알림 권한이 거부됨 - graceful degradation 적용');
			}

			return {
				success: true,
				message: '에러 처리 테스트 통과',
				data: status
			};
		} catch (error) {
			// 에러가 발생해도 적절히 처리되는지 확인
			return {
				success: true,
				message: '에러 처리 테스트 통과 (예상된 에러 처리됨)',
				data: { handledError: (error as Error).message }
			};
		}
	},

	/**
	 * 성능 테스트
	 */
	async testPerformance(): Promise<TestResult> {
		try {
			const startTime = Date.now();

			// 여러 작업을 동시에 실행하여 성능 측정
			await Promise.all([
				getAllNotifications(),
				getUnreadNotificationCount(),
				pushServiceManager.getServiceStatus()
			]);

			const endTime = Date.now();
			const totalTime = endTime - startTime;

			// 1초 이내 처리 확인 (요구사항)
			if (totalTime > 1000) {
				return {
					success: false,
					message: `성능 테스트 실패: ${totalTime}ms (1초 초과)`
				};
			}

			return {
				success: true,
				message: '성능 테스트 통과',
				data: { processingTime: totalTime }
			};
		} catch (error) {
			return {
				success: false,
				message: '성능 테스트 실패',
				error: error as Error
			};
		}
	},

	/**
	 * 테스트 결과 출력
	 */
	printTestResults(testSuite: E2ETestSuite): void {
		console.log('\n=== E2E 테스트 결과 ===');
		console.log(`플랫폼: ${testSuite.platform}`);
		console.log(`총 테스트: ${testSuite.summary.total}`);
		console.log(`통과: ${testSuite.summary.passed}`);
		console.log(`실패: ${testSuite.summary.failed}`);
		console.log(`소요 시간: ${testSuite.summary.duration}ms`);
		console.log('\n=== 상세 결과 ===');

		testSuite.results.forEach((result, index) => {
			const status = result.success ? '✅' : '❌';
			console.log(`${index + 1}. ${status} ${result.message}`);

			if (result.data) {
				console.log(`   데이터:`, result.data);
			}

			if (result.error) {
				console.log(`   에러:`, result.error.message);
			}
		});
	}
};

/**
 * 브라우저 콘솔에서 실행할 수 있는 테스트 함수
 */
export async function runPushServiceE2ETest(): Promise<void> {
	try {
		console.log('🧪 Pusher Beams E2E 테스트 시작...');

		const testSuite = await pushServiceTestHelper.runFullE2ETest();
		pushServiceTestHelper.printTestResults(testSuite);

		// 전역 객체에 결과 저장 (디버깅용)
		(window as any).lastE2ETestResult = testSuite;

		console.log('\n💡 테스트 결과는 window.lastE2ETestResult에서 확인할 수 있습니다.');
	} catch (error) {
		console.error('❌ E2E 테스트 실행 중 오류 발생:', error);
	}
}

// 전역 함수로 등록 (개발 환경에서만)
if (typeof window !== 'undefined' && import.meta.env.DEV) {
	(window as any).runPushServiceE2ETest = runPushServiceE2ETest;
	console.log(
		'💡 개발 모드: window.runPushServiceE2ETest() 함수를 사용하여 E2E 테스트를 실행할 수 있습니다.'
	);
}
