/**
 * E2E 테스트 실행 스크립트
 * 실제 환경에서 푸시 서비스 E2E 테스트를 실행합니다.
 */

import { pushServiceTestHelper, type E2ETestSuite } from './helpers/push-service-test-helper';
import { getCurrentPlatform } from '$lib/services/platformService';

/**
 * 메인 E2E 테스트 실행 함수
 */
export async function runE2ETests(): Promise<E2ETestSuite> {
	console.log('🚀 Pusher Beams E2E 테스트 시작...');
	console.log(`📱 현재 플랫폼: ${getCurrentPlatform()}`);

	try {
		const testSuite = await pushServiceTestHelper.runFullE2ETest();

		// 결과 출력
		pushServiceTestHelper.printTestResults(testSuite);

		// 성공/실패에 따른 종료 코드 설정
		if (testSuite.summary.failed > 0) {
			console.error(`❌ ${testSuite.summary.failed}개 테스트 실패`);
			process.exit(1);
		} else {
			console.log(`✅ 모든 테스트 통과 (${testSuite.summary.passed}/${testSuite.summary.total})`);
		}

		return testSuite;
	} catch (error) {
		console.error('❌ E2E 테스트 실행 중 치명적 오류 발생:', error);
		process.exit(1);
	}
}

/**
 * 특정 플랫폼 테스트만 실행
 */
export async function runPlatformSpecificTests(platform: 'desktop' | 'android'): Promise<void> {
	console.log(`🎯 ${platform} 플랫폼 특화 테스트 실행...`);

	const currentPlatform = getCurrentPlatform();
	if (currentPlatform !== platform) {
		console.warn(`⚠️  현재 플랫폼(${currentPlatform})과 요청된 플랫폼(${platform})이 다릅니다.`);
		console.warn('일부 테스트가 정확하지 않을 수 있습니다.');
	}

	try {
		if (platform === 'desktop') {
			await runDesktopSpecificTests();
		} else if (platform === 'android') {
			await runAndroidSpecificTests();
		}

		console.log(`✅ ${platform} 플랫폼 특화 테스트 완료`);
	} catch (error) {
		console.error(`❌ ${platform} 플랫폼 테스트 실패:`, error);
		throw error;
	}
}

/**
 * 데스크탑 특화 테스트
 */
async function runDesktopSpecificTests(): Promise<void> {
	console.log('🖥️  데스크탑 특화 테스트 실행...');

	// Service Worker 지원 확인
	if ('serviceWorker' in navigator) {
		console.log('✅ Service Worker 지원됨');

		try {
			const registrations = await navigator.serviceWorker.getRegistrations();
			console.log(`📋 등록된 Service Worker: ${registrations.length}개`);

			for (const registration of registrations) {
				console.log(`   - Scope: ${registration.scope}`);
				console.log(`   - State: ${registration.active?.state || 'inactive'}`);
			}
		} catch (error) {
			console.warn('⚠️  Service Worker 정보 조회 실패:', error);
		}
	} else {
		console.warn('⚠️  Service Worker가 지원되지 않습니다.');
	}

	// Notification API 지원 확인
	if ('Notification' in window) {
		console.log('✅ Notification API 지원됨');
		console.log(`📋 현재 권한 상태: ${Notification.permission}`);

		if (Notification.permission === 'default') {
			console.log('💡 알림 권한을 요청하려면 사용자 상호작용이 필요합니다.');
		}
	} else {
		console.warn('⚠️  Notification API가 지원되지 않습니다.');
	}

	// Push API 지원 확인
	if ('PushManager' in window) {
		console.log('✅ Push API 지원됨');
	} else {
		console.warn('⚠️  Push API가 지원되지 않습니다.');
	}
}

/**
 * 안드로이드 특화 테스트
 */
async function runAndroidSpecificTests(): Promise<void> {
	console.log('📱 안드로이드 특화 테스트 실행...');

	// Tauri 환경 확인
	if (typeof window !== 'undefined' && (window as any).__TAURI__) {
		console.log('✅ Tauri 환경 감지됨');

		try {
			const { invoke } = (window as any).__TAURI__.core;

			// FCM 플러그인 사용 가능 여부 확인
			try {
				await invoke('plugin:remote-push|check_permission');
				console.log('✅ FCM 플러그인 사용 가능');
			} catch (error) {
				console.warn('⚠️  FCM 플러그인 사용 불가:', error);
			}
		} catch (error) {
			console.warn('⚠️  Tauri API 접근 실패:', error);
		}
	} else {
		console.warn('⚠️  Tauri 환경이 아닙니다.');
	}

	// 안드로이드 특화 기능 확인
	const userAgent = navigator.userAgent;
	if (userAgent.includes('Android')) {
		console.log('✅ 안드로이드 환경 감지됨');
		console.log(`📋 User Agent: ${userAgent}`);
	} else {
		console.warn('⚠️  안드로이드 환경이 아닙니다.');
	}
}

/**
 * 성능 테스트 실행
 */
export async function runPerformanceTests(): Promise<void> {
	console.log('⚡ 성능 테스트 실행...');

	try {
		const result = await pushServiceTestHelper.testPerformance();

		if (result.success) {
			console.log(`✅ 성능 테스트 통과: ${result.data?.processingTime}ms`);
		} else {
			console.error(`❌ 성능 테스트 실패: ${result.message}`);
		}
	} catch (error) {
		console.error('❌ 성능 테스트 실행 중 오류:', error);
	}
}

/**
 * 연결성 테스트 실행
 */
export async function runConnectivityTests(): Promise<void> {
	console.log('🌐 연결성 테스트 실행...');

	// 네트워크 상태 확인
	if ('navigator' in window && 'onLine' in navigator) {
		console.log(`📡 네트워크 상태: ${navigator.onLine ? '온라인' : '오프라인'}`);

		if (!navigator.onLine) {
			console.warn('⚠️  오프라인 상태입니다. 일부 테스트가 실패할 수 있습니다.');
		}
	}

	// Pusher Beams 서비스 연결 테스트
	try {
		// 실제 Pusher Beams 엔드포인트에 연결 테스트
		const instanceId = import.meta.env.VITE_PUSHER_BEAMS_INSTANCE_ID;

		if (instanceId) {
			console.log(`🔗 Pusher Beams Instance ID: ${instanceId}`);

			// 간단한 연결 테스트 (실제 API 호출 없이)
			const beamsUrl = `https://${instanceId}.pushnotifications.pusher.com`;
			console.log(`📡 Pusher Beams URL: ${beamsUrl}`);
		} else {
			console.warn('⚠️  VITE_PUSHER_BEAMS_INSTANCE_ID가 설정되지 않았습니다.');
		}
	} catch (error) {
		console.error('❌ Pusher Beams 연결 테스트 실패:', error);
	}
}

/**
 * 전체 테스트 스위트 실행
 */
export async function runFullTestSuite(): Promise<void> {
	console.log('🧪 전체 E2E 테스트 스위트 실행...');
	console.log('='.repeat(50));

	const startTime = Date.now();

	try {
		// 1. 기본 E2E 테스트
		console.log('\n1️⃣  기본 E2E 테스트');
		await runE2ETests();

		// 2. 플랫폼별 특화 테스트
		console.log('\n2️⃣  플랫폼별 특화 테스트');
		const platform = getCurrentPlatform();
		if (platform === 'desktop' || platform === 'android') {
			await runPlatformSpecificTests(platform as 'desktop' | 'android');
		}

		// 3. 성능 테스트
		console.log('\n3️⃣  성능 테스트');
		await runPerformanceTests();

		// 4. 연결성 테스트
		console.log('\n4️⃣  연결성 테스트');
		await runConnectivityTests();

		const endTime = Date.now();
		const totalTime = endTime - startTime;

		console.log('\n' + '='.repeat(50));
		console.log(`🎉 전체 테스트 스위트 완료! (${totalTime}ms)`);
	} catch (error) {
		console.error('\n❌ 테스트 스위트 실행 중 오류 발생:', error);
		throw error;
	}
}

// CLI에서 직접 실행할 수 있도록 설정
if (import.meta.env.MODE === 'test' && typeof process !== 'undefined') {
	const args = process.argv.slice(2);
	const command = args[0];

	switch (command) {
		case 'full':
			runFullTestSuite().catch(console.error);
			break;
		case 'desktop':
			runPlatformSpecificTests('desktop').catch(console.error);
			break;
		case 'android':
			runPlatformSpecificTests('android').catch(console.error);
			break;
		case 'performance':
			runPerformanceTests().catch(console.error);
			break;
		case 'connectivity':
			runConnectivityTests().catch(console.error);
			break;
		default:
			runE2ETests().catch(console.error);
	}
}

// 브라우저 환경에서 전역 함수로 등록
if (typeof window !== 'undefined' && import.meta.env.DEV) {
	(window as any).runPushE2ETests = {
		full: runFullTestSuite,
		basic: runE2ETests,
		desktop: () => runPlatformSpecificTests('desktop'),
		android: () => runPlatformSpecificTests('android'),
		performance: runPerformanceTests,
		connectivity: runConnectivityTests
	};

	console.log(
		'💡 개발 모드: window.runPushE2ETests 객체를 사용하여 다양한 E2E 테스트를 실행할 수 있습니다.'
	);
	console.log('   - window.runPushE2ETests.full() : 전체 테스트 스위트');
	console.log('   - window.runPushE2ETests.basic() : 기본 E2E 테스트');
	console.log('   - window.runPushE2ETests.desktop() : 데스크탑 특화 테스트');
	console.log('   - window.runPushE2ETests.android() : 안드로이드 특화 테스트');
	console.log('   - window.runPushE2ETests.performance() : 성능 테스트');
	console.log('   - window.runPushE2ETests.connectivity() : 연결성 테스트');
}
