/**
 * Pusher Beams 플랫폼별 E2E 테스트 (9.3)
 *
 * 이 테스트는 플랫폼별 End-to-End 테스트를 수행합니다:
 * - 데스크탑에서 Web SDK 테스트
 * - 안드로이드에서 FCM 테스트
 * - 다양한 시나리오 (로그인/로그아웃, 우선순위별 알림) 테스트
 *
 * Requirements: 모든 기능 요구사항
 */

import { describe, it, expect, beforeEach, afterEach, vi, beforeAll, afterAll } from 'vitest';

// 플랫폼 서비스 모킹
vi.mock('$lib/services/platformService', () => ({
	getCurrentPlatform: vi.fn(),
	isDesktop: vi.fn(),
	isAndroid: vi.fn(),
	isTauri: vi.fn(),
	getStorageType: vi.fn(),
	supportsFeature: vi.fn()
}));

// Pusher Beams Web SDK 모킹
const mockPusherClient = {
	start: vi.fn(),
	stop: vi.fn(),
	setUserId: vi.fn(),
	clearAllState: vi.fn(),
	getDeviceId: vi.fn(),
	getRegistrationState: vi.fn()
};

vi.mock('@pusher/push-notifications-web', () => ({
	Client: vi.fn().mockImplementation(() => mockPusherClient)
}));

// Tauri FCM API 모킹
vi.mock('tauri-plugin-remote-push-api', () => ({
	getToken: vi.fn(),
	requestPermission: vi.fn(),
	onNotificationReceived: vi.fn(),
	onTokenRefresh: vi.fn()
}));

// Tauri Store 모킹
const mockTauriStore = {
	get: vi.fn(),
	set: vi.fn(),
	save: vi.fn(),
	load: vi.fn(),
	clear: vi.fn(),
	delete: vi.fn()
};

vi.mock('@tauri-apps/plugin-store', () => ({
	Store: vi.fn().mockImplementation(() => mockTauriStore)
}));

// 서비스 및 유틸리티 임포트
import { createDesktopPushService } from '$lib/services/desktopPushService';
import { createAndroidPushService } from '$lib/services/androidPushService';
import { createPushServiceManager } from '$lib/services/pushServiceManager';
import { tokenService } from '$lib/services/tokenService';
import {
	saveNotification,
	getAllNotifications,
	getNotificationsByPriority,
	getUnreadNotificationCount,
	markNotificationAsRead,
	initNotificationDatabase
} from '$lib/utils/notification-database';
import { closeEmployeeDB } from '$lib/utils/indexeddb-utils';

import type { NotificationData } from '$lib/types/notification';
import type { PusherBeamsNotificationData } from '$lib/types/pushNotificationTypes';

// IndexedDB 모킹
const mockIndexedDB = {
	open: vi.fn(),
	deleteDatabase: vi.fn()
};

const mockIDBDatabase = {
	createObjectStore: vi.fn(),
	transaction: vi.fn(),
	close: vi.fn(),
	version: 3,
	name: 'EmployeeDB',
	objectStoreNames: ['push_notifications', 'print_settings']
};

const mockIDBTransaction = {
	objectStore: vi.fn(),
	oncomplete: null,
	onerror: null,
	onabort: null
};

const mockIDBObjectStore = {
	add: vi.fn(),
	put: vi.fn(),
	get: vi.fn(),
	delete: vi.fn(),
	getAll: vi.fn(),
	index: vi.fn(),
	createIndex: vi.fn()
};

const mockIDBRequest = {
	onsuccess: null,
	onerror: null,
	result: null
};

const mockIDBIndex = {
	getAll: vi.fn()
};

// 전역 IndexedDB 모킹
global.indexedDB = mockIndexedDB as any;
global.IDBKeyRange = {
	bound: vi.fn(),
	only: vi.fn(),
	lowerBound: vi.fn(),
	upperBound: vi.fn()
} as any;

// Service Worker 모킹
const mockServiceWorkerRegistration = {
	unregister: vi.fn(),
	addEventListener: vi.fn(),
	scope: '/service-worker.js'
};

const mockServiceWorker = {
	register: vi.fn(),
	addEventListener: vi.fn()
};

// 브라우저 API 모킹
global.navigator = {
	serviceWorker: mockServiceWorker,
	onLine: true,
	userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
} as any;

global.Notific