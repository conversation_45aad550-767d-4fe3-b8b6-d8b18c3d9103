/**
 * Pusher Beams 기존 인프라 통합 테스트 (9.1)
 *
 * 이 테스트는 Pusher Beams 구현이 기존 인프라와 올바르게 통합되는지 검증합니다:
 * - 기존 platformService 연동 테스트
 * - 기존 tokenService 연동 테스트
 * - 기존 notification-database 연동 테스트
 * - 기존 EmployeeDB 호환성 테스트
 *
 * Requirements: 모든 기능 요구사항
 */

import { describe, it, expect, beforeEach, afterEach, vi, beforeAll, afterAll } from 'vitest';

// 기존 인프라 서비스들
import {
	getCurrentPlatform,
	isDesktop,
	isAndroid,
	isTauri,
	getStorageType,
	supportsFeature
} from '$lib/services/platformService';

import { tokenService } from '$lib/services/tokenService';

import {
	initNotificationDatabase,
	saveNotification,
	saveReceivedNotification,
	getAllNotifications,
	getActiveNotifications,
	getNotification,
	deleteNotification,
	getUnreadNotificationCount,
	markNotificationAsRead,
	markAllNotificationsAsRead,
	getNotificationsByPriority,
	getNotificationsByCategory,
	notificationManager
} from '$lib/utils/notification-database';

import { initEmployeeDB, closeEmployeeDB } from '$lib/utils/indexeddb-utils';

import type { NotificationData } from '$lib/types/notification';
import type { PusherBeamsNotificationData } from '$lib/types/pushNotificationTypes';

// Tauri API 모킹
const mockTauriStore = {
	get: vi.fn(),
	set: vi.fn(),
	save: vi.fn(),
	load: vi.fn(),
	clear: vi.fn(),
	delete: vi.fn(),
	entries: vi.fn(),
	keys: vi.fn(),
	values: vi.fn(),
	length: vi.fn(),
	reset: vi.fn()
};

vi.mock('@tauri-apps/plugin-store', () => ({
	Store: vi.fn().mockImplementation(() => mockTauriStore)
}));

// IndexedDB 모킹
const mockIndexedDB = {
	open: vi.fn(),
	deleteDatabase: vi.fn()
};

const mockIDBDatabase = {
	createObjectStore: vi.fn(),
	transaction: vi.fn(),
	close: vi.fn(),
	version: 3,
	name: 'EmployeeDB',
	objectStoreNames: ['push_notifications', 'print_settings']
};

const mockIDBTransaction = {
	objectStore: vi.fn(),
	oncomplete: null,
	onerror: null,
	onabort: null
};

const mockIDBObjectStore = {
	add: vi.fn(),
	put: vi.fn(),
	get: vi.fn(),
	delete: vi.fn(),
	getAll: vi.fn(),
	index: vi.fn(),
	createIndex: vi.fn()
};

const mockIDBRequest = {
	onsuccess: null,
	onerror: null,
	result: null
};

const mockIDBIndex = {
	getAll: vi.fn()
};

// 전역 IndexedDB 모킹
global.indexedDB = mockIndexedDB as any;
global.IDBKeyRange = {
	bound: vi.fn(),
	only: vi.fn(),
	lowerBound: vi.fn(),
	upperBound: vi.fn()
} as any;

describe('Pusher Beams 기존 인프라 통합 테스트 (9.1)', () => {
	// 테스트용 데이터
	const testNotificationData: NotificationData = {
		id: 'test-notification-1',
		content: '테스트 알림 내용',
		expire_day: '2025-12-31',
		created_at: new Date().toISOString(),
		read: false,
		success: true,
		message: '테스트 알림',
		priority: 'normal',
		category: 'general',
		image_url: 'https://example.com/image.jpg',
		deletable: true
	};

	const testPusherNotification: PusherBeamsNotificationData = {
		id: 'pusher-notification-1',
		title: 'Pusher 테스트 알림',
		body: 'Pusher Beams에서 전송된 테스트 알림입니다.',
		priority: 'high',
		category: 'urgent',
		image_url: 'https://example.com/pusher-image.jpg',
		expire_at: '2025-12-31T23:59:59Z',
		deletable: true
	};

	const validAccessToken =
		'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJzdWIiOiIxIiwiaWF0IjoxNzA0MDY3MjAwLCJleHAiOjk5OTk5OTk5OTksInR5cGUiOiJhY2Nlc3MifQ.valid-access-token';
	const validRefreshToken =
		'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJzdWIiOiIxIiwiaWF0IjoxNzA0MDY3MjAwLCJleHAiOjk5OTk5OTk5OTksInR5cGUiOiJyZWZyZXNoIn0.valid-refresh-token';

	beforeAll(async () => {
		// 전역 설정
		global.console.log = vi.fn();
		global.console.error = vi.fn();
		global.console.warn = vi.fn();
	});

	beforeEach(() => {
		vi.clearAllMocks();

		// Tauri Store 모킹 초기화
		mockTauriStore.get.mockResolvedValue(null);
		mockTauriStore.set.mockResolvedValue(undefined);
		mockTauriStore.save.mockResolvedValue(undefined);
		mockTauriStore.load.mockResolvedValue(undefined);
		mockTauriStore.clear.mockResolvedValue(undefined);
		mockTauriStore.delete.mockResolvedValue(undefined);

		// IndexedDB 모킹 초기화
		mockIndexedDB.open.mockImplementation(() => {
			const request = { ...mockIDBRequest };
			setTimeout(() => {
				request.result = mockIDBDatabase;
				if (request.onsuccess) request.onsuccess({ target: request } as any);
			}, 0);
			return request as any;
		});

		mockIDBDatabase.transaction.mockReturnValue(mockIDBTransaction);
		mockIDBTransaction.objectStore.mockReturnValue(mockIDBObjectStore);
		mockIDBObjectStore.index.mockReturnValue(mockIDBIndex);

		// 기본 성공 응답 설정
		mockIDBObjectStore.add.mockImplementation(() => {
			const request = { ...mockIDBRequest };
			setTimeout(() => {
				request.result = 'success';
				if (request.onsuccess) request.onsuccess({ target: request } as any);
			}, 0);
			return request as any;
		});

		mockIDBObjectStore.put.mockImplementation(() => {
			const request = { ...mockIDBRequest };
			setTimeout(() => {
				request.result = 'success';
				if (request.onsuccess) request.onsuccess({ target: request } as any);
			}, 0);
			return request as any;
		});

		mockIDBObjectStore.get.mockImplementation(() => {
			const request = { ...mockIDBRequest };
			setTimeout(() => {
				request.result = testNotificationData;
				if (request.onsuccess) request.onsuccess({ target: request } as any);
			}, 0);
			return request as any;
		});

		mockIDBObjectStore.getAll.mockImplementation(() => {
			const request = { ...mockIDBRequest };
			setTimeout(() => {
				request.result = [testNotificationData];
				if (request.onsuccess) request.onsuccess({ target: request } as any);
			}, 0);
			return request as any;
		});

		mockIDBIndex.getAll.mockImplementation(() => {
			const request = { ...mockIDBRequest };
			setTimeout(() => {
				request.result = [testNotificationData];
				if (request.onsuccess) request.onsuccess({ target: request } as any);
			}, 0);
			return request as any;
		});
	});

	afterEach(() => {
		vi.clearAllMocks();
	});

	afterAll(() => {
		vi.restoreAllMocks();
	});

	describe('기존 platformService 연동 테스트', () => {
		it('플랫폼 감지 기능이 정상적으로 동작해야 함', () => {
			// 플랫폼 감지 함수들이 올바르게 동작하는지 확인
			const platform = getCurrentPlatform();
			expect(['desktop', 'android', 'ios', 'web']).toContain(platform);

			// 플랫폼별 확인 함수들
			const desktopCheck = isDesktop();
			const androidCheck = isAndroid();
			const tauriCheck = isTauri();

			expect(typeof desktopCheck).toBe('boolean');
			expect(typeof androidCheck).toBe('boolean');
			expect(typeof tauriCheck).toBe('boolean');

			// 상호 배타적 플랫폼 확인
			if (desktopCheck) {
				expect(androidCheck).toBe(false);
			}
			if (androidCheck) {
				expect(desktopCheck).toBe(false);
			}
		});

		it('저장소 타입이 플랫폼에 따라 올바르게 결정되어야 함', () => {
			const storageType = getStorageType();
			expect([
				'tauri-store',
				'android-keystore',
				'ios-keychain',
				'localstorage',
				'memory'
			]).toContain(storageType);

			// 플랫폼별 저장소 타입 검증
			const platform = getCurrentPlatform();
			if (platform === 'desktop' && isTauri()) {
				expect(storageType).toBe('tauri-store');
			} else if (platform === 'android' && isTauri()) {
				expect(storageType).toBe('android-keystore');
			}
		});

		it('플랫폼별 기능 지원 여부가 올바르게 확인되어야 함', () => {
			// 알림 기능 지원 확인
			const notificationSupport = supportsFeature('notifications');
			expect(typeof notificationSupport).toBe('boolean');

			// Tauri 환경에서는 알림 지원
			if (isTauri()) {
				expect(notificationSupport).toBe(true);
			}

			// 기타 기능들
			const printerSupport = supportsFeature('printer');
			const updaterSupport = supportsFeature('updater');
			const filesystemSupport = supportsFeature('filesystem');

			expect(typeof printerSupport).toBe('boolean');
			expect(typeof updaterSupport).toBe('boolean');
			expect(typeof filesystemSupport).toBe('boolean');
		});

		it('Pusher Beams 구현에서 플랫폼 서비스를 올바르게 활용해야 함', () => {
			// 플랫폼별 푸시 서비스 선택 로직 시뮬레이션
			const platform = getCurrentPlatform();
			let expectedPushService: string;

			if (isDesktop()) {
				expectedPushService = 'DesktopPushService';
			} else if (isAndroid()) {
				expectedPushService = 'AndroidPushService';
			} else {
				expectedPushService = 'UnsupportedPlatform';
			}

			// 플랫폼에 따른 적절한 서비스 선택 확인
			expect(expectedPushService).toBeDefined();
			if (platform === 'desktop') {
				expect(expectedPushService).toBe('DesktopPushService');
			} else if (platform === 'android') {
				expect(expectedPushService).toBe('AndroidPushService');
			}
		});
	});

	describe('기존 tokenService 연동 테스트', () => {
		beforeEach(async () => {
			await tokenService.initialize();
		});

		it('토큰 서비스 초기화가 정상적으로 동작해야 함', async () => {
			// 토큰 서비스가 이미 초기화되어 있어야 함
			expect(tokenService.storage).toBeDefined();

			// 토큰 상태 확인
			const status = await tokenService.getTokenStatus();
			expect(status).toBeDefined();
			expect(typeof status.hasAccessToken).toBe('boolean');
			expect(typeof status.hasRefreshToken).toBe('boolean');
			expect(typeof status.isAccessTokenValid).toBe('boolean');
			expect(typeof status.isRefreshTokenValid).toBe('boolean');
		});

		it('JWT 토큰 저장 및 조회가 정상적으로 동작해야 함', async () => {
			// 토큰 저장
			await tokenService.storeTokens(validAccessToken, validRefreshToken);

			// 토큰 조회
			const accessToken = await tokenService.getAccessToken();
			const refreshToken = await tokenService.getRefreshToken();

			expect(accessToken).toBe(validAccessToken);
			expect(refreshToken).toBe(validRefreshToken);
		});

		it('사용자 ID 추출이 정상적으로 동작해야 함', async () => {
			// 토큰 저장
			await tokenService.storeTokens(validAccessToken, validRefreshToken);

			// 사용자 ID 조회
			const userId = await tokenService.getCurrentUserId();
			expect(userId).toBe('1'); // JWT 페이로드의 sub 값
		});

		it('인증 상태 확인이 정상적으로 동작해야 함', async () => {
			// 토큰이 없는 상태
			await tokenService.clearTokens();
			let isAuthenticated = await tokenService.isAuthenticated();
			expect(isAuthenticated).toBe(false);

			// 토큰이 있는 상태
			await tokenService.storeTokens(validAccessToken, validRefreshToken);
			isAuthenticated = await tokenService.isAuthenticated();
			expect(isAuthenticated).toBe(true);
		});

		it('Pusher Beams에서 JWT 토큰을 올바르게 활용해야 함', async () => {
			// 토큰 저장
			await tokenService.storeTokens(validAccessToken, validRefreshToken);

			// Pusher Beams 인증용 토큰 제공자 시뮬레이션
			const beamsTokenProvider = {
				async fetchToken(userId: string): Promise<string> {
					const token = await tokenService.getAccessToken();
					if (!token) {
						throw new Error('인증 토큰이 없습니다.');
					}
					return token;
				}
			};

			// 토큰 제공자 테스트
			const token = await beamsTokenProvider.fetchToken('1');
			expect(token).toBe(validAccessToken);
		});

		it('토큰 만료 확인이 정상적으로 동작해야 함', async () => {
			// 유효한 토큰 저장
			await tokenService.storeTokens(validAccessToken, validRefreshToken);

			// 토큰 만료 임박 확인
			const isExpiringSoon = await tokenService.isAccessTokenExpiringSoon(5);
			expect(typeof isExpiringSoon).toBe('boolean');

			// 유효한 토큰은 만료 임박하지 않아야 함
			expect(isExpiringSoon).toBe(false);
		});
	});

	describe('기존 notification-database 연동 테스트', () => {
		beforeEach(async () => {
			// 알림 데이터베이스 초기화
			await initNotificationDatabase();
		});

		afterEach(() => {
			// 데이터베이스 연결 정리
			closeEmployeeDB();
		});

		it('알림 데이터베이스 초기화가 정상적으로 동작해야 함', async () => {
			// 데이터베이스 초기화 확인
			expect(mockIndexedDB.open).toHaveBeenCalledWith('EmployeeDB', 3);
		});

		it('기존 NotificationData 타입으로 알림 저장이 정상적으로 동작해야 함', async () => {
			// 알림 저장
			await saveNotification(testNotificationData);

			// IndexedDB 저장 호출 확인
			expect(mockIDBObjectStore.put).toHaveBeenCalled();
		});

		it('Pusher Beams 알림을 기존 형식으로 변환하여 저장해야 함', async () => {
			// Pusher Beams 알림 저장
			await saveReceivedNotification(testPusherNotification);

			// IndexedDB 저장 호출 확인
			expect(mockIDBObjectStore.put).toHaveBeenCalled();

			// 저장된 데이터 형식 확인 (호출 인자 검증)
			const saveCall = mockIDBObjectStore.put.mock.calls[0];
			const savedData = saveCall[0] as NotificationData;

			expect(savedData.id).toBeDefined();
			expect(savedData.content).toBe(testPusherNotification.body);
			expect(savedData.message).toBe(testPusherNotification.title);
			expect(savedData.priority).toBe(testPusherNotification.priority);
			expect(savedData.category).toBe(testPusherNotification.category);
			expect(savedData.image_url).toBe(testPusherNotification.image_url);
			expect(savedData.deletable).toBe(testPusherNotification.deletable);
			expect(savedData.read).toBe(false);
			expect(savedData.success).toBe(true);
		});

		it('알림 조회 기능이 정상적으로 동작해야 함', async () => {
			// 모든 알림 조회
			const allNotifications = await getAllNotifications();
			expect(Array.isArray(allNotifications)).toBe(true);

			// 활성 알림 조회
			const activeNotifications = await getActiveNotifications();
			expect(Array.isArray(activeNotifications)).toBe(true);

			// 특정 알림 조회
			const notification = await getNotification('test-notification-1');
			expect(notification).toBeDefined();
		});

		it('우선순위별 알림 조회가 정상적으로 동작해야 함', async () => {
			// 우선순위별 조회
			const urgentNotifications = await getNotificationsByPriority('urgent');
			const highNotifications = await getNotificationsByPriority('high');
			const normalNotifications = await getNotificationsByPriority('normal');
			const lowNotifications = await getNotificationsByPriority('low');

			expect(Array.isArray(urgentNotifications)).toBe(true);
			expect(Array.isArray(highNotifications)).toBe(true);
			expect(Array.isArray(normalNotifications)).toBe(true);
			expect(Array.isArray(lowNotifications)).toBe(true);

			// 인덱스 활용 확인
			expect(mockIDBObjectStore.index).toHaveBeenCalledWith('priority');
		});

		it('카테고리별 알림 조회가 정상적으로 동작해야 함', async () => {
			// 카테고리별 조회
			const categoryNotifications = await getNotificationsByCategory('general');
			expect(Array.isArray(categoryNotifications)).toBe(true);

			// 인덱스 활용 확인
			expect(mockIDBObjectStore.index).toHaveBeenCalledWith('category');
		});

		it('읽음 처리 기능이 정상적으로 동작해야 함', async () => {
			// 개별 읽음 처리
			await markNotificationAsRead('test-notification-1');
			expect(mockIDBObjectStore.get).toHaveBeenCalled();
			expect(mockIDBObjectStore.put).toHaveBeenCalled();

			// 전체 읽음 처리
			await markAllNotificationsAsRead();
			expect(mockIDBObjectStore.getAll).toHaveBeenCalled();
		});

		it('읽지 않은 알림 개수 조회가 정상적으로 동작해야 함', async () => {
			// 읽지 않은 알림 개수 조회
			const unreadCount = await getUnreadNotificationCount();
			expect(typeof unreadCount).toBe('number');
			expect(unreadCount).toBeGreaterThanOrEqual(0);

			// read 인덱스 활용 확인
			expect(mockIDBObjectStore.index).toHaveBeenCalledWith('read');
		});

		it('알림 삭제 기능이 정상적으로 동작해야 함', async () => {
			// 알림 삭제
			await deleteNotification('test-notification-1');
			expect(mockIDBObjectStore.delete).toHaveBeenCalledWith('test-notification-1');
		});

		it('통합 알림 관리자가 모든 기능을 제공해야 함', async () => {
			// notificationManager 객체의 모든 메서드 확인
			expect(typeof notificationManager.getAll).toBe('function');
			expect(typeof notificationManager.getActive).toBe('function');
			expect(typeof notificationManager.getById).toBe('function');
			expect(typeof notificationManager.getByPriority).toBe('function');
			expect(typeof notificationManager.getByCategory).toBe('function');
			expect(typeof notificationManager.getUnreadCount).toBe('function');
			expect(typeof notificationManager.save).toBe('function');
			expect(typeof notificationManager.saveReceived).toBe('function');
			expect(typeof notificationManager.markAsRead).toBe('function');
			expect(typeof notificationManager.markAllAsRead).toBe('function');
			expect(typeof notificationManager.delete).toBe('function');
			expect(typeof notificationManager.init).toBe('function');

			// 기능 테스트
			const notifications = await notificationManager.getAll();
			expect(Array.isArray(notifications)).toBe(true);

			const unreadCount = await notificationManager.getUnreadCount();
			expect(typeof unreadCount).toBe('number');
		});
	});

	describe('기존 EmployeeDB 호환성 테스트', () => {
		beforeEach(async () => {
			await initEmployeeDB();
		});

		afterEach(() => {
			closeEmployeeDB();
		});

		it('EmployeeDB 초기화가 정상적으로 동작해야 함', async () => {
			// 데이터베이스 열기 확인
			expect(mockIndexedDB.open).toHaveBeenCalledWith('EmployeeDB', 3);
		});

		it('push_notifications 스토어가 올바르게 설정되어야 함', () => {
			// 트랜잭션 생성 시 push_notifications 스토어 사용 확인
			mockIDBDatabase.transaction(['push_notifications'], 'readwrite');
			expect(mockIDBDatabase.transaction).toHaveBeenCalledWith(['push_notifications'], 'readwrite');
		});

		it('기존 인덱스 구조가 유지되어야 함', () => {
			// 필요한 인덱스들이 사용되는지 확인
			const requiredIndexes = ['created_at', 'expire_day', 'read', 'priority', 'category'];

			requiredIndexes.forEach((indexName) => {
				mockIDBObjectStore.index(indexName);
				expect(mockIDBObjectStore.index).toHaveBeenCalledWith(indexName);
			});
		});

		it('기존 프린터 설정과 독립적으로 동작해야 함', () => {
			// push_notifications 스토어만 사용하고 print_settings는 건드리지 않음
			mockIDBDatabase.transaction(['push_notifications'], 'readonly');
			expect(mockIDBDatabase.transaction).toHaveBeenCalledWith(['push_notifications'], 'readonly');

			// print_settings 스토어는 호출되지 않아야 함
			const printSettingsCalls = mockIDBDatabase.transaction.mock.calls.filter((call) =>
				call[0].includes('print_settings')
			);
			expect(printSettingsCalls).toHaveLength(0);
		});

		it('데이터베이스 버전 호환성이 유지되어야 함', () => {
			// 버전 3으로 데이터베이스 열기
			expect(mockIndexedDB.open).toHaveBeenCalledWith('EmployeeDB', 3);

			// 데이터베이스 객체의 버전 확인
			expect(mockIDBDatabase.version).toBe(3);
			expect(mockIDBDatabase.name).toBe('EmployeeDB');
		});

		it('기존 데이터와 새로운 Pusher Beams 데이터가 공존해야 함', async () => {
			// 기존 알림 데이터 저장
			await saveNotification(testNotificationData);

			// Pusher Beams 알림 데이터 저장
			await saveReceivedNotification(testPusherNotification);

			// 두 데이터 모두 동일한 스토어에 저장되어야 함
			expect(mockIDBObjectStore.put).toHaveBeenCalledTimes(2);

			// 모든 알림 조회 시 두 데이터 모두 포함되어야 함
			const allNotifications = await getAllNotifications();
			expect(Array.isArray(allNotifications)).toBe(true);
		});

		it('기존 CRUD 작업이 정상적으로 동작해야 함', async () => {
			// Create
			await saveNotification(testNotificationData);
			expect(mockIDBObjectStore.put).toHaveBeenCalled();

			// Read
			const notification = await getNotification('test-notification-1');
			expect(mockIDBObjectStore.get).toHaveBeenCalledWith('test-notification-1');

			// Update (읽음 처리)
			await markNotificationAsRead('test-notification-1');
			expect(mockIDBObjectStore.get).toHaveBeenCalled();
			expect(mockIDBObjectStore.put).toHaveBeenCalled();

			// Delete
			await deleteNotification('test-notification-1');
			expect(mockIDBObjectStore.delete).toHaveBeenCalledWith('test-notification-1');
		});
	});

	describe('통합 시나리오 테스트', () => {
		beforeEach(async () => {
			await tokenService.initialize();
			await initNotificationDatabase();
		});

		afterEach(() => {
			closeEmployeeDB();
		});

		it('전체 Pusher Beams 알림 수신 플로우가 기존 인프라와 통합되어 동작해야 함', async () => {
			// 1. 플랫폼 확인
			const platform = getCurrentPlatform();
			expect(['desktop', 'android', 'ios', 'web']).toContain(platform);

			// 2. 토큰 서비스 준비
			await tokenService.storeTokens(validAccessToken, validRefreshToken);
			const isAuthenticated = await tokenService.isAuthenticated();
			expect(isAuthenticated).toBe(true);

			// 3. 사용자 ID 확인
			const userId = await tokenService.getCurrentUserId();
			expect(userId).toBe('1');

			// 4. Pusher Beams 알림 수신 시뮬레이션
			const pusherNotification: PusherBeamsNotificationData = {
				id: 'integration-test-notification',
				title: '통합 테스트 알림',
				body: '전체 플로우 테스트를 위한 알림입니다.',
				priority: 'urgent',
				category: 'system',
				expire_at: '2025-12-31T23:59:59Z',
				deletable: true
			};

			// 5. 알림 저장 (기존 시스템 활용)
			await saveReceivedNotification(pusherNotification);
			expect(mockIDBObjectStore.put).toHaveBeenCalled();

			// 6. 알림 조회 (기존 시스템 활용)
			const savedNotification = await getNotification('integration-test-notification');
			expect(savedNotification).toBeDefined();

			// 7. 우선순위별 조회 (기존 인덱스 활용)
			const urgentNotifications = await getNotificationsByPriority('urgent');
			expect(Array.isArray(urgentNotifications)).toBe(true);

			// 8. 읽지 않은 알림 개수 확인 (기존 인덱스 활용)
			const unreadCount = await getUnreadNotificationCount();
			expect(typeof unreadCount).toBe('number');

			// 9. 읽음 처리 (기존 함수 활용)
			await markNotificationAsRead('integration-test-notification');
			expect(mockIDBObjectStore.get).toHaveBeenCalled();
			expect(mockIDBObjectStore.put).toHaveBeenCalled();
		});

		it('플랫폼별 차이점이 기존 인프라에서 올바르게 처리되어야 함', async () => {
			// 플랫폼별 저장소 타입 확인
			const storageType = getStorageType();
			expect([
				'tauri-store',
				'android-keystore',
				'ios-keychain',
				'localstorage',
				'memory'
			]).toContain(storageType);

			// 플랫폼별 기능 지원 확인
			const notificationSupport = supportsFeature('notifications');
			expect(typeof notificationSupport).toBe('boolean');

			// 토큰 서비스가 플랫폼에 관계없이 동작
			await tokenService.storeTokens(validAccessToken, validRefreshToken);
			const token = await tokenService.getAccessToken();
			expect(token).toBe(validAccessToken);

			// 알림 데이터베이스가 플랫폼에 관계없이 동작
			await saveNotification(testNotificationData);
			const notification = await getNotification('test-notification-1');
			expect(notification).toBeDefined();
		});

		it('에러 상황에서도 기존 인프라가 안정적으로 동작해야 함', async () => {
			// 토큰 서비스 에러 처리
			await tokenService.clearTokens();
			const token = await tokenService.getAccessToken();
			expect(token).toBeNull();

			// 데이터베이스 에러 시뮬레이션
			mockIDBObjectStore.get.mockImplementation(() => {
				const request = { ...mockIDBRequest };
				setTimeout(() => {
					if (request.onerror) request.onerror({ target: request } as any);
				}, 0);
				return request as any;
			});

			// 에러가 발생해도 함수가 예외를 던지지 않아야 함
			await expect(getNotification('non-existent')).resolves.not.toThrow();
		});

		it('성능 요구사항이 기존 인프라에서 충족되어야 함', async () => {
			// 토큰 처리 성능 테스트
			const tokenStartTime = Date.now();
			for (let i = 0; i < 100; i++) {
				await tokenService.getAccessToken();
			}
			const tokenEndTime = Date.now();
			const tokenProcessingTime = tokenEndTime - tokenStartTime;

			// 토큰 처리는 빨라야 함 (100회 처리 시 1초 이내)
			expect(tokenProcessingTime).toBeLessThan(1000);

			// 알림 저장 성능 테스트
			const dbStartTime = Date.now();
			for (let i = 0; i < 50; i++) {
				const notification: NotificationData = {
					...testNotificationData,
					id: `performance-test-${i}`
				};
				await saveNotification(notification);
			}
			const dbEndTime = Date.now();
			const dbProcessingTime = dbEndTime - dbStartTime;

			// 알림 저장은 합리적인 시간 내에 완료되어야 함 (50회 저장 시 2초 이내)
			expect(dbProcessingTime).toBeLessThan(2000);
		});
	});
});
