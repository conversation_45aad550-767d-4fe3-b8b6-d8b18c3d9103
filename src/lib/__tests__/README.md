# Pusher Beams E2E 테스트 가이드

## 개요

이 디렉토리는 Pusher Beams 클라이언트 알림 수신 시스템의 E2E (End-to-End) 테스트를 포함합니다. 실제 환경에서 데스크탑과 안드로이드 플랫폼에서의 푸시 알림 기능을 종합적으로 테스트합니다.

## 테스트 구조

### 1. 기본 E2E 테스트 (`push-service-e2e.test.ts`)

**목적**: 푸시 서비스의 기본 기능과 인프라 호환성을 테스트합니다.

**테스트 범위**:

- 플랫폼 감지 기능
- 환경 변수 설정 확인
- 브라우저 API 지원 (Service Worker, Notification API, Push API)
- Tauri 환경 및 FCM 플러그인 지원
- 로컬 스토리지 및 IndexedDB 기능
- 네트워크 연결 상태
- 성능 기준 (응답 시간, 처리 속도)
- 데이터 형식 호환성
- 에러 처리

### 2. 플랫폼별 특화 테스트 (`platform-specific-e2e.test.ts`)

**목적**: 데스크탑과 안드로이드 환경의 고유 기능을 테스트합니다.

**테스트 범위**:

- **데스크탑 특화**:
  - Service Worker 등록 및 관리
  - Notification API 권한 관리
  - Pusher Beams Web SDK 로딩
  - 브라우저 푸시 알림 처리

- **안드로이드 특화**:
  - FCM 토큰 관리
  - Tauri API 연동
  - 안드로이드 User Agent 확인
  - 로컬 스토리지 기능

- **크로스 플랫폼**:
  - 플랫폼별 기능 분기 로직
  - 공통 데이터 형식 호환성
  - 환경별 기능 지원 매트릭스
  - 성능 및 안정성

### 3. 테스트 헬퍼 (`helpers/push-service-test-helper.ts`)

**목적**: 실제 환경에서 실행할 수 있는 통합 테스트 유틸리티를 제공합니다.

**주요 기능**:

- 전체 E2E 테스트 실행
- 플랫폼별 테스트 실행
- 성능 테스트
- 연결성 테스트
- 브라우저 콘솔에서 실행 가능한 함수들

### 4. 테스트 실행 스크립트 (`run-e2e-tests.ts`)

**목적**: 다양한 테스트 시나리오를 실행할 수 있는 스크립트를 제공합니다.

**지원 기능**:

- 전체 테스트 스위트 실행
- 플랫폼별 특화 테스트
- 성능 테스트
- 연결성 테스트

## 테스트 실행 방법

### NPM 스크립트 사용

```bash
# 기본 E2E 테스트 실행
pnpm test:e2e

# 플랫폼별 특화 테스트 실행
pnpm test:e2e:platform

# 모든 E2E 테스트 실행
pnpm test:e2e:all
```

### 브라우저 콘솔에서 실행 (개발 모드)

개발 모드에서 브라우저를 열면 다음 함수들을 콘솔에서 직접 실행할 수 있습니다:

```javascript
// 기본 E2E 테스트
window.runPushServiceE2ETest();

// 전체 테스트 스위트
window.runPushE2ETests.full();

// 기본 테스트만
window.runPushE2ETests.basic();

// 데스크탑 특화 테스트
window.runPushE2ETests.desktop();

// 안드로이드 특화 테스트
window.runPushE2ETests.android();

// 성능 테스트
window.runPushE2ETests.performance();

// 연결성 테스트
window.runPushE2ETests.connectivity();
```

## 테스트 결과 해석

### 성공 기준

1. **플랫폼 감지**: 현재 환경을 정확히 감지해야 함
2. **API 지원**: 플랫폼별 필요한 API가 지원되어야 함
3. **성능**: 기본 함수 호출이 10ms 이내, 로컬 스토리지 작업이 100ms 이내
4. **데이터 호환성**: 플랫폼별 알림 데이터가 공통 형식으로 변환되어야 함
5. **에러 처리**: 지원되지 않는 기능에 대해 graceful degradation 적용

### 환경별 예상 결과

#### 테스트 환경 (Vitest)

- Service Worker: ❌ (지원되지 않음)
- Notification API: ❌ (지원되지 않음)
- Push API: ❌ (지원되지 않음)
- Tauri API: ❌ (지원되지 않음)
- localStorage: ✅ (지원됨)
- IndexedDB: ❌ (지원되지 않음)

#### 데스크탑 환경 (Tauri)

- Service Worker: ✅ (지원됨)
- Notification API: ✅ (지원됨)
- Push API: ✅ (지원됨)
- Tauri API: ✅ (지원됨)
- localStorage: ✅ (지원됨)
- IndexedDB: ✅ (지원됨)

#### 안드로이드 환경 (Tauri)

- Service Worker: ❌ (필요하지 않음)
- Notification API: ❌ (FCM 사용)
- Push API: ❌ (FCM 사용)
- Tauri API: ✅ (지원됨)
- localStorage: ✅ (지원됨)
- IndexedDB: ✅ (지원됨)

## 테스트 확장 가이드

### 새로운 테스트 추가

1. **기본 기능 테스트**: `push-service-e2e.test.ts`에 추가
2. **플랫폼 특화 테스트**: `platform-specific-e2e.test.ts`에 추가
3. **헬퍼 함수**: `helpers/push-service-test-helper.ts`에 추가

### 테스트 작성 규칙

1. **환경 감지**: 테스트 시작 전에 현재 환경을 확인
2. **조건부 실행**: 지원되지 않는 환경에서는 테스트를 건너뛰기
3. **에러 처리**: 예상되는 에러에 대해 graceful handling
4. **성능 측정**: 중요한 작업에 대해 성능 기준 설정
5. **로깅**: 테스트 진행 상황을 콘솔에 출력

### Mock vs 실제 테스트

현재 E2E 테스트는 실제 환경에서의 동작을 확인하는 데 중점을 둡니다:

- **Mock 사용**: 복잡한 외부 의존성이나 네트워크 호출
- **실제 테스트**: 플랫폼 감지, 브라우저 API 지원, 로컬 스토리지 등

## 문제 해결

### 일반적인 문제

1. **IndexedDB 지원 안됨**: 테스트 환경에서는 정상적인 현상
2. **Service Worker 지원 안됨**: 테스트 환경에서는 정상적인 현상
3. **Tauri API 접근 불가**: 실제 Tauri 환경이 아닌 경우 정상적인 현상

### 디버깅 팁

1. **콘솔 로그 확인**: 테스트 실행 중 출력되는 로그 메시지 확인
2. **환경 변수 확인**: `VITE_PUSHER_BEAMS_INSTANCE_ID` 설정 여부 확인
3. **플랫폼 감지 확인**: `getCurrentPlatform()` 결과 확인
4. **브라우저 개발자 도구**: 실제 브라우저에서 테스트 시 개발자 도구 활용

## 성능 벤치마크

### 현재 성능 기준

- **플랫폼 감지**: 평균 0.01ms 이내
- **로컬 스토리지 작업**: 100개 항목 처리 시 100ms 이내
- **기본 함수 호출**: 10ms 이내

### 성능 모니터링

정기적으로 성능 테스트를 실행하여 성능 저하를 모니터링합니다:

```bash
# 성능 테스트만 실행
pnpm test:e2e -- --grep "성능"
```

## 기여 가이드

새로운 테스트를 추가하거나 기존 테스트를 수정할 때는 다음 사항을 고려해주세요:

1. **테스트 목적 명확화**: 무엇을 테스트하는지 명확히 기술
2. **환경 호환성**: 다양한 환경에서 동작하도록 조건부 처리
3. **성능 고려**: 테스트 실행 시간이 과도하게 길어지지 않도록 주의
4. **문서 업데이트**: 새로운 테스트 추가 시 이 README 파일도 업데이트

---

**마지막 업데이트**: 2025년 1월 20일  
**테스트 커버리지**: 35개 테스트 케이스  
**지원 플랫폼**: 데스크탑 (Tauri), 안드로이드 (Tauri), 웹 브라우저
