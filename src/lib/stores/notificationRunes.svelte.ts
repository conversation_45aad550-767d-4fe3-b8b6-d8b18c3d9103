/**
 * Svelte 5 Runes 기반 알림 상태 관리
 *
 * 이 파일은 Svelte 5의 runes를 활용하여 푸시 알림 시스템의
 * 상태를 관리하고 IndexedDB와 연동하여 영구 저장 기능을 제공합니다.
 *
 * Requirements: 3.1, 3.2, 3.4 - 알림 데이터 저장, 읽음 상태 관리, 만료 처리
 */

import { browser } from '$app/environment';
import type {
	NotificationData,
	NotificationPriority,
	NotificationStats
} from '$lib/types/notification';
import { NotificationConstants, NotificationUtils } from '$lib/types/notification';
import {
	closeNotificationDatabase,
	deleteExpiredNotifications,
	deleteNotification,
	getActiveNotifications,
	getAllNotifications,
	getUnreadCountForUser,
	getUnreadNotificationCount,
	initNotificationDatabase,
	markAllNotificationsAsRead,
	markNotificationAsRead,
	saveNotification
} from '$lib/utils/notification-database';

/**
 * 알림 상태 인터페이스
 */
interface NotificationState {
	// 알림 데이터
	notifications: NotificationData[];
	activeNotifications: NotificationData[];

	// 상태 정보
	isInitialized: boolean;
	isLoading: boolean;
	unreadCount: number;

	// 에러 처리
	error: string | null;

	// 사용자 정보
	currentUserId: string | null;

	// UI 상태
	isRotating: boolean;
	currentRotationIndex: number;

	// 통계 정보
	stats: NotificationStats | null;
}

// 전역 상태 (함수형 패턴)
const notificationState = $state<NotificationState>({
	notifications: [],
	activeNotifications: [],
	isInitialized: false,
	isLoading: false,
	unreadCount: 0,
	error: null,
	currentUserId: null,
	isRotating: false,
	currentRotationIndex: 0,
	stats: null
});

// 타이머 관리 (함수형 패턴)
let rotationTimer: number | null = null;
let cleanupTimer: number | null = null;

/**
 * 읽지 않은 알림 수 업데이트
 */
async function updateUnreadCount(): Promise<void> {
	try {
		if (notificationState.currentUserId) {
			notificationState.unreadCount = await getUnreadCountForUser();
		} else {
			notificationState.unreadCount = await getUnreadNotificationCount();
		}
	} catch (error) {
		console.error('읽지 않은 알림 수 업데이트 실패:', error);
	}
}

/**
 * 통계 정보 업데이트
 */
function updateStats(): void {
	const notifications = notificationState.activeNotifications;

	notificationState.stats = {
		total: notifications.length,
		unread: notifications.filter((n) => !n.read).length,
		by_priority: {
			low: notifications.filter((n) => n.priority === 'low').length,
			normal: notifications.filter((n) => n.priority === 'normal' || !n.priority).length,
			high: notifications.filter((n) => n.priority === 'high').length,
			urgent: notifications.filter((n) => n.priority === 'urgent').length
		},
		expired: notificationState.notifications.length - notifications.length
	};
}

/**
 * 자동 정리 타이머 시작
 */
function startCleanupTimer(): void {
	// 24시간마다 만료된 알림 정리
	cleanupTimer = window.setInterval(() => {
		cleanupExpiredNotifications();
	}, NotificationConstants.CLEANUP_INTERVAL);
}

/**
 * 알림 데이터 로드
 */
async function loadNotifications(): Promise<void> {
	try {
		notificationState.isLoading = true;

		const [allNotifications, activeNotifications] = await Promise.all([
			getAllNotifications(),
			getActiveNotifications()
		]);

		notificationState.notifications = allNotifications;
		notificationState.activeNotifications = activeNotifications;

		await updateUnreadCount();
		updateStats();
	} catch (error) {
		console.error('알림 로드 실패:', error);
		notificationState.error = error instanceof Error ? error.message : '로드 실패';
	} finally {
		notificationState.isLoading = false;
	}
}

/**
 * 만료된 알림 정리
 */
async function cleanupExpiredNotifications(): Promise<number> {
	try {
		const deletedCount = await deleteExpiredNotifications();

		if (deletedCount > 0) {
			// 로컬 상태 새로고침
			await loadNotifications();
		}

		return deletedCount;
	} catch (error) {
		console.error('만료 알림 정리 실패:', error);
		notificationState.error = error instanceof Error ? error.message : '정리 실패';
		return 0;
	}
}

/**
 * 초기화
 */
async function initializeNotifications(): Promise<void> {
	if (notificationState.isInitialized) return;

	try {
		notificationState.isLoading = true;
		notificationState.error = null;

		// 데이터베이스 초기화
		await initNotificationDatabase();

		// 만료된 알림 정리
		await cleanupExpiredNotifications();

		// 알림 데이터 로드
		await loadNotifications();

		// 자동 정리 타이머 시작
		startCleanupTimer();

		notificationState.isInitialized = true;
		console.log('알림 스토어 초기화 완료');
	} catch (error) {
		console.error('알림 스토어 초기화 실패:', error);
		notificationState.error = error instanceof Error ? error.message : '초기화 실패';
	} finally {
		notificationState.isLoading = false;
	}
}

/**
 * 사용자 정보 설정
 */
function setUser(userId: string): void {
	notificationState.currentUserId = userId;

	// 사용자별 읽지 않은 알림 수 업데이트
	updateUnreadCount();
}

/**
 * 새 알림 추가
 */
async function addNotification(notification: NotificationData): Promise<void> {
	try {
		// 데이터베이스에 저장
		await saveNotification(notification);

		// 로컬 상태 업데이트
		notificationState.notifications.unshift(notification);

		// 활성 알림인지 확인하고 추가
		if (!NotificationUtils.isExpired(notification)) {
			notificationState.activeNotifications.unshift(notification);
		}

		await updateUnreadCount();
		updateStats();

		console.log('새 알림 추가:', notification.id);
	} catch (error) {
		console.error('알림 추가 실패:', error);
		notificationState.error = error instanceof Error ? error.message : '알림 추가 실패';
	}
}

/**
 * 알림을 읽음으로 표시
 */
async function markAsRead(id: string): Promise<void> {
	try {
		await markNotificationAsRead(id);

		// 로컬 상태 업데이트
		const updateNotification = (notifications: NotificationData[]) => {
			const index = notifications.findIndex((n) => n.id === id);
			if (index !== -1) {
				notifications[index] = { ...notifications[index], read: true };
			}
		};

		updateNotification(notificationState.notifications);
		updateNotification(notificationState.activeNotifications);

		await updateUnreadCount();
		updateStats();
	} catch (error) {
		console.error('알림 읽음 처리 실패:', error);
		notificationState.error = error instanceof Error ? error.message : '읽음 처리 실패';
	}
}

/**
 * 모든 알림을 읽음으로 표시
 */
async function markAllAsRead(): Promise<void> {
	try {
		await markAllNotificationsAsRead();

		// 로컬 상태 업데이트
		const markAllRead = (notifications: NotificationData[]) => {
			notifications.forEach((notification) => {
				notification.read = true;
			});
		};

		markAllRead(notificationState.notifications);
		markAllRead(notificationState.activeNotifications);

		notificationState.unreadCount = 0;
		updateStats();
	} catch (error) {
		console.error('전체 읽음 처리 실패:', error);
		notificationState.error = error instanceof Error ? error.message : '전체 읽음 처리 실패';
	}
}

/**
 * 알림 삭제
 */
async function removeNotification(id: string): Promise<void> {
	try {
		await deleteNotification(id);

		// 로컬 상태에서 제거
		notificationState.notifications = notificationState.notifications.filter((n) => n.id !== id);
		notificationState.activeNotifications = notificationState.activeNotifications.filter(
			(n) => n.id !== id
		);

		await updateUnreadCount();
		updateStats();
	} catch (error) {
		console.error('알림 삭제 실패:', error);
		notificationState.error = error instanceof Error ? error.message : '알림 삭제 실패';
	}
}

/**
 * 알림 로테이션 시작
 */
function startRotation(): void {
	const unreadUserNotifications = getUserNotifications().filter((n) => !n.read);
	if (rotationTimer || unreadUserNotifications.length === 0) return;

	notificationState.isRotating = true;
	notificationState.currentRotationIndex = 0;

	rotationTimer = window.setInterval(() => {
		const unreadCount = getUserNotifications().filter((n) => !n.read).length;
		if (unreadCount === 0) {
			stopRotation();
			return;
		}

		notificationState.currentRotationIndex =
			(notificationState.currentRotationIndex + 1) % unreadCount;
	}, NotificationConstants.ROTATION_INTERVAL);

	console.log('알림 로테이션 시작');
}

/**
 * 알림 로테이션 중지
 */
function stopRotation(): void {
	if (!notificationState.isRotating) return;

	notificationState.isRotating = false;

	if (rotationTimer) {
		clearInterval(rotationTimer);
		rotationTimer = null;
	}

	console.log('알림 로테이션 중지');
}

/**
 * 에러 클리어
 */
function clearError(): void {
	notificationState.error = null;
}

/**
 * 리소스 정리
 */
function destroyNotifications(): void {
	stopRotation();

	if (cleanupTimer) {
		clearInterval(cleanupTimer);
		cleanupTimer = null;
	}

	closeNotificationDatabase();
}

/**
 * 사용자별 알림 조회 (단순화된 버전)
 * 서버에서 이미 필터링되어 전송된 알림들을 모두 반환
 */
function getUserNotifications(): NotificationData[] {
	// 서버에서 이미 해당 사용자에게 필터링해서 보낸 알림들이므로
	// 모든 활성 알림을 반환
	return notificationState.activeNotifications;
}

/**
 * 현재 로테이션 알림 조회
 */
function getCurrentRotationNotification(): NotificationData | null {
	const unread = getUserNotifications().filter((n) => !n.read);
	if (unread.length === 0) return null;

	const index = notificationState.currentRotationIndex % unread.length;
	return unread[index];
}

// 브라우저 환경에서 자동 초기화
if (browser) {
	initializeNotifications();
}

/**
 * 알림 상태 및 액션 함수들을 담은 객체 (함수형 패턴)
 */
export const notifications = {
	// 상태 접근자들
	get state() {
		return notificationState;
	},
	get notifications() {
		return notificationState.notifications;
	},
	get activeNotifications() {
		return notificationState.activeNotifications;
	},
	get isInitialized() {
		return notificationState.isInitialized;
	},
	get isLoading() {
		return notificationState.isLoading;
	},
	get unreadCount() {
		return notificationState.unreadCount;
	},
	get error() {
		return notificationState.error;
	},
	get currentUserId() {
		return notificationState.currentUserId;
	},

	get isRotating() {
		return notificationState.isRotating;
	},
	get currentRotationIndex() {
		return notificationState.currentRotationIndex;
	},
	get stats() {
		return notificationState.stats;
	},

	// 파생된 상태들
	get hasNotifications() {
		return notificationState.activeNotifications.length > 0;
	},
	get hasUnreadNotifications() {
		return notificationState.unreadCount > 0;
	},
	get userNotifications() {
		return getUserNotifications();
	},
	get unreadUserNotifications() {
		return getUserNotifications().filter((n) => !n.read);
	},
	get currentRotationNotification() {
		return getCurrentRotationNotification();
	},

	// 액션 함수들
	initialize: initializeNotifications,
	setUser,
	loadNotifications,
	addNotification,
	markAsRead,
	markAllAsRead,
	removeNotification,
	cleanupExpiredNotifications,
	startRotation,
	stopRotation,
	clearError,
	destroy: destroyNotifications
};

/**
 * 컴포넌트에서 사용할 수 있는 알림 관련 유틸리티 함수들
 */

/**
 * 알림 목록 표시에 사용할 수 있는 헬퍼
 */
export function useNotificationList() {
	return {
		get allNotifications() {
			return notifications.notifications;
		},
		get activeNotifications() {
			return notifications.activeNotifications;
		},
		get userNotifications() {
			return notifications.userNotifications;
		},
		get unreadNotifications() {
			return notifications.unreadUserNotifications;
		},
		get isLoading() {
			return notifications.isLoading;
		},
		get error() {
			return notifications.error;
		},
		markAsRead: notifications.markAsRead,
		markAllAsRead: notifications.markAllAsRead,
		removeNotification: notifications.removeNotification
	};
}

/**
 * 알림 카운터에 사용할 수 있는 헬퍼
 */
export function useNotificationCounter() {
	return {
		get unreadCount() {
			return notifications.unreadCount;
		},
		get hasUnread() {
			return notifications.hasUnreadNotifications;
		},
		get stats() {
			return notifications.stats;
		}
	};
}

/**
 * Footer 로테이션에 사용할 수 있는 헬퍼
 */
export function useNotificationRotation() {
	// 자동 로테이션 시작/중지
	$effect(() => {
		const hasNotifications = notifications.hasUnreadNotifications;
		const isRotating = notifications.isRotating;

		if (hasNotifications && !isRotating) {
			notifications.startRotation();
		} else if (!hasNotifications && isRotating) {
			notifications.stopRotation();
		}
	});

	return {
		get isRotating() {
			return notifications.isRotating;
		},
		get currentNotification() {
			return notifications.currentRotationNotification;
		},
		get hasNotifications() {
			return notifications.hasUnreadNotifications;
		},
		startRotation: notifications.startRotation,
		stopRotation: notifications.stopRotation
	};
}

/**
 * 알림 추가에 사용할 수 있는 헬퍼
 */
export function useNotificationActions() {
	return {
		get isInitialized() {
			return notifications.isInitialized;
		},
		get isLoading() {
			return notifications.isLoading;
		},
		get error() {
			return notifications.error;
		},
		addNotification: notifications.addNotification,
		setUser: notifications.setUser,
		initialize: notifications.initialize,
		cleanupExpired: notifications.cleanupExpiredNotifications,
		clearError: notifications.clearError
	};
}

/**
 * 개발 환경에서 디버깅에 사용할 수 있는 헬퍼
 */
export function useNotificationDebug() {
	if (!import.meta.env.DEV) {
		return {
			logState: () => {},
			createTestNotification: () => {},
			clearAllNotifications: () => {}
		};
	}

	const logState = () => {
		console.log('[Notification Runes Debug]', {
			isInitialized: notifications.isInitialized,
			isLoading: notifications.isLoading,
			notificationCount: notifications.notifications.length,
			activeCount: notifications.activeNotifications.length,
			unreadCount: notifications.unreadCount,
			isRotating: notifications.isRotating,
			currentUser: notifications.currentUserId,
			stats: notifications.stats,
			error: notifications.error
		});
	};

	const createTestNotification = async (priority: NotificationPriority = 'normal') => {
		const testNotification = NotificationUtils.createNotification({
			content: `테스트 알림 (${priority}) - ${new Date().toLocaleTimeString()}`,
			expire_day: NotificationUtils.getDefaultExpireDay(),
			priority
		});

		await notifications.addNotification(testNotification);
		console.log('테스트 알림 생성:', testNotification);
	};

	const clearAllNotifications = async () => {
		const allNotifications = notifications.notifications;
		for (const notification of allNotifications) {
			await notifications.removeNotification(notification.id);
		}
		console.log('모든 알림 삭제 완료');
	};

	return {
		logState,
		createTestNotification,
		clearAllNotifications
	};
}

// 브라우저 환경에서 전역 객체에 디버그 함수 등록
if (typeof window !== 'undefined' && import.meta.env.DEV) {
	(window as any).notificationDebug = useNotificationDebug();
	(window as any).notifications = notifications;
}
