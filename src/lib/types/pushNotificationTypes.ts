/**
 * 푸시 알림 서비스 관련 타입 정의
 */

import type { NotificationData, CreateNotificationData } from '$lib/types/notification';

/**
 * 푸시 알림 권한 상태
 */
export type PermissionStatus = 'granted' | 'denied' | 'default' | 'unavailable';

/**
 * 푸시 서비스 에러 타입
 */
export type PushServiceErrorCode =
	| 'PERMISSION_DENIED'
	| 'PERMISSION_UNAVAILABLE'
	| 'PERMISSION_REQUEST_FAILED'
	| 'SERVICE_INIT_FAILED'
	| 'TOKEN_REG_FAILED'
	| 'NOTIFICATION_SEND_FAILED'
	| 'PLATFORM_NOT_SUPPORTED'
	| 'DEVICE_REGISTRATION_FAILED'
	| 'USER_ID_CONNECTION_FAILED'
	| 'USER_ID_CLEAR_FAILED'
	| 'TOKEN_RECOVERY_FAILED'
	| 'AUTH_REQUIRED'
	| 'REFRESH_FAILED'
	| 'RETRY_IN_PROGRESS'
	| 'PERMISSION_ERROR'
	| 'NETWORK_ERROR'
	| 'BEAMS_CONNECTION_ERROR'
	| 'SERVICE_WORKER_ERROR'
	| 'FCM_ERROR'
	| 'TOKEN_REFRESH_FAILED'
	| 'TOKEN_EXPIRED'
	| 'TOKEN_INVALID'
	| 'TOKEN_MISSING'
	| 'DESKTOP_ERROR'
	| 'ANDROID_ERROR'
	| 'TAURI_ERROR'
	| 'SYNC_INIT_FAILED'
	| 'OFFLINE_SAVE_FAILED'
	| 'OFFLINE_MODE'
	| 'SYNC_IN_PROGRESS'
	| 'OFFLINE_OPERATION_FAILED'
	| 'SYNC_FAILED'
	| 'MARK_READ_FAILED';

/**
 * 푸시 서비스 에러 객체
 */
export interface PushServiceError {
	code: PushServiceErrorCode;
	message: string;
	originalError?: Error;
}

/**
 * 푸시 서비스 에러 생성 함수
 */
export function createPushServiceError(
	code: PushServiceErrorCode,
	message: string,
	originalError?: Error
): PushServiceError {
	return {
		code,
		message,
		originalError
	};
}

/**
 * 알림 수신 콜백 타입
 */
export type NotificationCallback = (notification: NotificationData) => void;

// === 제거된 타입들 ===
// Interest 관리 및 복잡한 Beams 상태 관리 관련 타입들이 제거되었습니다.
// 새로운 구현에서는 클라이언트에서 Pusher Beams에 직접 연동합니다.

/**
 * Pusher Beams에서 수신되는 알림 데이터 타입
 * action_url/redirect 필드는 사용하지 않음 (요구사항에 따라 제거됨)
 */
export interface PusherBeamsNotificationData {
	id?: string;
	title: string;
	body: string;
	data?: Record<string, any>;
	priority?: 'low' | 'normal' | 'high' | 'urgent';
	category?: string;
	image_url?: string;
	expire_at?: string; // ISO 8601 형식
	deletable?: boolean;
}

/**
 * 기본 푸시 알림 서비스 인터페이스 (간소화된 버전)
 */
export interface BasicPushNotificationService {
	// 기본 서비스 메서드들
	initialize(): Promise<void>;
	requestPermission(): Promise<PermissionStatus>;
	getPermissionStatus(): Promise<PermissionStatus>;
	onNotificationReceived(callback: NotificationCallback): void;
	offNotificationReceived(callback: NotificationCallback): void;
	showNotification(notification: CreateNotificationData): Promise<void>;
	cleanup(): void | Promise<void>;
}
