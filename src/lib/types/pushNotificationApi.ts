/**
 * Push Notification API 타입 정의
 *
 * 백엔드 알림 발송 및 알림 목록 관리를 위한 타입들입니다.
 *
 * 주의: 디바이스 등록은 클라이언트에서 Pusher Beams SDK로 직접 처리합니다.
 */

/**
 * 알림 발송 요청 데이터
 */
export interface SendNotificationRequest {
	/** 수신자 타입 */
	type: 'interest' | 'user' | 'users';
	/** 수신자 목록 (Interest 이름 또는 사용자 ID) */
	targets: string[];
	/** 알림 제목 */
	title: string;
	/** 알림 내용 */
	body: string;
	/** 추가 데이터 (선택사항) */
	data?: Record<string, any>;
	/** 우선순위 */
	priority?: 'low' | 'normal' | 'high';
}

/**
 * 알림 발송 응답 데이터
 */
export interface SendNotificationResponse {
	success: boolean;
	message: string;
	publishId?: string;
}

/**
 * 알림 목록 조회 요청 파라미터
 */
export interface NotificationListRequest {
	/** 사용자 ID */
	userId: string;
	/** 페이지 번호 (선택사항) */
	page?: number;
	/** 페이지당 항목 수 (선택사항) */
	limit?: number;
	/** 읽지 않은 알림만 조회 (선택사항) */
	unreadOnly?: boolean;
}

/**
 * 알림 목록 조회 응답 데이터
 */
export interface NotificationListResponse {
	/** 조회 성공 여부 */
	success: boolean;
	/** 응답 메시지 */
	message: string;
	/** 알림 목록 */
	notifications: NotificationData[];
	/** 페이지네이션 정보 */
	pagination?: {
		/** 현재 페이지 */
		currentPage: number;
		/** 전체 페이지 수 */
		totalPages: number;
		/** 전체 항목 수 */
		totalItems: number;
		/** 페이지당 항목 수 */
		itemsPerPage: number;
	};
}

/**
 * 알림 읽음 처리 요청 데이터
 */
export interface NotificationReadRequest {
	/** 알림 ID 목록 */
	notificationIds: string[];
	/** 사용자 ID */
	userId: string;
}

/**
 * 알림 읽음 처리 응답 데이터
 */
export interface NotificationReadResponse {
	/** 처리 성공 여부 */
	success: boolean;
	/** 응답 메시지 */
	message: string;
	/** 처리된 알림 ID 목록 */
	processedIds: string[];
}

/**
 * 알림 데이터
 */
export interface NotificationData {
	/** 알림 고유 ID */
	id: string;
	/** 알림 내용 */
	content: string;
	/** 만료일 (ISO 8601 형식) */
	expire_day: string;
	/** 생성 시간 */
	created_at: string;
	/** 읽음 여부 */
	read: boolean;
	/** 우선순위 */
	priority?: 'low' | 'normal' | 'high' | 'urgent';
	/** 카테고리 */
	category?: string;
	/** 액션 URL */
	action_url?: string;
	/** 이미지 URL */
	image_url?: string;
}
