import { invoke } from '@tauri-apps/api/core';
import { listen } from '@tauri-apps/api/event';
import { writable } from 'svelte/store';

// 업데이트 상태 관리
export interface UpdateState {
	isAvailable: boolean;
	version: string;
	isDownloading: boolean;
	downloadProgress: number;
	isInstalling: boolean;
	userChoice: 'pending' | 'accepted' | 'deferred' | null;
}

export const updateState = writable<UpdateState>({
	isAvailable: false,
	version: '',
	isDownloading: false,
	downloadProgress: 0,
	isInstalling: false,
	userChoice: null
});

// 업데이트 모달 상태 관리
export const updateModalState = writable<{
	showManualModal: boolean;
}>({
	showManualModal: false
});

// 수동 업데이트 모달 제어 함수들
export function showUpdateModal() {
	updateModalState.update(state => ({
		...state,
		showManualModal: true
	}));
}

export function hideUpdateModal() {
	updateModalState.update(state => ({
		...state,
		showManualModal: false
	}));
}

/**
 * 수동으로 업데이트를 확인하는 함수
 * @returns Promise<string> 업데이트 확인 결과 메시지
 */
export async function checkForUpdates(): Promise<string> {
	try {
		return await invoke<string>('check_for_updates');
	} catch (error) {
		console.error('업데이트 확인 중 오류:', error);
		throw error;
	}
}

/**
 * 현재 앱 버전을 가져오는 함수
 * @returns Promise<string> 현재 앱 버전
 */
export async function getCurrentVersion(): Promise<string> {
	try {
		const { getVersion } = await import('@tauri-apps/api/app');
		return await getVersion();
	} catch (error) {
		console.error('버전 정보 가져오기 실패:', error);
		return 'Unknown';
	}
}

/**
 * 업데이트 이벤트 리스너 설정
 */
export async function setupUpdateListeners() {
	// 업데이트 사용 가능 이벤트
	const unlistenUpdateAvailable = listen('update-available', (event) => {
		const version = event.payload as string;
		updateState.update(state => ({
			...state,
			isAvailable: true,
			version,
			userChoice: 'pending'
		}));
		console.log('업데이트 사용 가능:', version);
	});

	// 다운로드 진행률 이벤트
	const unlistenDownloadProgress = listen('update-download-progress', (event) => {
		const progress = event.payload as number;
		updateState.update(state => ({
			...state,
			isDownloading: true,
			downloadProgress: progress
		}));
		console.log('다운로드 진행률:', progress + '%');
	});

	// 다운로드 완료 이벤트
	const unlistenDownloadFinished = listen('update-download-finished', () => {
		updateState.update(state => ({
			...state,
			isDownloading: false,
			isInstalling: true
		}));
		console.log('다운로드 완료, 설치 중...');
	});

	// 업데이트 설치 완료 이벤트
	const unlistenUpdateInstalled = listen('update-installed', () => {
		updateState.update(state => ({
			...state,
			isInstalling: false,
			isAvailable: false
		}));
		console.log('업데이트 설치 완료, 앱이 재시작됩니다.');
	});

	// 정리 함수 반환
	return async () => {
		(await unlistenUpdateAvailable)();
		(await unlistenDownloadProgress)();
		(await unlistenDownloadFinished)();
		(await unlistenUpdateInstalled)();
	};
}

/**
 * 사용자가 업데이트를 수락했을 때 호출
 */
export async function acceptUpdate() {
	updateState.update(state => ({
		...state,
		userChoice: 'accepted'
	}));

	// 업데이트 설치 진행
	try {
		await invoke('install_available_update');
	} catch (error) {
		console.error('업데이트 설치 실패:', error);
		updateState.update(state => ({
			...state,
			userChoice: 'pending' // 실패 시 다시 선택 가능하도록
		}));
	}
}

/**
 * 사용자가 업데이트를 연기했을 때 호출
 */
export function deferUpdate() {
	updateState.update(state => ({
		...state,
		userChoice: 'deferred'
	}));
}

// 페이지 복원 캐시 및 중복 호출 방지
let pageRestoreCache: { url: string | null; timestamp: number; checked: boolean } | null = null;
let isPageRestoreChecking = false;

/**
 * 페이지 복원 관련 함수들
 */
export const pageRestore = {
	/**
	 * 현재 페이지 URL을 저장
	 */
	save(url: string) {
		if (typeof localStorage !== 'undefined') {
			if (import.meta.env.VITE_NODE_ENV === 'development') {
				console.log('💾 pageRestore.save: URL 저장:', url);
			}
			localStorage.setItem('cnsprowms-last-page', url);
			localStorage.setItem('cnsprowms-page-saved-at', Date.now().toString());

			// 캐시 무효화
			pageRestoreCache = null;

			if (import.meta.env.VITE_NODE_ENV === 'development') {
				console.log('✅ pageRestore.save: 저장 완료');
			}
		} else if (import.meta.env.VITE_NODE_ENV === 'development') {
			console.warn('⚠️ pageRestore.save: localStorage 사용 불가');
		}
	},

	/**
	 * 저장된 페이지 URL을 가져오기 (캐싱 및 중복 호출 방지)
	 * @param maxAgeMinutes 최대 유효 시간 (분)
	 */
	get(maxAgeMinutes: number = 30): string | null {
		const isDev = import.meta.env.VITE_NODE_ENV === 'development';

		if (typeof localStorage === 'undefined') {
			if (isDev && !pageRestoreCache?.checked) {
				console.warn('⚠️ pageRestore.get: localStorage 사용 불가');
			}
			return null;
		}

		// 캐시된 결과가 있고 최근에 확인했다면 캐시 반환 (중복 호출 방지)
		if (pageRestoreCache && (Date.now() - pageRestoreCache.timestamp) < 5000) { // 5초 캐시
			return pageRestoreCache.url;
		}

		// 중복 호출 방지
		if (isPageRestoreChecking) {
			return pageRestoreCache?.url || null;
		}

		isPageRestoreChecking = true;

		try {
			const savedUrl = localStorage.getItem('cnsprowms-last-page');
			const savedAt = localStorage.getItem('cnsprowms-page-saved-at');

			// 첫 번째 호출에서만 로그 출력 (반복 로그 방지)
			if (isDev && !pageRestoreCache?.checked) {
				console.log('🔍 pageRestore.get: 저장된 URL:', savedUrl);
				console.log('🕒 pageRestore.get: 저장 시간:', savedAt);
			}

			if (!savedUrl || !savedAt) {
				if (isDev && !pageRestoreCache?.checked) {
					console.log('❌ pageRestore.get: 저장된 데이터 없음');
				}

				// 캐시 업데이트
				pageRestoreCache = { url: null, timestamp: Date.now(), checked: true };
				return null;
			}

			// 저장된 시간이 너무 오래되었으면 무시
			const savedTime = parseInt(savedAt);
			const maxAge = maxAgeMinutes * 60 * 1000; // 분을 밀리초로 변환
			const ageMinutes = (Date.now() - savedTime) / (60 * 1000);

			if (isDev && !pageRestoreCache?.checked) {
				console.log(`⏰ pageRestore.get: 저장된 지 ${ageMinutes.toFixed(1)}분 경과 (최대 ${maxAgeMinutes}분)`);
			}

			if (Date.now() - savedTime > maxAge) {
				if (isDev && !pageRestoreCache?.checked) {
					console.log('⏰ pageRestore.get: 저장 시간 초과로 데이터 삭제');
				}
				this.clear();

				// 캐시 업데이트
				pageRestoreCache = { url: null, timestamp: Date.now(), checked: true };
				return null;
			}

			if (isDev && !pageRestoreCache?.checked) {
				console.log('✅ pageRestore.get: 유효한 URL 반환:', savedUrl);
			}

			// 캐시 업데이트
			pageRestoreCache = { url: savedUrl, timestamp: Date.now(), checked: true };
			return savedUrl;
		} finally {
			isPageRestoreChecking = false;
		}
	},

	/**
	 * 저장된 페이지 정보 삭제
	 */
	clear() {
		if (typeof localStorage !== 'undefined') {
			if (import.meta.env.VITE_NODE_ENV === 'development') {
				console.log('🗑️ pageRestore.clear: 저장된 데이터 삭제');
			}
			localStorage.removeItem('cnsprowms-last-page');
			localStorage.removeItem('cnsprowms-page-saved-at');

			// 캐시 무효화
			pageRestoreCache = null;
		}
	},

	/**
	 * 페이지 복원이 필요한지 확인 (캐싱 적용)
	 */
	shouldRestore(): boolean {
		const result = this.get() !== null;

		// 첫 번째 호출에서만 로그 출력 (반복 로그 방지)
		if (import.meta.env.VITE_NODE_ENV === 'development' && !pageRestoreCache?.checked) {
			console.log('🔍 pageRestore.shouldRestore:', result);
		}

		return result;
	},

	/**
	 * 캐시 초기화 (테스트 및 디버깅용)
	 */
	clearCache() {
		pageRestoreCache = null;
		isPageRestoreChecking = false;
		if (import.meta.env.VITE_NODE_ENV === 'development') {
			console.log('🧹 pageRestore.clearCache: 캐시 초기화 완료');
		}
	}
};
