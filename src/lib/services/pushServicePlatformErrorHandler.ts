/**
 * 플랫폼별 푸시 서비스 에러 처리
 *
 * 기존 platformService 기반으로 플랫폼별 에러를 처리하고
 * Pusher Beams 연결 오류 및 네트워크 오류 시 재시도 메커니즘을 제공합니다.
 *
 * Requirements: 4.2.1, 4.2.2, 4.2.4
 */

import { getCurrentPlatform, isDesktop, isAndroid, isTauri } from '$lib/services/platformService';
import type { PushServiceError } from '$lib/types/pushNotificationTypes';
import { createPushServiceError } from '$lib/types/pushNotificationTypes';

/**
 * 플랫폼별 에러 타입
 */
export type PlatformErrorType =
	| 'PLATFORM_NOT_SUPPORTED'
	| 'DESKTOP_ERROR'
	| 'ANDROID_ERROR'
	| 'TAURI_ERROR'
	| 'BEAMS_CONNECTION_ERROR'
	| 'NETWORK_ERROR'
	| 'SERVICE_WORKER_ERROR'
	| 'FCM_ERROR'
	| 'PERMISSION_ERROR';

/**
 * 네트워크 에러 타입
 */
export type NetworkErrorType =
	| 'CONNECTION_FAILED'
	| 'TIMEOUT'
	| 'DNS_ERROR'
	| 'SSL_ERROR'
	| 'SERVER_ERROR'
	| 'RATE_LIMITED';

/**
 * 재시도 설정
 */
export interface RetryConfig {
	maxAttempts: number;
	baseDelayMs: number;
	maxDelayMs: number;
	backoffMultiplier: number;
	retryableErrors: string[];
}

/**
 * 플랫폼별 에러 정보
 */
export interface PlatformErrorInfo {
	platform: string;
	errorType: PlatformErrorType;
	message: string;
	canRetry: boolean;
	retryConfig?: RetryConfig;
	platformSpecific?: Record<string, any>;
	originalError?: any;
}

/**
 * 재시도 결과
 */
export interface RetryResult {
	success: boolean;
	attempts: number;
	totalTimeMs: number;
	lastError?: PushServiceError;
	recoveredAfterAttempt?: number;
}

/**
 * 플랫폼별 에러 처리 상태
 */
interface PlatformErrorHandlerState {
	currentPlatform: string;
	retryAttempts: Map<string, number>;
	lastRetryTime: Map<string, number>;
	activeRetries: Set<string>;
	defaultRetryConfig: RetryConfig;
	platformRetryConfigs: Map<string, RetryConfig>;
}

/**
 * 플랫폼별 푸시 서비스 에러 핸들러 생성
 */
export function createPushServicePlatformErrorHandler() {
	const state: PlatformErrorHandlerState = {
		currentPlatform: getCurrentPlatform(),
		retryAttempts: new Map(),
		lastRetryTime: new Map(),
		activeRetries: new Set(),
		defaultRetryConfig: {
			maxAttempts: 3,
			baseDelayMs: 1000,
			maxDelayMs: 30000,
			backoffMultiplier: 2,
			retryableErrors: [
				'NETWORK_ERROR',
				'BEAMS_CONNECTION_ERROR',
				'TIMEOUT',
				'CONNECTION_FAILED',
				'SERVER_ERROR'
			]
		},
		platformRetryConfigs: new Map()
	};

	// 플랫폼별 기본 재시도 설정 초기화
	initializePlatformRetryConfigs();

	/**
	 * 플랫폼별 재시도 설정 초기화
	 */
	function initializePlatformRetryConfigs(): void {
		// 데스크탑 설정
		state.platformRetryConfigs.set('desktop', {
			maxAttempts: 3,
			baseDelayMs: 2000,
			maxDelayMs: 15000,
			backoffMultiplier: 1.5,
			retryableErrors: [
				'NETWORK_ERROR',
				'BEAMS_CONNECTION_ERROR',
				'SERVICE_WORKER_ERROR',
				'TIMEOUT'
			]
		});

		// 안드로이드 설정
		state.platformRetryConfigs.set('android', {
			maxAttempts: 5,
			baseDelayMs: 1500,
			maxDelayMs: 20000,
			backoffMultiplier: 2,
			retryableErrors: [
				'NETWORK_ERROR',
				'FCM_ERROR',
				'BEAMS_CONNECTION_ERROR',
				'CONNECTION_FAILED',
				'TIMEOUT'
			]
		});

		console.log('[PlatformErrorHandler] 플랫폼별 재시도 설정 초기화 완료');
	}

	/**
	 * 플랫폼별 에러인지 확인
	 */
	function isPlatformError(error: any): boolean {
		if (!error) return false;

		// PushServiceError 타입 확인
		if (error.type) {
			const platformErrorTypes: PlatformErrorType[] = [
				'PLATFORM_NOT_SUPPORTED',
				'DESKTOP_ERROR',
				'ANDROID_ERROR',
				'TAURI_ERROR',
				'BEAMS_CONNECTION_ERROR',
				'NETWORK_ERROR',
				'SERVICE_WORKER_ERROR',
				'FCM_ERROR',
				'PERMISSION_ERROR'
			];
			return platformErrorTypes.includes(error.type);
		}

		// 네트워크 에러 확인
		if (isNetworkError(error)) {
			return true;
		}

		// Pusher Beams 관련 에러 확인
		if (isBeamsConnectionError(error)) {
			return true;
		}

		// 플랫폼 관련 에러 메시지 패턴 확인
		const errorMessage = error.message?.toLowerCase() || '';
		const platformErrorPatterns = [
			'platform',
			'service worker',
			'fcm',
			'beams',
			'pusher',
			'notification',
			'permission'
		];

		return platformErrorPatterns.some((pattern) => errorMessage.includes(pattern));
	}

	/**
	 * 네트워크 에러인지 확인
	 */
	function isNetworkError(error: any): boolean {
		if (!error) return false;

		// Axios 에러 확인
		if (error.code) {
			const networkErrorCodes = [
				'ECONNABORTED',
				'ECONNREFUSED',
				'ECONNRESET',
				'ETIMEDOUT',
				'ENOTFOUND',
				'ENETUNREACH',
				'ENETDOWN'
			];
			return networkErrorCodes.includes(error.code);
		}

		// HTTP 상태 코드 확인
		if (error.response?.status) {
			const retryableStatusCodes = [408, 429, 500, 502, 503, 504];
			return retryableStatusCodes.includes(error.response.status);
		}

		// 에러 메시지 패턴 확인
		const errorMessage = error.message?.toLowerCase() || '';
		const networkErrorPatterns = [
			'network',
			'connection',
			'timeout',
			'dns',
			'ssl',
			'certificate',
			'fetch'
		];

		return networkErrorPatterns.some((pattern) => errorMessage.includes(pattern));
	}

	/**
	 * Pusher Beams 연결 에러인지 확인
	 */
	function isBeamsConnectionError(error: any): boolean {
		if (!error) return false;

		const errorMessage = error.message?.toLowerCase() || '';
		const beamsErrorPatterns = ['beams', 'pusher', 'instance', 'websocket', 'subscription'];

		return beamsErrorPatterns.some((pattern) => errorMessage.includes(pattern));
	}

	/**
	 * 에러를 플랫폼별 에러 정보로 분석
	 */
	function analyzePlatformError(error: any): PlatformErrorInfo {
		console.log('[PlatformErrorHandler] 플랫폼별 에러 분석:', {
			platform: state.currentPlatform,
			error: error.message || error,
			type: error.type
		});

		// 기존 platformService 기반 플랫폼 확인
		const platform = getCurrentPlatform();

		// 플랫폼별 에러 분석
		if (platform === 'desktop' && isDesktop()) {
			return analyzeDesktopError(error);
		} else if (platform === 'android' && isAndroid()) {
			return analyzeAndroidError(error);
		} else if (isTauri()) {
			return analyzeTauriError(error);
		} else {
			return analyzeGenericPlatformError(error, platform);
		}
	}

	/**
	 * 데스크탑 에러 분석
	 */
	function analyzeDesktopError(error: any): PlatformErrorInfo {
		const errorMessage = error.message?.toLowerCase() || '';

		// Service Worker 에러
		if (errorMessage.includes('service worker') || error.type === 'SERVICE_WORKER_ERROR') {
			return {
				platform: 'desktop',
				errorType: 'SERVICE_WORKER_ERROR',
				message: 'Service Worker 관련 오류가 발생했습니다.',
				canRetry: true,
				retryConfig: state.platformRetryConfigs.get('desktop'),
				platformSpecific: {
					serviceWorkerSupported: 'serviceWorker' in navigator,
					pushManagerSupported: 'PushManager' in window
				},
				originalError: error
			};
		}

		// Pusher Beams 연결 에러
		if (isBeamsConnectionError(error)) {
			return {
				platform: 'desktop',
				errorType: 'BEAMS_CONNECTION_ERROR',
				message: 'Pusher Beams 연결에 실패했습니다.',
				canRetry: true,
				retryConfig: state.platformRetryConfigs.get('desktop'),
				platformSpecific: {
					instanceId: import.meta.env.VITE_PUSHER_BEAMS_INSTANCE_ID,
					webSocketSupported: 'WebSocket' in window
				},
				originalError: error
			};
		}

		// 네트워크 에러
		if (isNetworkError(error)) {
			return {
				platform: 'desktop',
				errorType: 'NETWORK_ERROR',
				message: '네트워크 연결 오류가 발생했습니다.',
				canRetry: true,
				retryConfig: state.platformRetryConfigs.get('desktop'),
				platformSpecific: {
					online: navigator.onLine,
					connectionType: (navigator as any).connection?.effectiveType
				},
				originalError: error
			};
		}

		// 권한 에러
		if (errorMessage.includes('permission') || errorMessage.includes('denied')) {
			return {
				platform: 'desktop',
				errorType: 'PERMISSION_ERROR',
				message: '알림 권한이 거부되었습니다.',
				canRetry: false,
				platformSpecific: {
					notificationPermission:
						typeof Notification !== 'undefined' ? Notification.permission : 'unavailable'
				},
				originalError: error
			};
		}

		// 일반 데스크탑 에러
		return {
			platform: 'desktop',
			errorType: 'DESKTOP_ERROR',
			message: '데스크탑 푸시 서비스 오류가 발생했습니다.',
			canRetry: true,
			retryConfig: state.platformRetryConfigs.get('desktop'),
			originalError: error
		};
	}

	/**
	 * 안드로이드 에러 분석
	 */
	function analyzeAndroidError(error: any): PlatformErrorInfo {
		const errorMessage = error.message?.toLowerCase() || '';

		// FCM 에러
		if (errorMessage.includes('fcm') || error.type === 'FCM_ERROR') {
			return {
				platform: 'android',
				errorType: 'FCM_ERROR',
				message: 'FCM 서비스 오류가 발생했습니다.',
				canRetry: true,
				retryConfig: state.platformRetryConfigs.get('android'),
				platformSpecific: {
					fcmSupported: true,
					playServicesAvailable: true // Tauri 환경에서는 일반적으로 사용 가능
				},
				originalError: error
			};
		}

		// Pusher Beams 연결 에러
		if (isBeamsConnectionError(error)) {
			return {
				platform: 'android',
				errorType: 'BEAMS_CONNECTION_ERROR',
				message: 'Pusher Beams 연결에 실패했습니다.',
				canRetry: true,
				retryConfig: state.platformRetryConfigs.get('android'),
				platformSpecific: {
					instanceId: import.meta.env.VITE_PUSHER_BEAMS_INSTANCE_ID
				},
				originalError: error
			};
		}

		// 네트워크 에러
		if (isNetworkError(error)) {
			return {
				platform: 'android',
				errorType: 'NETWORK_ERROR',
				message: '네트워크 연결 오류가 발생했습니다.',
				canRetry: true,
				retryConfig: state.platformRetryConfigs.get('android'),
				platformSpecific: {
					connectionType: 'mobile' // 안드로이드는 일반적으로 모바일 연결
				},
				originalError: error
			};
		}

		// 권한 에러
		if (errorMessage.includes('permission') || errorMessage.includes('denied')) {
			return {
				platform: 'android',
				errorType: 'PERMISSION_ERROR',
				message: '푸시 알림 권한이 거부되었습니다.',
				canRetry: false,
				platformSpecific: {
					permissionStatus: 'denied'
				},
				originalError: error
			};
		}

		// 일반 안드로이드 에러
		return {
			platform: 'android',
			errorType: 'ANDROID_ERROR',
			message: '안드로이드 푸시 서비스 오류가 발생했습니다.',
			canRetry: true,
			retryConfig: state.platformRetryConfigs.get('android'),
			originalError: error
		};
	}

	/**
	 * Tauri 에러 분석
	 */
	function analyzeTauriError(error: any): PlatformErrorInfo {
		const errorMessage = error.message?.toLowerCase() || '';

		// Tauri API 에러
		if (errorMessage.includes('tauri') || errorMessage.includes('invoke')) {
			return {
				platform: 'tauri',
				errorType: 'TAURI_ERROR',
				message: 'Tauri API 호출 오류가 발생했습니다.',
				canRetry: true,
				retryConfig: state.defaultRetryConfig,
				platformSpecific: {
					tauriVersion: (window as any).__TAURI__?.version || 'unknown'
				},
				originalError: error
			};
		}

		// 네트워크 에러
		if (isNetworkError(error)) {
			return {
				platform: 'tauri',
				errorType: 'NETWORK_ERROR',
				message: 'Tauri 환경에서 네트워크 오류가 발생했습니다.',
				canRetry: true,
				retryConfig: state.defaultRetryConfig,
				originalError: error
			};
		}

		// 일반 Tauri 에러
		return {
			platform: 'tauri',
			errorType: 'TAURI_ERROR',
			message: 'Tauri 환경에서 오류가 발생했습니다.',
			canRetry: true,
			retryConfig: state.defaultRetryConfig,
			originalError: error
		};
	}

	/**
	 * 일반 플랫폼 에러 분석
	 */
	function analyzeGenericPlatformError(error: any, platform: string): PlatformErrorInfo {
		// 지원되지 않는 플랫폼
		if (!['desktop', 'android', 'web'].includes(platform)) {
			return {
				platform,
				errorType: 'PLATFORM_NOT_SUPPORTED',
				message: `지원되지 않는 플랫폼입니다: ${platform}`,
				canRetry: false,
				originalError: error
			};
		}

		// 네트워크 에러
		if (isNetworkError(error)) {
			return {
				platform,
				errorType: 'NETWORK_ERROR',
				message: '네트워크 연결 오류가 발생했습니다.',
				canRetry: true,
				retryConfig: state.defaultRetryConfig,
				originalError: error
			};
		}

		// Pusher Beams 연결 에러
		if (isBeamsConnectionError(error)) {
			return {
				platform,
				errorType: 'BEAMS_CONNECTION_ERROR',
				message: 'Pusher Beams 연결에 실패했습니다.',
				canRetry: true,
				retryConfig: state.defaultRetryConfig,
				originalError: error
			};
		}

		// 일반 에러
		return {
			platform,
			errorType: 'PLATFORM_NOT_SUPPORTED',
			message: '플랫폼별 처리가 필요한 오류가 발생했습니다.',
			canRetry: true,
			retryConfig: state.defaultRetryConfig,
			originalError: error
		};
	}

	/**
	 * 네트워크 오류 시 재시도 메커니즘
	 */
	async function retryWithBackoff<T>(
		operation: () => Promise<T>,
		errorKey: string,
		customRetryConfig?: Partial<RetryConfig>
	): Promise<T> {
		const retryConfig = {
			...state.defaultRetryConfig,
			...state.platformRetryConfigs.get(state.currentPlatform),
			...customRetryConfig
		};

		console.log('[PlatformErrorHandler] 재시도 시작:', {
			errorKey,
			platform: state.currentPlatform,
			retryConfig
		});

		const startTime = Date.now();
		let lastError: Error | null = null;

		// 이미 재시도 중인지 확인
		if (state.activeRetries.has(errorKey)) {
			throw createPushServiceError('RETRY_IN_PROGRESS', '이미 재시도가 진행 중입니다.');
		}

		state.activeRetries.add(errorKey);

		try {
			for (let attempt = 1; attempt <= retryConfig.maxAttempts; attempt++) {
				try {
					console.log(`[PlatformErrorHandler] 재시도 시도 ${attempt}/${retryConfig.maxAttempts}`);

					// 첫 번째 시도가 아니면 백오프 대기
					if (attempt > 1) {
						const delay = Math.min(
							retryConfig.baseDelayMs * Math.pow(retryConfig.backoffMultiplier, attempt - 2),
							retryConfig.maxDelayMs
						);

						console.log(`[PlatformErrorHandler] ${delay}ms 대기 중...`);
						await new Promise((resolve) => setTimeout(resolve, delay));
					}

					// 작업 실행
					const result = await operation();

					// 성공 시 재시도 상태 초기화
					state.retryAttempts.delete(errorKey);
					state.lastRetryTime.delete(errorKey);

					const totalTime = Date.now() - startTime;
					console.log('[PlatformErrorHandler] 재시도 성공:', {
						attempt,
						totalTimeMs: totalTime
					});

					return result;
				} catch (error) {
					lastError = error as Error;
					console.warn(`[PlatformErrorHandler] 재시도 ${attempt} 실패:`, error);

					// 재시도 가능한 에러인지 확인
					const platformError = analyzePlatformError(error);
					if (!platformError.canRetry || !isRetryableError(error, retryConfig)) {
						console.log('[PlatformErrorHandler] 재시도 불가능한 에러 - 중단');
						throw error;
					}

					// 마지막 시도인 경우 에러 throw
					if (attempt === retryConfig.maxAttempts) {
						console.error('[PlatformErrorHandler] 모든 재시도 실패');
						throw error;
					}
				}
			}

			// 여기에 도달하면 모든 재시도 실패
			throw lastError || new Error('모든 재시도가 실패했습니다.');
		} finally {
			state.activeRetries.delete(errorKey);
			state.retryAttempts.set(errorKey, (state.retryAttempts.get(errorKey) || 0) + 1);
			state.lastRetryTime.set(errorKey, Date.now());
		}
	}

	/**
	 * 재시도 가능한 에러인지 확인
	 */
	function isRetryableError(error: any, retryConfig: RetryConfig): boolean {
		// 네트워크 에러는 항상 재시도 가능
		if (isNetworkError(error)) {
			return true;
		}

		// PushServiceError 타입 확인
		if (error.type && retryConfig.retryableErrors.includes(error.type)) {
			return true;
		}

		// HTTP 상태 코드 확인
		if (error.response?.status) {
			const retryableStatusCodes = [408, 429, 500, 502, 503, 504];
			return retryableStatusCodes.includes(error.response.status);
		}

		return false;
	}

	/**
	 * 플랫폼별 에러 처리
	 */
	async function handlePlatformError(error: any): Promise<any> {
		try {
			console.log('[PlatformErrorHandler] 플랫폼별 에러 처리 시작:', error);

			// 에러 분석
			const platformError = analyzePlatformError(error);
			console.log('[PlatformErrorHandler] 플랫폼별 에러 분석 결과:', platformError);

			// 재시도 불가능한 에러 처리
			if (!platformError.canRetry) {
				console.warn('[PlatformErrorHandler] 재시도 불가능한 에러:', platformError.errorType);
				throw createPushServiceError(
					platformError.errorType,
					platformError.message,
					platformError.originalError
				);
			}

			// 플랫폼별 특수 처리
			switch (platformError.errorType) {
				case 'PERMISSION_ERROR':
					return await handlePermissionError(platformError);

				case 'SERVICE_WORKER_ERROR':
					return await handleServiceWorkerError(platformError);

				case 'FCM_ERROR':
					return await handleFCMError(platformError);

				case 'BEAMS_CONNECTION_ERROR':
					return await handleBeamsConnectionError(platformError);

				case 'NETWORK_ERROR':
					return await handleNetworkError(platformError);

				default:
					return await handleGenericPlatformError(platformError);
			}
		} catch (handlingError) {
			console.error('[PlatformErrorHandler] 플랫폼별 에러 처리 실패:', handlingError);
			throw handlingError;
		}
	}

	/**
	 * 권한 에러 처리
	 */
	async function handlePermissionError(platformError: PlatformErrorInfo): Promise<void> {
		console.log('[PlatformErrorHandler] 권한 에러 처리:', platformError.platform);

		// 플랫폼별 권한 처리
		if (platformError.platform === 'desktop') {
			// 데스크탑: 브라우저 알림 권한 재요청 안내
			throw createPushServiceError(
				'PERMISSION_ERROR',
				'브라우저 설정에서 알림 권한을 허용해주세요.',
				platformError.originalError
			);
		} else if (platformError.platform === 'android') {
			// 안드로이드: 앱 설정에서 알림 권한 재요청 안내
			throw createPushServiceError(
				'PERMISSION_ERROR',
				'앱 설정에서 알림 권한을 허용해주세요.',
				platformError.originalError
			);
		}

		throw createPushServiceError(
			'PERMISSION_ERROR',
			platformError.message,
			platformError.originalError
		);
	}

	/**
	 * Service Worker 에러 처리
	 */
	async function handleServiceWorkerError(platformError: PlatformErrorInfo): Promise<void> {
		console.log('[PlatformErrorHandler] Service Worker 에러 처리');

		// Service Worker 재등록 시도
		const errorKey = 'service_worker_error';

		await retryWithBackoff(
			async () => {
				if (typeof window !== 'undefined' && 'serviceWorker' in navigator) {
					// 기존 Service Worker 해제
					const registrations = await navigator.serviceWorker.getRegistrations();
					for (const registration of registrations) {
						await registration.unregister();
					}

					// Service Worker 재등록
					const registration = await navigator.serviceWorker.register('/service-worker.js');
					console.log('[PlatformErrorHandler] Service Worker 재등록 완료:', registration.scope);
					return registration;
				} else {
					throw new Error('Service Worker를 지원하지 않는 환경입니다.');
				}
			},
			errorKey,
			platformError.retryConfig
		);
	}

	/**
	 * FCM 에러 처리
	 */
	async function handleFCMError(platformError: PlatformErrorInfo): Promise<void> {
		console.log('[PlatformErrorHandler] FCM 에러 처리');

		const errorKey = 'fcm_error';

		await retryWithBackoff(
			async () => {
				// FCM 토큰 재획득 시도 (실제 구현은 androidPushService에서)
				console.log('[PlatformErrorHandler] FCM 토큰 재획득 시도');

				// 여기서는 에러 복구 시뮬레이션
				await new Promise((resolve) => setTimeout(resolve, 1000));

				return 'fcm_recovery_success';
			},
			errorKey,
			platformError.retryConfig
		);
	}

	/**
	 * Pusher Beams 연결 에러 처리
	 */
	async function handleBeamsConnectionError(platformError: PlatformErrorInfo): Promise<void> {
		console.log('[PlatformErrorHandler] Pusher Beams 연결 에러 처리');

		const errorKey = 'beams_connection_error';

		await retryWithBackoff(
			async () => {
				// Pusher Beams 재연결 시도
				console.log('[PlatformErrorHandler] Pusher Beams 재연결 시도');

				// 환경 변수 확인
				const instanceId = import.meta.env.VITE_PUSHER_BEAMS_INSTANCE_ID;
				if (!instanceId) {
					throw new Error('VITE_PUSHER_BEAMS_INSTANCE_ID 환경 변수가 설정되지 않았습니다.');
				}

				// 연결 테스트 (실제 구현은 각 푸시 서비스에서)
				await new Promise((resolve) => setTimeout(resolve, 2000));

				return 'beams_reconnection_success';
			},
			errorKey,
			platformError.retryConfig
		);
	}

	/**
	 * 네트워크 에러 처리
	 */
	async function handleNetworkError(platformError: PlatformErrorInfo): Promise<void> {
		console.log('[PlatformErrorHandler] 네트워크 에러 처리');

		const errorKey = 'network_error';

		await retryWithBackoff(
			async () => {
				// 네트워크 연결 상태 확인
				if (typeof navigator !== 'undefined' && !navigator.onLine) {
					throw new Error('네트워크 연결이 끊어져 있습니다.');
				}

				// 간단한 연결 테스트
				const testUrl = `${import.meta.env.VITE_HOME_URL}/api/health`;
				const response = await fetch(testUrl, {
					method: 'GET'
				});

				if (!response.ok) {
					throw new Error(`네트워크 테스트 실패: ${response.status}`);
				}

				console.log('[PlatformErrorHandler] 네트워크 연결 복구 확인');
				return 'network_recovery_success';
			},
			errorKey,
			platformError.retryConfig
		);
	}

	/**
	 * 일반 플랫폼 에러 처리
	 */
	async function handleGenericPlatformError(platformError: PlatformErrorInfo): Promise<void> {
		console.log('[PlatformErrorHandler] 일반 플랫폼 에러 처리:', platformError.errorType);

		const errorKey = `generic_${platformError.errorType}`;

		await retryWithBackoff(
			async () => {
				// 일반적인 복구 시도
				console.log('[PlatformErrorHandler] 일반 에러 복구 시도');

				await new Promise((resolve) => setTimeout(resolve, 1000));

				return 'generic_recovery_success';
			},
			errorKey,
			platformError.retryConfig
		);
	}

	/**
	 * 재시도 상태 초기화
	 */
	function resetRetryState(errorKey?: string): void {
		if (errorKey) {
			state.retryAttempts.delete(errorKey);
			state.lastRetryTime.delete(errorKey);
			state.activeRetries.delete(errorKey);
			console.log(`[PlatformErrorHandler] 재시도 상태 초기화: ${errorKey}`);
		} else {
			state.retryAttempts.clear();
			state.lastRetryTime.clear();
			state.activeRetries.clear();
			console.log('[PlatformErrorHandler] 모든 재시도 상태 초기화');
		}
	}

	/**
	 * 현재 재시도 상태 조회
	 */
	function getRetryState() {
		return {
			currentPlatform: state.currentPlatform,
			activeRetries: Array.from(state.activeRetries),
			retryAttempts: Object.fromEntries(state.retryAttempts),
			lastRetryTime: Object.fromEntries(state.lastRetryTime)
		};
	}

	/**
	 * 플랫폼별 재시도 설정 업데이트
	 */
	function updatePlatformRetryConfig(platform: string, config: Partial<RetryConfig>): void {
		const currentConfig = state.platformRetryConfigs.get(platform) || state.defaultRetryConfig;
		const updatedConfig = { ...currentConfig, ...config };
		state.platformRetryConfigs.set(platform, updatedConfig);
		console.log(`[PlatformErrorHandler] ${platform} 재시도 설정 업데이트:`, updatedConfig);
	}

	return {
		// 에러 분석 및 처리
		isPlatformError,
		analyzePlatformError,
		handlePlatformError,

		// 네트워크 에러 처리
		isNetworkError,
		retryWithBackoff,

		// Pusher Beams 연결 에러 처리
		isBeamsConnectionError,

		// 상태 관리
		resetRetryState,
		getRetryState,
		updatePlatformRetryConfig,

		// 개별 에러 처리 메서드들 (테스트용)
		handlePermissionError,
		handleServiceWorkerError,
		handleFCMError,
		handleBeamsConnectionError,
		handleNetworkError
	};
}

/**
 * 전역 플랫폼 에러 핸들러 인스턴스
 */
let globalPlatformErrorHandler: ReturnType<typeof createPushServicePlatformErrorHandler> | null =
	null;

/**
 * 전역 플랫폼 에러 핸들러 인스턴스 획득
 */
export function getPushServicePlatformErrorHandler(): ReturnType<
	typeof createPushServicePlatformErrorHandler
> {
	if (!globalPlatformErrorHandler) {
		globalPlatformErrorHandler = createPushServicePlatformErrorHandler();
	}
	return globalPlatformErrorHandler;
}

/**
 * 플랫폼별 에러 처리 (편의 함수)
 */
export async function handlePushServicePlatformError(error: any): Promise<any> {
	const handler = getPushServicePlatformErrorHandler();
	return await handler.handlePlatformError(error);
}

/**
 * 네트워크 에러 재시도 (편의 함수)
 */
export async function retryPushServiceOperation<T>(
	operation: () => Promise<T>,
	errorKey: string,
	customRetryConfig?: Partial<RetryConfig>
): Promise<T> {
	const handler = getPushServicePlatformErrorHandler();
	return await handler.retryWithBackoff(operation, errorKey, customRetryConfig);
}

/**
 * 플랫폼별 에러 여부 확인 (편의 함수)
 */
export function isPushServicePlatformError(error: any): boolean {
	const handler = getPushServicePlatformErrorHandler();
	return handler.isPlatformError(error);
}
