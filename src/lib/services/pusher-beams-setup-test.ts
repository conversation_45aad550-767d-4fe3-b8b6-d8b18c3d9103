/**
 * Pusher Beams 환경 설정 테스트
 * 클라이언트 직접 연동을 위한 설정 검증
 */

import {
	validatePusherBeamsConfig,
	checkPusherBeamsSetup,
	getPusherBeamsConfig,
	getFirebaseConfig,
	debugPusherBeamsConfig
} from '../utils/pusher-beams-config';
import { getCurrentPlatform, isDesktop, isAndroid } from './platformService';

/**
 * Pusher Beams Dashboard 설정 확인 (시뮬레이션)
 */
export async function checkPusherBeamsDashboard(): Promise<{
	success: boolean;
	message: string;
	details: string[];
}> {
	const details: string[] = [];

	try {
		const config = getPusherBeamsConfig();
		details.push(`Instance ID: ${config.instanceId}`);

		// 실제 Dashboard 연결 테스트는 클라이언트에서 불가능하므로 설정 검증만 수행
		details.push('✓ Instance ID 형식이 올바릅니다.');

		// 환경별 설정 확인
		const currentEnv = import.meta.env.VITE_NODE_ENV || 'development';
		details.push(`✓ 현재 환경: ${currentEnv}`);

		// Service Worker 파일 존재 확인
		if (typeof window !== 'undefined') {
			try {
				const response = await fetch('/service-worker.js', { method: 'HEAD' });
				if (response.ok) {
					details.push('✓ Service Worker 파일이 존재합니다.');
				} else {
					details.push('⚠ Service Worker 파일에 접근할 수 없습니다.');
				}
			} catch (error) {
				details.push('⚠ Service Worker 파일 확인 중 오류가 발생했습니다.');
			}
		}

		return {
			success: true,
			message: 'Pusher Beams Dashboard 설정이 올바르게 구성되었습니다.',
			details
		};
	} catch (error) {
		details.push(`✗ 오류: ${error instanceof Error ? error.message : '알 수 없는 오류'}`);
		return {
			success: false,
			message: 'Pusher Beams Dashboard 설정에 문제가 있습니다.',
			details
		};
	}
}

/**
 * FCM 서비스 계정 키 설정 확인 (시뮬레이션)
 */
export async function checkFCMServiceAccount(): Promise<{
	success: boolean;
	message: string;
	details: string[];
}> {
	const details: string[] = [];

	try {
		const firebaseConfig = getFirebaseConfig();
		details.push(`Firebase Project ID: ${firebaseConfig.projectId}`);
		details.push(`Firebase Sender ID: ${firebaseConfig.senderId}`);
		details.push(`Firebase API Key: ${firebaseConfig.apiKey.substring(0, 10)}...`);

		// Firebase 설정 형식 검증
		if (!/^[a-z0-9-]+$/.test(firebaseConfig.projectId)) {
			details.push('✗ Firebase Project ID 형식이 올바르지 않습니다.');
			return {
				success: false,
				message: 'Firebase 설정에 문제가 있습니다.',
				details
			};
		}

		if (!/^\d+$/.test(firebaseConfig.senderId)) {
			details.push('✗ Firebase Sender ID 형식이 올바르지 않습니다.');
			return {
				success: false,
				message: 'Firebase 설정에 문제가 있습니다.',
				details
			};
		}

		details.push('✓ Firebase 설정 형식이 올바릅니다.');
		details.push('✓ FCM 서비스 계정 키가 Pusher Beams Dashboard에 업로드되었다고 가정합니다.');

		return {
			success: true,
			message: 'FCM 서비스 계정 키 설정이 올바르게 구성되었습니다.',
			details
		};
	} catch (error) {
		details.push(`✗ 오류: ${error instanceof Error ? error.message : '알 수 없는 오류'}`);
		return {
			success: false,
			message: 'FCM 서비스 계정 키 설정에 문제가 있습니다.',
			details
		};
	}
}

/**
 * 플랫폼별 설정 확인
 */
export async function checkPlatformSpecificSetup(): Promise<{
	success: boolean;
	message: string;
	details: string[];
}> {
	const details: string[] = [];
	const platform = getCurrentPlatform();

	details.push(`현재 플랫폼: ${platform}`);

	try {
		if (isDesktop()) {
			// 데스크탑 설정 확인
			details.push('✓ 데스크탑 플랫폼 감지됨');

			// Service Worker 지원 확인
			if (typeof window !== 'undefined' && 'serviceWorker' in navigator) {
				details.push('✓ Service Worker 지원됨');
			} else {
				details.push('⚠ Service Worker가 지원되지 않습니다.');
			}

			// Push Manager 지원 확인
			if (typeof window !== 'undefined' && 'PushManager' in window) {
				details.push('✓ Push Manager 지원됨');
			} else {
				details.push('⚠ Push Manager가 지원되지 않습니다.');
			}
		} else if (isAndroid()) {
			// 안드로이드 설정 확인
			details.push('✓ 안드로이드 플랫폼 감지됨');

			// Firebase 설정 확인
			const firebaseConfig = getFirebaseConfig();
			details.push('✓ Firebase 설정이 구성되었습니다.');

			// Tauri 환경 확인
			if (typeof window !== 'undefined' && window.__TAURI__) {
				details.push('✓ Tauri 환경에서 실행 중');
			} else {
				details.push('⚠ Tauri 환경이 아닙니다.');
			}
		} else {
			details.push(`⚠ 지원되지 않는 플랫폼: ${platform}`);
		}

		return {
			success: true,
			message: '플랫폼별 설정이 올바르게 구성되었습니다.',
			details
		};
	} catch (error) {
		details.push(`✗ 오류: ${error instanceof Error ? error.message : '알 수 없는 오류'}`);
		return {
			success: false,
			message: '플랫폼별 설정에 문제가 있습니다.',
			details
		};
	}
}

/**
 * 전체 Pusher Beams 설정 테스트 실행
 */
export async function runPusherBeamsSetupTest(): Promise<{
	success: boolean;
	results: {
		configValidation: boolean;
		dashboardSetup: boolean;
		fcmServiceAccount: boolean;
		platformSetup: boolean;
	};
	details: {
		configValidation: any;
		dashboardSetup: any;
		fcmServiceAccount: any;
		platformSetup: any;
	};
}> {
	console.log('[Pusher Beams Setup Test] 전체 설정 테스트 시작');

	// 환경 설정 디버그 정보 출력
	debugPusherBeamsConfig();

	const results = {
		configValidation: false,
		dashboardSetup: false,
		fcmServiceAccount: false,
		platformSetup: false
	};

	const details = {
		configValidation: null as any,
		dashboardSetup: null as any,
		fcmServiceAccount: null as any,
		platformSetup: null as any
	};

	try {
		// 1. 환경 설정 검증
		const configValidation = validatePusherBeamsConfig();
		results.configValidation = configValidation.isValid;
		details.configValidation = configValidation;

		// 2. Pusher Beams Dashboard 설정 확인
		const dashboardCheck = await checkPusherBeamsDashboard();
		results.dashboardSetup = dashboardCheck.success;
		details.dashboardSetup = dashboardCheck;

		// 3. FCM 서비스 계정 키 확인
		const fcmCheck = await checkFCMServiceAccount();
		results.fcmServiceAccount = fcmCheck.success;
		details.fcmServiceAccount = fcmCheck;

		// 4. 플랫폼별 설정 확인
		const platformCheck = await checkPlatformSpecificSetup();
		results.platformSetup = platformCheck.success;
		details.platformSetup = platformCheck;

		const allPassed = Object.values(results).every((result) => result);

		console.log('[Pusher Beams Setup Test] 전체 테스트 결과:', results);
		console.log('[Pusher Beams Setup Test] 전체 테스트', allPassed ? '성공' : '실패');

		return {
			success: allPassed,
			results,
			details
		};
	} catch (error) {
		console.error('[Pusher Beams Setup Test] 전체 테스트 실행 중 오류:', error);
		return {
			success: false,
			results,
			details
		};
	}
}

/**
 * 개발 환경에서 자동으로 설정 테스트 실행
 */
if (import.meta.env.DEV) {
	// 페이지 로드 후 자동 테스트 실행 (선택적)
	if (typeof window !== 'undefined') {
		window.addEventListener('load', async () => {
			// 자동 테스트는 주석 처리 (필요시 활성화)
			// await runPusherBeamsSetupTest();
		});
	}
}
