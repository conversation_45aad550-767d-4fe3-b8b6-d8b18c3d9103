/**
 * 기존 인프라 호환성 테스트
 * Pusher Beams 구현을 위한 기존 서비스들의 호환성을 확인합니다.
 */

import { getCurrentPlatform, isDesktop, isAndroid, isTauri } from './platformService';
import { tokenService } from './tokenService';
import { initEmployeeDB, EMPLOYEE_DB_CONFIG } from '../utils/indexeddb-utils';
import {
	saveNotification,
	getAllNotifications,
	getUnreadNotificationCount,
	markNotificationAsRead,
	deleteNotification
} from '../utils/notification-database';
import type { NotificationData } from '$lib/types/notification';

/**
 * 플랫폼 서비스 호환성 테스트
 */
export async function testPlatformService(): Promise<boolean> {
	try {
		console.log('[Infrastructure Test] 플랫폼 서비스 테스트 시작');

		// 플랫폼 감지 테스트
		const platform = getCurrentPlatform();
		console.log('현재 플랫폼:', platform);

		// 플랫폼별 확인 함수 테스트
		const platformChecks = {
			isDesktop: isDesktop(),
			isAndroid: isAndroid(),
			isTauri: isTauri()
		};
		console.log('플랫폼 확인 결과:', platformChecks);

		// 플랫폼이 올바르게 감지되었는지 확인
		if (!['desktop', 'android', 'web', 'ios'].includes(platform)) {
			throw new Error(`알 수 없는 플랫폼: ${platform}`);
		}

		console.log('[Infrastructure Test] 플랫폼 서비스 테스트 성공');
		return true;
	} catch (error) {
		console.error('[Infrastructure Test] 플랫폼 서비스 테스트 실패:', error);
		return false;
	}
}

/**
 * 토큰 서비스 호환성 테스트
 */
export async function testTokenService(): Promise<boolean> {
	try {
		console.log('[Infrastructure Test] 토큰 서비스 테스트 시작');

		// 토큰 서비스 초기화
		await tokenService.initialize();
		console.log('토큰 서비스 초기화 완료');

		// 토큰 상태 확인
		const tokenStatus = await tokenService.getTokenStatus();
		console.log('토큰 상태:', tokenStatus);

		// 인증 상태 확인
		const isAuthenticated = await tokenService.isAuthenticated();
		console.log('인증 상태:', isAuthenticated);

		// 사용자 ID 조회 (토큰이 있는 경우)
		const userId = await tokenService.getCurrentUserId();
		console.log('현재 사용자 ID:', userId);

		// 액세스 토큰 조회
		const accessToken = await tokenService.getAccessToken();
		console.log('액세스 토큰 존재:', !!accessToken);

		console.log('[Infrastructure Test] 토큰 서비스 테스트 성공');
		return true;
	} catch (error) {
		console.error('[Infrastructure Test] 토큰 서비스 테스트 실패:', error);
		return false;
	}
}

/**
 * IndexedDB 및 EmployeeDB 호환성 테스트
 */
export async function testEmployeeDB(): Promise<boolean> {
	try {
		console.log('[Infrastructure Test] EmployeeDB 테스트 시작');

		// EmployeeDB 초기화
		const db = await initEmployeeDB();
		console.log('EmployeeDB 초기화 완료');

		// 데이터베이스 구조 확인
		console.log('데이터베이스 이름:', db.name);
		console.log('데이터베이스 버전:', db.version);
		console.log('스토어 목록:', Array.from(db.objectStoreNames));

		// 필수 스토어 존재 확인
		const requiredStores = ['print_settings', 'push_notifications'];
		for (const storeName of requiredStores) {
			if (!db.objectStoreNames.contains(storeName)) {
				throw new Error(`필수 스토어가 없습니다: ${storeName}`);
			}
		}

		// 설정 확인
		console.log('EmployeeDB 설정:', EMPLOYEE_DB_CONFIG);

		console.log('[Infrastructure Test] EmployeeDB 테스트 성공');
		return true;
	} catch (error) {
		console.error('[Infrastructure Test] EmployeeDB 테스트 실패:', error);
		return false;
	}
}

/**
 * 알림 데이터베이스 호환성 테스트
 */
export async function testNotificationDatabase(): Promise<boolean> {
	try {
		console.log('[Infrastructure Test] 알림 데이터베이스 테스트 시작');

		// 테스트 알림 데이터
		const testNotification: NotificationData = {
			id: `test_notification_${Date.now()}`,
			content: '인프라 호환성 테스트 알림',
			expire_day: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
			created_at: new Date().toISOString(),
			read: false,
			priority: 'normal',
			category: 'test',
			message: '테스트 알림'
		};

		// 알림 저장 테스트
		await saveNotification(testNotification);
		console.log('테스트 알림 저장 완료');

		// 알림 조회 테스트
		const allNotifications = await getAllNotifications();
		console.log('전체 알림 개수:', allNotifications.length);

		// 읽지 않은 알림 개수 테스트
		const unreadCount = await getUnreadNotificationCount();
		console.log('읽지 않은 알림 개수:', unreadCount);

		// 알림 읽음 처리 테스트
		await markNotificationAsRead(testNotification.id);
		console.log('테스트 알림 읽음 처리 완료');

		// 알림 삭제 테스트
		await deleteNotification(testNotification.id);
		console.log('테스트 알림 삭제 완료');

		console.log('[Infrastructure Test] 알림 데이터베이스 테스트 성공');
		return true;
	} catch (error) {
		console.error('[Infrastructure Test] 알림 데이터베이스 테스트 실패:', error);
		return false;
	}
}

/**
 * 전체 인프라 호환성 테스트 실행
 */
export async function runInfrastructureCompatibilityTest(): Promise<{
	success: boolean;
	results: {
		platformService: boolean;
		tokenService: boolean;
		employeeDB: boolean;
		notificationDatabase: boolean;
	};
}> {
	console.log('[Infrastructure Test] 전체 인프라 호환성 테스트 시작');

	const results = {
		platformService: false,
		tokenService: false,
		employeeDB: false,
		notificationDatabase: false
	};

	try {
		// 각 서비스별 테스트 실행
		results.platformService = await testPlatformService();
		results.tokenService = await testTokenService();
		results.employeeDB = await testEmployeeDB();
		results.notificationDatabase = await testNotificationDatabase();

		const allPassed = Object.values(results).every((result) => result);

		console.log('[Infrastructure Test] 전체 테스트 결과:', results);
		console.log('[Infrastructure Test] 전체 테스트', allPassed ? '성공' : '실패');

		return {
			success: allPassed,
			results
		};
	} catch (error) {
		console.error('[Infrastructure Test] 전체 테스트 실행 중 오류:', error);
		return {
			success: false,
			results
		};
	}
}

/**
 * 개발 환경에서 자동으로 호환성 테스트 실행
 */
if (import.meta.env.DEV) {
	// 페이지 로드 후 자동 테스트 실행 (선택적)
	if (typeof window !== 'undefined') {
		window.addEventListener('load', async () => {
			// 자동 테스트는 주석 처리 (필요시 활성화)
			// await runInfrastructureCompatibilityTest();
		});
	}
}
