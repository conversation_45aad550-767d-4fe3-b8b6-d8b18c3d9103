/**
 * 안드로이드용 푸시 알림 서비스 (완전 새로 작성)
 *
 * 요구사항:
 * - 기존 platformService.isAndroid() 활용하여 플랫폼 확인
 * - 기존 tokenService 활용하여 JWT 토큰 관리
 * - FCM 토큰 획득 최적화
 * - Interest 관련 코드 완전 제거
 * - 백엔드 연동 코드 완전 제거
 * - 기존 saveNotification() 함수로 IndexedDB 저장
 * - redirect 필드 제거됨 (action_url 사용하지 않음)
 *
 * 중요: action_url 필드는 사용하지 않습니다!
 */

import {
	getToken,
	requestPermission,
	onNotificationReceived,
	onTokenRefresh
} from 'tauri-plugin-remote-push-api';
import { isAndroid } from '$lib/services/platformService';
import { tokenService } from '$lib/services/tokenService';
import { saveNotification } from '$lib/utils/notification-database';
import type { NotificationData, CreateNotificationData } from '$lib/types/notification';
import type { PermissionStatus, NotificationCallback } from '../types/pushNotificationTypes';
import { createPushServiceError } from '../types/pushNotificationTypes';

/**
 * 서비스 상태
 */
interface ServiceState {
	isInitialized: boolean;
	fcmToken: string | null;
	userId: string | null;
	callbacks: Set<NotificationCallback>;
	unsubscribeNotifications?: () => void;
	unsubscribeTokenRefresh?: () => void;
}

/**
 * 안드로이드 푸시 서비스 생성
 */
export function createAndroidPushService() {
	const state: ServiceState = {
		isInitialized: false,
		fcmToken: null,
		userId: null,
		callbacks: new Set()
	};

	/**
	 * 서비스 초기화
	 */
	async function initialize(): Promise<void> {
		console.log('[AndroidPush] 초기화 시작');

		// 플랫폼 확인
		if (!isAndroid()) {
			throw createPushServiceError('PLATFORM_NOT_SUPPORTED', '안드로이드가 아닙니다');
		}

		// 사용자 ID 획득
		try {
			state.userId = await tokenService.getCurrentUserId();
			console.log('[AndroidPush] 사용자 ID:', state.userId ? '획득' : '없음');
		} catch (error) {
			console.warn('[AndroidPush] 사용자 ID 획득 실패:', error);
		}

		// FCM 토큰 획득
		await acquireFCMToken();

		// 알림 리스너 등록
		await setupNotificationListeners();

		state.isInitialized = true;
		console.log('[AndroidPush] 초기화 완료');
	}

	/**
	 * FCM 토큰 획득
	 */
	async function acquireFCMToken(): Promise<void> {
		try {
			const token = await getToken();
			state.fcmToken = token;
			console.log('[AndroidPush] FCM 토큰 획득:', token.substring(0, 20) + '...');
		} catch (error) {
			console.error('[AndroidPush] FCM 토큰 획득 실패:', error);
			// 개발 환경에서는 더미 토큰 생성
			if (import.meta.env.DEV) {
				state.fcmToken = `dev_token_${Date.now()}`;
				console.log('[AndroidPush] 개발용 토큰 생성');
			} else {
				throw error;
			}
		}
	}

	/**
	 * 알림 리스너 설정
	 */
	async function setupNotificationListeners(): Promise<void> {
		try {
			// 알림 수신 리스너
			const notificationListener = await onNotificationReceived(handleIncomingNotification);
			state.unsubscribeNotifications = () => {
				if (
					notificationListener &&
					typeof notificationListener === 'object' &&
					'unregister' in notificationListener
				) {
					(notificationListener as any).unregister();
				}
			};

			// 토큰 갱신 리스너
			const tokenListener = await onTokenRefresh((newToken) => {
				console.log('[AndroidPush] 토큰 갱신:', newToken.substring(0, 20) + '...');
				state.fcmToken = newToken;
			});
			state.unsubscribeTokenRefresh = () => {
				if (tokenListener && typeof tokenListener === 'object' && 'unregister' in tokenListener) {
					(tokenListener as any).unregister();
				}
			};

			console.log('[AndroidPush] 리스너 등록 완료');
		} catch (error) {
			console.error('[AndroidPush] 리스너 등록 실패:', error);
			if (!import.meta.env.DEV) {
				throw error;
			}
		}
	}

	/**
	 * 알림 수신 처리
	 */
	async function handleIncomingNotification(notification: any): Promise<void> {
		try {
			console.log('[AndroidPush] 알림 수신:', notification);

			// 알림 데이터 변환
			const notificationData: NotificationData = {
				id:
					notification.messageId ||
					notification.id ||
					`android_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
				content: notification.body || notification.content || '새 알림',
				expire_day:
					notification.expire_day ||
					new Date(Date.now() + 6 * 30 * 24 * 60 * 60 * 1000).toISOString(),
				created_at: new Date().toISOString(),
				read: false,
				success: notification.success === 'true' || notification.success === true,
				message: notification.title || notification.message,
				priority: notification.priority || 'normal',
				category: notification.category,
				image_url: notification.image_url || notification.imageUrl
			};

			// IndexedDB에 저장
			await saveNotification(notificationData);
			console.log('[AndroidPush] 알림 저장 완료:', notificationData.id);

			// 콜백 실행
			state.callbacks.forEach((callback) => {
				try {
					callback(notificationData);
				} catch (error) {
					console.error('[AndroidPush] 콜백 실행 오류:', error);
				}
			});
		} catch (error) {
			console.error('[AndroidPush] 알림 처리 실패:', error);
		}
	}

	/**
	 * 권한 요청
	 */
	async function requestPermissionStatus(): Promise<PermissionStatus> {
		try {
			console.log('[AndroidPush] 권한 요청');

			if (import.meta.env.DEV) {
				return 'granted';
			}

			const permission = await requestPermission();
			return permission.granted ? 'granted' : 'denied';
		} catch (error) {
			console.error('[AndroidPush] 권한 요청 실패:', error);
			throw createPushServiceError('PERMISSION_REQUEST_FAILED', '권한 요청 실패', error as Error);
		}
	}

	/**
	 * 권한 상태 확인
	 */
	async function getPermissionStatus(): Promise<PermissionStatus> {
		if (import.meta.env.DEV) {
			return 'granted';
		}
		return 'granted'; // 실제 구현에서는 Tauri API 사용
	}

	/**
	 * FCM 토큰 반환
	 */
	async function getDeviceToken(): Promise<string> {
		if (!state.isInitialized) {
			throw createPushServiceError('SERVICE_INIT_FAILED', '서비스가 초기화되지 않았습니다');
		}

		if (state.fcmToken) {
			return state.fcmToken;
		}

		// 토큰 재획득
		await acquireFCMToken();

		if (!state.fcmToken) {
			throw createPushServiceError('TOKEN_REG_FAILED', 'FCM 토큰을 획득할 수 없습니다');
		}

		return state.fcmToken;
	}

	/**
	 * Pusher Beams 등록 시뮬레이션
	 */
	async function registerWithBeams(): Promise<void> {
		console.log('[AndroidPush] Beams 등록 시뮬레이션');

		if (!state.isInitialized) {
			throw createPushServiceError('SERVICE_INIT_FAILED', '서비스가 초기화되지 않았습니다');
		}

		const fcmToken = await getDeviceToken();
		const userId = await tokenService.getCurrentUserId();

		// 로컬 상태로 디바이스 정보 관리
		const deviceInfo = {
			deviceId: `android_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
			fcmToken,
			userId,
			platform: 'android',
			registeredAt: new Date().toISOString()
		};

		localStorage.setItem('cnsprowms_android_beams_device', JSON.stringify(deviceInfo));
		console.log('[AndroidPush] Beams 등록 시뮬레이션 완료');
	}

	/**
	 * 알림 콜백 등록
	 */
	function onNotificationReceivedCallback(callback: NotificationCallback): void {
		state.callbacks.add(callback);
	}

	/**
	 * 알림 콜백 제거
	 */
	function offNotificationReceivedCallback(callback: NotificationCallback): void {
		state.callbacks.delete(callback);
	}

	/**
	 * 로컬 알림 표시
	 */
	async function showNotification(notification: CreateNotificationData): Promise<void> {
		const fullNotification: NotificationData = {
			id: `local_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
			created_at: new Date().toISOString(),
			read: false,
			...notification
		};

		await saveNotification(fullNotification);

		state.callbacks.forEach((callback) => {
			try {
				callback(fullNotification);
			} catch (error) {
				console.error('[AndroidPush] 로컬 알림 콜백 오류:', error);
			}
		});
	}

	/**
	 * 정리
	 */
	function cleanup(): void {
		if (state.unsubscribeNotifications) {
			state.unsubscribeNotifications();
		}
		if (state.unsubscribeTokenRefresh) {
			state.unsubscribeTokenRefresh();
		}

		state.callbacks.clear();
		state.isInitialized = false;
		state.fcmToken = null;
		state.userId = null;

		console.log('[AndroidPush] 정리 완료');
	}

	return {
		// 기본 메서드
		initialize,
		requestPermission: requestPermissionStatus,
		getPermissionStatus,
		onNotificationReceived: onNotificationReceivedCallback,
		offNotificationReceived: offNotificationReceivedCallback,
		showNotification,
		cleanup,

		// FCM 관련
		getDeviceToken,

		// Beams 시뮬레이션
		registerWithBeams
	};
}
