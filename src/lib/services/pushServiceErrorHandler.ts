/**
 * 푸시 서비스 에러 처리 및 복구 시스템
 *
 * 기존 토큰 서비스와 연동하여 JWT 토큰 관련 에러를 처리하고
 * 자동 복구 메커니즘을 제공합니다.
 *
 * Requirements: 5.1.1, 5.1.2, 5.1.3
 */

import { tokenService } from './tokenService';
import { tokenRefreshService } from './tokenRefreshService';
import type { PushServiceError } from '$lib/types/pushNotificationTypes';
import { createPushServiceError } from '$lib/types/pushNotificationTypes';

/**
 * 토큰 에러 타입
 */
export type TokenErrorType =
	| 'TOKEN_EXPIRED'
	| 'TOKEN_INVALID'
	| 'TOKEN_MISSING'
	| 'REFRESH_FAILED'
	| 'AUTH_REQUIRED';

/**
 * 토큰 에러 정보
 */
export interface TokenErrorInfo {
	type: TokenErrorType;
	message: string;
	canRetry: boolean;
	retryAfterMs?: number;
	originalError?: any;
}

/**
 * 에러 복구 결과
 */
export interface ErrorRecoveryResult {
	success: boolean;
	newToken?: string;
	error?: PushServiceError;
	shouldRetry: boolean;
	retryAfterMs?: number;
}

/**
 * 에러 처리 상태
 */
interface ErrorHandlerState {
	isRecovering: boolean;
	lastRecoveryAttempt: number;
	recoveryAttempts: number;
	maxRecoveryAttempts: number;
	recoveryBackoffMs: number;
}

/**
 * 푸시 서비스 에러 핸들러 생성
 */
export function createPushServiceErrorHandler() {
	const state: ErrorHandlerState = {
		isRecovering: false,
		lastRecoveryAttempt: 0,
		recoveryAttempts: 0,
		maxRecoveryAttempts: 3,
		recoveryBackoffMs: 2000 // 2초
	};

	/**
	 * 토큰 관련 에러인지 확인
	 */
	function isTokenError(error: any): boolean {
		if (!error) return false;

		// PushServiceError 타입 확인
		if (error.type) {
			return [
				'TOKEN_EXPIRED',
				'TOKEN_INVALID',
				'TOKEN_MISSING',
				'REFRESH_FAILED',
				'AUTH_REQUIRED'
			].includes(error.type);
		}

		// HTTP 에러 상태 코드 확인
		if (error.response?.status === 401 || error.status === 401) {
			return true;
		}

		// 에러 메시지 패턴 확인
		const errorMessage = error.message?.toLowerCase() || '';
		const tokenErrorPatterns = [
			'token',
			'unauthorized',
			'authentication',
			'expired',
			'invalid',
			'jwt'
		];

		return tokenErrorPatterns.some((pattern) => errorMessage.includes(pattern));
	}

	/**
	 * 에러를 토큰 에러 정보로 분석
	 */
	function analyzeTokenError(error: any): TokenErrorInfo {
		console.log('[PushServiceErrorHandler] 토큰 에러 분석:', error);

		// HTTP 401 에러
		if (error.response?.status === 401 || error.status === 401) {
			return {
				type: 'TOKEN_EXPIRED',
				message: '인증 토큰이 만료되었습니다.',
				canRetry: true,
				retryAfterMs: 1000,
				originalError: error
			};
		}

		// PushServiceError 타입별 분석
		if (error.type) {
			switch (error.type) {
				case 'TOKEN_EXPIRED':
					return {
						type: 'TOKEN_EXPIRED',
						message: '액세스 토큰이 만료되었습니다.',
						canRetry: true,
						retryAfterMs: 1000,
						originalError: error
					};

				case 'TOKEN_INVALID':
					return {
						type: 'TOKEN_INVALID',
						message: '유효하지 않은 토큰입니다.',
						canRetry: true,
						retryAfterMs: 2000,
						originalError: error
					};

				case 'TOKEN_MISSING':
					return {
						type: 'TOKEN_MISSING',
						message: '인증 토큰이 없습니다.',
						canRetry: true,
						retryAfterMs: 500,
						originalError: error
					};

				case 'REFRESH_FAILED':
					return {
						type: 'REFRESH_FAILED',
						message: '토큰 갱신에 실패했습니다.',
						canRetry: false, // 리프레시 실패는 재시도 불가
						originalError: error
					};

				case 'AUTH_REQUIRED':
					return {
						type: 'AUTH_REQUIRED',
						message: '사용자 인증이 필요합니다.',
						canRetry: false, // 인증 필요는 재시도 불가
						originalError: error
					};
			}
		}

		// 에러 메시지 기반 분석
		const errorMessage = error.message?.toLowerCase() || '';

		if (errorMessage.includes('expired')) {
			return {
				type: 'TOKEN_EXPIRED',
				message: '토큰이 만료되었습니다.',
				canRetry: true,
				retryAfterMs: 1000,
				originalError: error
			};
		}

		if (errorMessage.includes('invalid') || errorMessage.includes('malformed')) {
			return {
				type: 'TOKEN_INVALID',
				message: '유효하지 않은 토큰입니다.',
				canRetry: true,
				retryAfterMs: 2000,
				originalError: error
			};
		}

		if (errorMessage.includes('missing') || errorMessage.includes('required')) {
			return {
				type: 'TOKEN_MISSING',
				message: '인증 토큰이 없습니다.',
				canRetry: true,
				retryAfterMs: 500,
				originalError: error
			};
		}

		// 기본 토큰 에러
		return {
			type: 'TOKEN_INVALID',
			message: '토큰 관련 오류가 발생했습니다.',
			canRetry: true,
			retryAfterMs: 2000,
			originalError: error
		};
	}

	/**
	 * 기존 토큰 서비스를 활용한 토큰 에러 처리
	 */
	async function handleTokenError(error: any): Promise<ErrorRecoveryResult> {
		try {
			console.log('[PushServiceErrorHandler] 토큰 에러 처리 시작:', error);

			// 에러 분석
			const tokenError = analyzeTokenError(error);
			console.log('[PushServiceErrorHandler] 토큰 에러 분석 결과:', tokenError);

			// 복구 불가능한 에러 타입 확인
			if (!tokenError.canRetry) {
				console.warn('[PushServiceErrorHandler] 복구 불가능한 토큰 에러:', tokenError.type);
				return {
					success: false,
					shouldRetry: false,
					error: createPushServiceError(
						'TOKEN_RECOVERY_FAILED',
						`복구 불가능한 토큰 에러: ${tokenError.message}`,
						tokenError.originalError
					)
				};
			}

			// 복구 시도 횟수 확인
			if (state.recoveryAttempts >= state.maxRecoveryAttempts) {
				console.warn('[PushServiceErrorHandler] 최대 복구 시도 횟수 초과');
				return {
					success: false,
					shouldRetry: false,
					error: createPushServiceError(
						'TOKEN_RECOVERY_FAILED',
						'최대 토큰 복구 시도 횟수를 초과했습니다.',
						tokenError.originalError
					)
				};
			}

			// 백오프 시간 확인
			const now = Date.now();
			const timeSinceLastAttempt = now - state.lastRecoveryAttempt;
			const requiredBackoff = state.recoveryBackoffMs * Math.pow(2, state.recoveryAttempts);

			if (timeSinceLastAttempt < requiredBackoff) {
				const waitTime = requiredBackoff - timeSinceLastAttempt;
				console.log(`[PushServiceErrorHandler] 백오프 대기 중: ${waitTime}ms`);
				return {
					success: false,
					shouldRetry: true,
					retryAfterMs: waitTime
				};
			}

			// 복구 시도 시작
			state.isRecovering = true;
			state.lastRecoveryAttempt = now;
			state.recoveryAttempts++;

			console.log(
				`[PushServiceErrorHandler] 토큰 복구 시도 ${state.recoveryAttempts}/${state.maxRecoveryAttempts}`
			);

			// 토큰 에러 타입별 처리
			switch (tokenError.type) {
				case 'TOKEN_MISSING':
					return await handleMissingToken();

				case 'TOKEN_EXPIRED':
				case 'TOKEN_INVALID':
					return await handleExpiredOrInvalidToken();

				default:
					return await handleGenericTokenError(tokenError);
			}
		} catch (recoveryError) {
			console.error('[PushServiceErrorHandler] 토큰 에러 처리 중 오류:', recoveryError);
			return {
				success: false,
				shouldRetry: false,
				error: createPushServiceError(
					'TOKEN_RECOVERY_FAILED',
					'토큰 복구 처리 중 오류가 발생했습니다.',
					recoveryError as Error
				)
			};
		} finally {
			state.isRecovering = false;
		}
	}

	/**
	 * 토큰 누락 처리
	 */
	async function handleMissingToken(): Promise<ErrorRecoveryResult> {
		try {
			console.log('[PushServiceErrorHandler] 토큰 누락 처리 시작');

			// 기존 tokenService.isAuthenticated() 활용
			const isAuthenticated = await tokenService.isAuthenticated();
			if (!isAuthenticated) {
				console.warn('[PushServiceErrorHandler] 사용자가 인증되지 않음');
				return {
					success: false,
					shouldRetry: false,
					error: createPushServiceError(
						'AUTH_REQUIRED',
						'사용자 인증이 필요합니다. 다시 로그인해주세요.'
					)
				};
			}

			// 기존 tokenService.getAccessToken() 활용
			const accessToken = await tokenService.getAccessToken();
			if (accessToken) {
				console.log('[PushServiceErrorHandler] 유효한 액세스 토큰 발견');
				return {
					success: true,
					newToken: accessToken,
					shouldRetry: true,
					retryAfterMs: 500
				};
			}

			// 토큰이 없으면 리프레시 시도
			console.log('[PushServiceErrorHandler] 액세스 토큰 없음 - 리프레시 시도');
			return await attemptTokenRefresh();
		} catch (error) {
			console.error('[PushServiceErrorHandler] 토큰 누락 처리 실패:', error);
			return {
				success: false,
				shouldRetry: false,
				error: createPushServiceError(
					'TOKEN_RECOVERY_FAILED',
					'토큰 누락 처리에 실패했습니다.',
					error as Error
				)
			};
		}
	}

	/**
	 * 만료되거나 유효하지 않은 토큰 처리
	 */
	async function handleExpiredOrInvalidToken(): Promise<ErrorRecoveryResult> {
		try {
			console.log('[PushServiceErrorHandler] 만료/유효하지 않은 토큰 처리 시작');

			// 기존 tokenRefreshService 자동 갱신 활용
			return await attemptTokenRefresh();
		} catch (error) {
			console.error('[PushServiceErrorHandler] 만료/유효하지 않은 토큰 처리 실패:', error);
			return {
				success: false,
				shouldRetry: false,
				error: createPushServiceError(
					'TOKEN_RECOVERY_FAILED',
					'토큰 갱신 처리에 실패했습니다.',
					error as Error
				)
			};
		}
	}

	/**
	 * 일반적인 토큰 에러 처리
	 */
	async function handleGenericTokenError(tokenError: TokenErrorInfo): Promise<ErrorRecoveryResult> {
		try {
			console.log('[PushServiceErrorHandler] 일반 토큰 에러 처리:', tokenError.type);

			// 먼저 현재 토큰 상태 확인
			const isAuthenticated = await tokenService.isAuthenticated();
			if (!isAuthenticated) {
				return {
					success: false,
					shouldRetry: false,
					error: createPushServiceError('AUTH_REQUIRED', '사용자 인증이 필요합니다.')
				};
			}

			// 토큰 갱신 시도
			return await attemptTokenRefresh();
		} catch (error) {
			console.error('[PushServiceErrorHandler] 일반 토큰 에러 처리 실패:', error);
			return {
				success: false,
				shouldRetry: false,
				error: createPushServiceError(
					'TOKEN_RECOVERY_FAILED',
					'토큰 에러 처리에 실패했습니다.',
					error as Error
				)
			};
		}
	}

	/**
	 * 기존 tokenRefreshService를 활용한 토큰 갱신 시도
	 */
	async function attemptTokenRefresh(): Promise<ErrorRecoveryResult> {
		try {
			console.log('[PushServiceErrorHandler] 토큰 갱신 시도 시작');

			// 기존 tokenRefreshService.refreshAccessToken() 활용
			const newToken = await tokenRefreshService.refreshAccessToken();

			if (newToken) {
				console.log('[PushServiceErrorHandler] 토큰 갱신 성공');

				// 복구 성공 시 상태 초기화
				state.recoveryAttempts = 0;

				return {
					success: true,
					newToken,
					shouldRetry: true,
					retryAfterMs: 1000
				};
			} else {
				console.warn('[PushServiceErrorHandler] 토큰 갱신 실패 - null 반환');
				return {
					success: false,
					shouldRetry: false,
					error: createPushServiceError('REFRESH_FAILED', '토큰 갱신에 실패했습니다.')
				};
			}
		} catch (refreshError) {
			console.error('[PushServiceErrorHandler] 토큰 갱신 중 오류:', refreshError);

			// 기존 tokenRefreshService.handleRefreshFailure() 활용
			if (
				tokenRefreshService.handleRefreshFailure &&
				typeof refreshError === 'object' &&
				refreshError !== null
			) {
				try {
					await tokenRefreshService.handleRefreshFailure(refreshError as any);
				} catch (handleError) {
					console.error('[PushServiceErrorHandler] 리프레시 실패 처리 중 오류:', handleError);
				}
			}

			return {
				success: false,
				shouldRetry: false,
				error: createPushServiceError(
					'REFRESH_FAILED',
					'토큰 갱신에 실패했습니다.',
					refreshError as Error
				)
			};
		}
	}

	/**
	 * JWT 토큰 만료 시 기존 로직으로 처리
	 */
	async function handleTokenExpiration(): Promise<ErrorRecoveryResult> {
		try {
			console.log('[PushServiceErrorHandler] JWT 토큰 만료 처리 시작');

			// 기존 tokenService로 토큰 만료 확인
			const isExpiringSoon = await tokenService.isAccessTokenExpiringSoon(1); // 1분 여유

			if (isExpiringSoon) {
				console.log('[PushServiceErrorHandler] 토큰이 곧 만료됨 - 갱신 시도');

				// 기존 tokenRefreshService.ensureValidToken() 활용
				const validToken = await tokenRefreshService.ensureValidToken(5); // 5분 여유

				if (validToken) {
					console.log('[PushServiceErrorHandler] 유효한 토큰 확보 완료');
					return {
						success: true,
						newToken: validToken,
						shouldRetry: true,
						retryAfterMs: 500
					};
				}
			}

			// 토큰이 만료되지 않았거나 갱신 실패
			const currentToken = await tokenService.getAccessToken();
			if (currentToken) {
				return {
					success: true,
					newToken: currentToken,
					shouldRetry: true,
					retryAfterMs: 500
				};
			}

			// 토큰이 없으면 인증 필요
			return {
				success: false,
				shouldRetry: false,
				error: createPushServiceError('AUTH_REQUIRED', '토큰이 만료되어 재인증이 필요합니다.')
			};
		} catch (error) {
			console.error('[PushServiceErrorHandler] JWT 토큰 만료 처리 실패:', error);
			return {
				success: false,
				shouldRetry: false,
				error: createPushServiceError(
					'TOKEN_RECOVERY_FAILED',
					'토큰 만료 처리에 실패했습니다.',
					error as Error
				)
			};
		}
	}

	/**
	 * 에러 복구 상태 초기화
	 */
	function resetRecoveryState(): void {
		state.isRecovering = false;
		state.lastRecoveryAttempt = 0;
		state.recoveryAttempts = 0;
		console.log('[PushServiceErrorHandler] 에러 복구 상태 초기화 완료');
	}

	/**
	 * 현재 복구 상태 조회
	 */
	function getRecoveryState() {
		return {
			isRecovering: state.isRecovering,
			recoveryAttempts: state.recoveryAttempts,
			maxRecoveryAttempts: state.maxRecoveryAttempts,
			lastRecoveryAttempt: state.lastRecoveryAttempt,
			recoveryBackoffMs: state.recoveryBackoffMs
		};
	}

	/**
	 * 복구 설정 업데이트
	 */
	function updateRecoverySettings(settings: {
		maxRecoveryAttempts?: number;
		recoveryBackoffMs?: number;
	}): void {
		if (settings.maxRecoveryAttempts !== undefined) {
			state.maxRecoveryAttempts = settings.maxRecoveryAttempts;
		}
		if (settings.recoveryBackoffMs !== undefined) {
			state.recoveryBackoffMs = settings.recoveryBackoffMs;
		}
		console.log('[PushServiceErrorHandler] 복구 설정 업데이트:', settings);
	}

	return {
		// 에러 처리 메서드들
		isTokenError,
		analyzeTokenError,
		handleTokenError,
		handleTokenExpiration,

		// 상태 관리
		resetRecoveryState,
		getRecoveryState,
		updateRecoverySettings,

		// 개별 처리 메서드들 (테스트용)
		handleMissingToken,
		handleExpiredOrInvalidToken,
		attemptTokenRefresh
	};
}

/**
 * 전역 에러 핸들러 인스턴스
 */
let globalErrorHandler: ReturnType<typeof createPushServiceErrorHandler> | null = null;

/**
 * 전역 에러 핸들러 인스턴스 획득
 */
export function getPushServiceErrorHandler(): ReturnType<typeof createPushServiceErrorHandler> {
	if (!globalErrorHandler) {
		globalErrorHandler = createPushServiceErrorHandler();
	}
	return globalErrorHandler;
}

/**
 * 토큰 에러 처리 (편의 함수)
 */
export async function handlePushServiceTokenError(error: any): Promise<ErrorRecoveryResult> {
	const handler = getPushServiceErrorHandler();
	return await handler.handleTokenError(error);
}

/**
 * 토큰 만료 처리 (편의 함수)
 */
export async function handlePushServiceTokenExpiration(): Promise<ErrorRecoveryResult> {
	const handler = getPushServiceErrorHandler();
	return await handler.handleTokenExpiration();
}

/**
 * 토큰 에러 여부 확인 (편의 함수)
 */
export function isPushServiceTokenError(error: any): boolean {
	const handler = getPushServiceErrorHandler();
	return handler.isTokenError(error);
}
