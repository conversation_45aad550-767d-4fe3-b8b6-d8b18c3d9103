/**
 * 푸시 서비스 데이터 동기화 메커니즘
 *
 * 기존 IndexedDB 트랜잭션을 활용하여 오프라인 상태에서의 알림 저장과
 * 온라인 복구 시 데이터 정합성을 확인하는 시스템을 제공합니다.
 *
 * Requirements: 4.2.1, 4.2.3
 */

import {
	initEmployeeDB,
	saveData,
	getAllData,
	deleteData
} from '$lib/utils/indexeddb-utils';
import type { NotificationData } from '$lib/types/notification';
import type { PushServiceError } from '$lib/types/pushNotificationTypes';
import { createPushServiceError } from '$lib/types/pushNotificationTypes';

/**
 * 동기화 상태 타입
 */
export type SyncStatus =
	| 'idle'
	| 'syncing'
	| 'offline_pending'
	| 'conflict_detected'
	| 'sync_failed'
	| 'sync_completed';

/**
 * 동기화 작업 타입
 */
export type SyncOperationType = 'create' | 'update' | 'delete' | 'mark_read' | 'mark_unread';

/**
 * 오프라인 작업 정보
 */
export interface OfflineOperation {
	id: string;
	type: SyncOperationType;
	timestamp: number;
	data: any;
	retryCount: number;
	maxRetries: number;
	lastError?: string;
}

/**
 * 동기화 결과
 */
export interface SyncResult {
	success: boolean;
	processedOperations: number;
	failedOperations: number;
	conflicts: number;
	errors: PushServiceError[];
	syncedNotifications: NotificationData[];
}

/**
 * 데이터 정합성 검사 결과
 */
export interface DataIntegrityResult {
	isValid: boolean;
	totalNotifications: number;
	corruptedNotifications: number;
	duplicateNotifications: number;
	expiredNotifications: number;
	fixedIssues: number;
	errors: string[];
}

/**
 * 동기화 핸들러 상태
 */
interface DataSyncHandlerState {
	db: IDBDatabase | null;
	syncStatus: SyncStatus;
	offlineOperations: Map<string, OfflineOperation>;
	isOnline: boolean;
	lastSyncTime: number;
	syncInProgress: boolean;
	conflictResolutionStrategy: 'server_wins' | 'client_wins' | 'merge' | 'manual';
}

/**
 * 푸시 서비스 데이터 동기화 핸들러 생성
 */
export function createPushServiceDataSyncHandler() {
	const state: DataSyncHandlerState = {
		db: null,
		syncStatus: 'idle',
		offlineOperations: new Map(),
		isOnline: typeof navigator !== 'undefined' ? navigator.onLine : true,
		lastSyncTime: 0,
		syncInProgress: false,
		conflictResolutionStrategy: 'server_wins'
	};

	/**
	 * 동기화 핸들러 초기화
	 */
	async function initialize(): Promise<void> {
		try {
			console.log('[DataSyncHandler] 데이터 동기화 핸들러 초기화 시작');

			// 기존 EmployeeDB 초기화
			state.db = await initEmployeeDB();

			// 온라인 상태 감지 리스너 등록
			if (typeof window !== 'undefined') {
				window.addEventListener('online', handleOnlineStatusChange);
				window.addEventListener('offline', handleOnlineStatusChange);
			}

			// 저장된 오프라인 작업 복원
			await restoreOfflineOperations();

			// 초기 데이터 정합성 검사
			await performDataIntegrityCheck();

			state.syncStatus = 'idle';
			console.log('[DataSyncHandler] 데이터 동기화 핸들러 초기화 완료');
		} catch (error) {
			console.error('[DataSyncHandler] 초기화 실패:', error);
			throw createPushServiceError(
				'SYNC_INIT_FAILED',
				'데이터 동기화 핸들러 초기화에 실패했습니다.',
				error as Error
			);
		}
	}

	/**
	 * 온라인 상태 변경 처리
	 */
	function handleOnlineStatusChange(): void {
		const wasOnline = state.isOnline;
		state.isOnline = navigator.onLine;

		console.log('[DataSyncHandler] 온라인 상태 변경:', {
			wasOnline,
			isOnline: state.isOnline
		});

		if (!wasOnline && state.isOnline) {
			// 오프라인에서 온라인으로 전환
			console.log('[DataSyncHandler] 온라인 복구 감지 - 동기화 시작');
			handleOnlineRecovery();
		} else if (wasOnline && !state.isOnline) {
			// 온라인에서 오프라인으로 전환
			console.log('[DataSyncHandler] 오프라인 모드 전환');
			state.syncStatus = 'offline_pending';
		}
	}

	/**
	 * 온라인 복구 시 처리
	 */
	async function handleOnlineRecovery(): Promise<void> {
		try {
			console.log('[DataSyncHandler] 온라인 복구 처리 시작');

			// 데이터 정합성 확인
			const integrityResult = await performDataIntegrityCheck();
			if (!integrityResult.isValid) {
				console.warn('[DataSyncHandler] 데이터 정합성 문제 감지:', integrityResult);
			}

			// 오프라인 작업 동기화
			if (state.offlineOperations.size > 0) {
				console.log(
					`[DataSyncHandler] ${state.offlineOperations.size}개의 오프라인 작업 동기화 시작`
				);
				await syncOfflineOperations();
			}

			// 서버와 데이터 동기화 (실제 구현에서는 서버 API 호출)
			await performServerSync();

			state.lastSyncTime = Date.now();
			state.syncStatus = 'sync_completed';

			console.log('[DataSyncHandler] 온라인 복구 처리 완료');
		} catch (error) {
			console.error('[DataSyncHandler] 온라인 복구 처리 실패:', error);
			state.syncStatus = 'sync_failed';
		}
	}

	/**
	 * 오프라인 상태에서의 알림 저장
	 */
	async function saveNotificationOffline(notification: NotificationData): Promise<void> {
		try {
			if (!state.db) {
				throw new Error('데이터베이스가 초기화되지 않았습니다.');
			}

			console.log('[DataSyncHandler] 오프라인 알림 저장:', notification.id);

			// 기존 IndexedDB 트랜잭션 활용하여 알림 저장
			await saveData(state.db, 'push_notifications', notification);

			// 오프라인 작업으로 기록
			const operation: OfflineOperation = {
				id: `create_${notification.id}_${Date.now()}`,
				type: 'create',
				timestamp: Date.now(),
				data: notification,
				retryCount: 0,
				maxRetries: 3
			};

			state.offlineOperations.set(operation.id, operation);
			await persistOfflineOperations();

			console.log('[DataSyncHandler] 오프라인 알림 저장 완료:', {
				notificationId: notification.id,
				operationId: operation.id,
				totalOfflineOps: state.offlineOperations.size
			});
		} catch (error) {
			console.error('[DataSyncHandler] 오프라인 알림 저장 실패:', error);
			throw createPushServiceError(
				'OFFLINE_SAVE_FAILED',
				'오프라인 상태에서 알림 저장에 실패했습니다.',
				error as Error
			);
		}
	}

	/**
	 * 오프라인 작업 동기화
	 */
	async function syncOfflineOperations(): Promise<SyncResult> {
		if (state.syncInProgress) {
			throw createPushServiceError('SYNC_IN_PROGRESS', '이미 동기화가 진행 중입니다.');
		}

		state.syncInProgress = true;
		state.syncStatus = 'syncing';

		const result: SyncResult = {
			success: true,
			processedOperations: 0,
			failedOperations: 0,
			conflicts: 0,
			errors: [],
			syncedNotifications: []
		};

		try {
			console.log(`[DataSyncHandler] ${state.offlineOperations.size}개 오프라인 작업 동기화 시작`);

			// 오프라인 작업을 시간순으로 정렬
			const operations = Array.from(state.offlineOperations.values()).sort(
				(a, b) => a.timestamp - b.timestamp
			);

			for (const operation of operations) {
				try {
					console.log(`[DataSyncHandler] 오프라인 작업 처리: ${operation.type} - ${operation.id}`);

					await processOfflineOperation(operation);

					// 성공한 작업 제거
					state.offlineOperations.delete(operation.id);
					result.processedOperations++;

					// 알림 데이터인 경우 결과에 추가
					if (operation.type === 'create' && operation.data) {
						result.syncedNotifications.push(operation.data);
					}
				} catch (error) {
					console.error(`[DataSyncHandler] 오프라인 작업 처리 실패: ${operation.id}`, error);

					operation.retryCount++;
					operation.lastError = error instanceof Error ? error.message : String(error);

					if (operation.retryCount >= operation.maxRetries) {
						// 최대 재시도 횟수 초과 시 제거
						state.offlineOperations.delete(operation.id);
						result.failedOperations++;
						result.errors.push(
							createPushServiceError(
								'OFFLINE_OPERATION_FAILED',
								`오프라인 작업 처리 실패: ${operation.id}`,
								error as Error
							)
						);
					}
				}
			}

			// 남은 오프라인 작업 저장
			await persistOfflineOperations();

			result.success = result.failedOperations === 0;
			state.syncStatus = result.success ? 'sync_completed' : 'sync_failed';

			console.log('[DataSyncHandler] 오프라인 작업 동기화 완료:', result);
			return result;
		} catch (error) {
			console.error('[DataSyncHandler] 오프라인 작업 동기화 실패:', error);
			result.success = false;
			result.errors.push(
				createPushServiceError(
					'SYNC_FAILED',
					'오프라인 작업 동기화에 실패했습니다.',
					error as Error
				)
			);
			state.syncStatus = 'sync_failed';
			return result;
		} finally {
			state.syncInProgress = false;
		}
	}

	/**
	 * 개별 오프라인 작업 처리
	 */
	async function processOfflineOperation(operation: OfflineOperation): Promise<void> {
		if (!state.db) {
			throw new Error('데이터베이스가 초기화되지 않았습니다.');
		}

		switch (operation.type) {
			case 'create':
				// 알림 생성 작업 - 이미 로컬에 저장되어 있으므로 서버 동기화만 필요
				console.log(`[DataSyncHandler] 생성 작업 처리: ${operation.data.id}`);
				// 실제 구현에서는 서버 API 호출
				break;

			case 'update':
				// 알림 업데이트 작업
				console.log(`[DataSyncHandler] 업데이트 작업 처리: ${operation.data.id}`);
				await saveData(state.db, 'push_notifications', operation.data);
				break;

			case 'delete':
				// 알림 삭제 작업
				console.log(`[DataSyncHandler] 삭제 작업 처리: ${operation.data.id}`);
				await deleteData(state.db, 'push_notifications', operation.data.id);
				break;

			case 'mark_read':
			{
				// 읽음 처리 작업
				console.log(`[DataSyncHandler] 읽음 처리 작업: ${operation.data.id}`);
				const readNotification = await getData<NotificationData>(state.db, 'push_notifications', operation.data.id);
				if (readNotification) {
					readNotification.read = true;
					await saveData(state.db, 'push_notifications', readNotification);
				}
				break;
			}

			case 'mark_unread':
			{
				// 읽지 않음 처리 작업
				console.log(`[DataSyncHandler] 읽지 않음 처리 작업: ${operation.data.id}`);
				const unreadNotification = await getData<NotificationData>(state.db, 'push_notifications', operation.data.id);
				if (unreadNotification) {
					unreadNotification.read = false;
					await saveData(state.db, 'push_notifications', unreadNotification);
				}
				break;
			}

			default:
				throw new Error(`지원되지 않는 작업 타입: ${operation.type}`);
		}
	}

	/**
	 * 서버와 데이터 동기화
	 */
	async function performServerSync(): Promise<void> {
		try {
			console.log('[DataSyncHandler] 서버 동기화 시작');

			if (!state.db) {
				throw new Error('데이터베이스가 초기화되지 않았습니다.');
			}

			// 로컬 알림 데이터 조회
			const localNotifications = await getAllData<NotificationData>(state.db, 'push_notifications');
			console.log(`[DataSyncHandler] 로컬 알림 ${localNotifications.length}개 확인`);

			// 실제 구현에서는 서버 API를 호출하여 서버 데이터와 비교
			// 현재는 로컬 데이터 정합성만 확인
			const integrityResult = await performDataIntegrityCheck();

			if (!integrityResult.isValid) {
				console.warn('[DataSyncHandler] 서버 동기화 중 데이터 정합성 문제 감지');
			}

			console.log('[DataSyncHandler] 서버 동기화 완료');
		} catch (error) {
			console.error('[DataSyncHandler] 서버 동기화 실패:', error);
			throw error;
		}
	}

	/**
	 * 데이터 정합성 확인
	 */
	async function performDataIntegrityCheck(): Promise<DataIntegrityResult> {
		const result: DataIntegrityResult = {
			isValid: true,
			totalNotifications: 0,
			corruptedNotifications: 0,
			duplicateNotifications: 0,
			expiredNotifications: 0,
			fixedIssues: 0,
			errors: []
		};

		try {
			if (!state.db) {
				throw new Error('데이터베이스가 초기화되지 않았습니다.');
			}

			console.log('[DataSyncHandler] 데이터 정합성 검사 시작');

			// 모든 알림 데이터 조회
			const notifications = await getAllData<NotificationData>(state.db, 'push_notifications');
			result.totalNotifications = notifications.length;

			const seenIds = new Set<string>();
			const now = new Date();
			const corruptedNotifications: NotificationData[] = [];
			const expiredNotifications: NotificationData[] = [];

			for (const notification of notifications) {
				try {
					// 필수 필드 검사
					if (!notification.id || !notification.content || !notification.created_at) {
						console.warn('[DataSyncHandler] 손상된 알림 데이터:', notification);
						result.corruptedNotifications++;
						corruptedNotifications.push(notification);
						continue;
					}

					// 중복 ID 검사
					if (seenIds.has(notification.id)) {
						console.warn('[DataSyncHandler] 중복 알림 ID:', notification.id);
						result.duplicateNotifications++;
						// 중복된 알림 삭제
						await deleteData(state.db, 'push_notifications', notification.id);
						result.fixedIssues++;
						continue;
					}
					seenIds.add(notification.id);

					// 만료된 알림 검사
					if (notification.expire_day) {
						const expireDate = new Date(notification.expire_day);
						if (expireDate < now) {
							console.log('[DataSyncHandler] 만료된 알림:', notification.id);
							result.expiredNotifications++;
							expiredNotifications.push(notification);
						}
					}

					// 데이터 타입 검사
					if (typeof notification.read !== 'boolean') {
						console.warn('[DataSyncHandler] 잘못된 read 필드:', notification.id);
						notification.read = false;
						await saveData(state.db, 'push_notifications', notification);
						result.fixedIssues++;
					}
				} catch (error) {
					console.error(`[DataSyncHandler] 알림 검사 중 오류 (${notification.id}):`, error);
					result.errors.push(`알림 ${notification.id} 검사 실패: ${error}`);
				}
			}

			// 손상된 알림 정리
			for (const corrupted of corruptedNotifications) {
				try {
					if (corrupted.id) {
						await deleteData(state.db, 'push_notifications', corrupted.id);
						result.fixedIssues++;
					}
				} catch (error) {
					console.error('[DataSyncHandler] 손상된 알림 삭제 실패:', error);
					result.errors.push(`손상된 알림 삭제 실패: ${error}`);
				}
			}

			// 만료된 알림 정리 (설정에 따라)
			const shouldCleanExpired = true; // 설정값
			if (shouldCleanExpired) {
				for (const expired of expiredNotifications) {
					try {
						await deleteData(state.db, 'push_notifications', expired.id);
						result.fixedIssues++;
					} catch (error) {
						console.error('[DataSyncHandler] 만료된 알림 삭제 실패:', error);
						result.errors.push(`만료된 알림 삭제 실패: ${error}`);
					}
				}
			}

			// 정합성 판단
			result.isValid = result.corruptedNotifications === 0 && result.errors.length === 0;

			console.log('[DataSyncHandler] 데이터 정합성 검사 완료:', result);
			return result;
		} catch (error) {
			console.error('[DataSyncHandler] 데이터 정합성 검사 실패:', error);
			result.isValid = false;
			result.errors.push(`정합성 검사 실패: ${error}`);
			return result;
		}
	}

	/**
	 * 오프라인 작업 저장
	 */
	async function persistOfflineOperations(): Promise<void> {
		try {
			const operations = Array.from(state.offlineOperations.values());
			const serialized = JSON.stringify(operations);
			localStorage.setItem('cnsprowms_offline_operations', serialized);
			console.log(`[DataSyncHandler] ${operations.length}개 오프라인 작업 저장 완료`);
		} catch (error) {
			console.error('[DataSyncHandler] 오프라인 작업 저장 실패:', error);
		}
	}

	/**
	 * 오프라인 작업 복원
	 */
	async function restoreOfflineOperations(): Promise<void> {
		try {
			const serialized = localStorage.getItem('cnsprowms_offline_operations');
			if (serialized) {
				const operations: OfflineOperation[] = JSON.parse(serialized);
				state.offlineOperations.clear();

				operations.forEach((op) => {
					state.offlineOperations.set(op.id, op);
				});

				console.log(`[DataSyncHandler] ${operations.length}개 오프라인 작업 복원 완료`);
			}
		} catch (error) {
			console.error('[DataSyncHandler] 오프라인 작업 복원 실패:', error);
			// 복원 실패 시 초기화
			state.offlineOperations.clear();
			localStorage.removeItem('cnsprowms_offline_operations');
		}
	}

	/**
	 * 데이터베이스에서 데이터 조회 (내부 헬퍼)
	 */
	async function getData<T>(db: IDBDatabase, storeName: string, key: string): Promise<T | null> {
		return new Promise((resolve, reject) => {
			const transaction = db.transaction([storeName], 'readonly');
			const store = transaction.objectStore(storeName);
			const request = store.get(key);

			request.onsuccess = () => resolve(request.result || null);
			request.onerror = () => reject(request.error);
		});
	}

	/**
	 * 알림 읽음 처리 (오프라인 지원)
	 */
	async function markNotificationAsRead(notificationId: string): Promise<void> {
		try {
			if (!state.db) {
				throw new Error('데이터베이스가 초기화되지 않았습니다.');
			}

			// 로컬 데이터 업데이트
			const notification = await getData<NotificationData>(
				state.db,
				'push_notifications',
				notificationId
			);
			if (notification) {
				notification.read = true;
				await saveData(state.db, 'push_notifications', notification);
			}

			// 오프라인 작업으로 기록 (온라인 상태에서도 동기화를 위해)
			const operation: OfflineOperation = {
				id: `mark_read_${notificationId}_${Date.now()}`,
				type: 'mark_read',
				timestamp: Date.now(),
				data: { id: notificationId },
				retryCount: 0,
				maxRetries: 3
			};

			state.offlineOperations.set(operation.id, operation);
			await persistOfflineOperations();

			console.log('[DataSyncHandler] 알림 읽음 처리 완료:', notificationId);
		} catch (error) {
			console.error('[DataSyncHandler] 알림 읽음 처리 실패:', error);
			throw createPushServiceError(
				'MARK_READ_FAILED',
				'알림 읽음 처리에 실패했습니다.',
				error as Error
			);
		}
	}

	/**
	 * 동기화 상태 조회
	 */
	function getSyncStatus() {
		return {
			syncStatus: state.syncStatus,
			isOnline: state.isOnline,
			lastSyncTime: state.lastSyncTime,
			offlineOperationsCount: state.offlineOperations.size,
			syncInProgress: state.syncInProgress,
			conflictResolutionStrategy: state.conflictResolutionStrategy
		};
	}

	/**
	 * 수동 동기화 트리거
	 */
	async function triggerManualSync(): Promise<SyncResult> {
		console.log('[DataSyncHandler] 수동 동기화 시작');

		if (!state.isOnline) {
			throw createPushServiceError('OFFLINE_MODE', '오프라인 상태에서는 동기화할 수 없습니다.');
		}

		return await syncOfflineOperations();
	}

	/**
	 * 정리
	 */
	async function cleanup(): Promise<void> {
		try {
			console.log('[DataSyncHandler] 데이터 동기화 핸들러 정리 시작');

			// 이벤트 리스너 제거
			if (typeof window !== 'undefined') {
				window.removeEventListener('online', handleOnlineStatusChange);
				window.removeEventListener('offline', handleOnlineStatusChange);
			}

			// 진행 중인 동기화 대기
			if (state.syncInProgress) {
				console.log('[DataSyncHandler] 진행 중인 동기화 완료 대기...');
				// 실제 구현에서는 Promise를 사용하여 동기화 완료 대기
			}

			// 오프라인 작업 저장
			await persistOfflineOperations();

			// 상태 초기화
			state.db = null;
			state.syncStatus = 'idle';
			state.offlineOperations.clear();
			state.syncInProgress = false;

			console.log('[DataSyncHandler] 데이터 동기화 핸들러 정리 완료');
		} catch (error) {
			console.error('[DataSyncHandler] 정리 중 오류:', error);
		}
	}

	return {
		// 초기화 및 정리
		initialize,
		cleanup,

		// 오프라인 데이터 처리
		saveNotificationOffline,
		markNotificationAsRead,

		// 동기화 처리
		syncOfflineOperations,
		triggerManualSync,

		// 데이터 정합성
		performDataIntegrityCheck,

		// 상태 조회
		getSyncStatus,

		// 내부 메서드들 (테스트용)
		handleOnlineRecovery,
		performServerSync,
		processOfflineOperation
	};
}

/**
 * 전역 데이터 동기화 핸들러 인스턴스
 */
let globalDataSyncHandler: ReturnType<typeof createPushServiceDataSyncHandler> | null = null;

/**
 * 전역 데이터 동기화 핸들러 인스턴스 획득
 */
export function getPushServiceDataSyncHandler(): ReturnType<
	typeof createPushServiceDataSyncHandler
> {
	if (!globalDataSyncHandler) {
		globalDataSyncHandler = createPushServiceDataSyncHandler();
	}
	return globalDataSyncHandler;
}

/**
 * 데이터 동기화 핸들러 초기화 (편의 함수)
 */
export async function initializePushServiceDataSync(): Promise<void> {
	const handler = getPushServiceDataSyncHandler();
	await handler.initialize();
}

/**
 * 오프라인 알림 저장 (편의 함수)
 */
export async function savePushNotificationOffline(notification: NotificationData): Promise<void> {
	const handler = getPushServiceDataSyncHandler();
	await handler.saveNotificationOffline(notification);
}

/**
 * 수동 동기화 트리거 (편의 함수)
 */
export async function triggerPushServiceSync(): Promise<SyncResult> {
	const handler = getPushServiceDataSyncHandler();
	return await handler.triggerManualSync();
}

/**
 * 데이터 정합성 검사 (편의 함수)
 */
export async function checkPushServiceDataIntegrity(): Promise<DataIntegrityResult> {
	const handler = getPushServiceDataSyncHandler();
	return await handler.performDataIntegrityCheck();
}
