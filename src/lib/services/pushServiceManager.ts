/**
 * 통합 푸시 서비스 매니저
 *
 * 기존 인프라를 활용하여 플랫폼별 푸시 서비스를 자동으로 선택하고 관리합니다.
 * - 기존 platformService.getCurrentPlatform() 활용
 * - 플랫폼별 푸시 서비스 자동 선택 로직
 * - 통합 인터페이스 제공
 *
 * Requirements: 1.1, 1.2, 4.1
 */

import { getCurrentPlatform, isDesktop, isAndroid } from '$lib/services/platformService';
import { tokenService } from '$lib/services/tokenService';
import { createDesktopPushService } from './desktopPushService';
import { createAndroidPushService } from './androidPushService';
import { getPushServiceErrorHandler } from './pushServiceErrorHandler';
import { getPushServicePlatformErrorHandler } from './pushServicePlatformErrorHandler';
import { getPushServiceDataSyncHandler } from './pushServiceDataSyncHandler';
import { performanceManager } from '$lib/utils/notification-performance';
import { memoryManager as notificationMemoryManager } from '$lib/utils/notification-memory-optimizer';
import type { NotificationData, CreateNotificationData } from '$lib/types/notification';
import type {
	PermissionStatus,
	NotificationCallback,
	PushServiceError,
	BasicPushNotificationService
} from '$lib/types/pushNotificationTypes';
import { createPushServiceError } from '$lib/types/pushNotificationTypes';

/**
 * 푸시 서비스 매니저 상태
 */
interface PushServiceManagerState {
	currentService: BasicPushNotificationService | null;
	platform: string;
	isInitialized: boolean;
	callbacks: Set<NotificationCallback>;
	registrationStatus: 'not_started' | 'in_progress' | 'completed' | 'failed';
	lastError: PushServiceError | null;
}

/**
 * 서비스 상태 정보
 */
export interface ServiceStatus {
	platform: string;
	isInitialized: boolean;
	hasService: boolean;
	registrationStatus: 'not_started' | 'in_progress' | 'completed' | 'failed';
	permissionStatus?: PermissionStatus;
	lastError?: PushServiceError;
	deviceInfo?: {
		deviceId?: string;
		userId?: string;
		isAuthenticated?: boolean;
	};
}

/**
 * 통합 푸시 서비스 매니저 생성
 */
export function createPushServiceManager() {
	const state: PushServiceManagerState = {
		currentService: null,
		platform: 'unknown',
		isInitialized: false,
		callbacks: new Set(),
		registrationStatus: 'not_started',
		lastError: null
	};

	/**
	 * 플랫폼별 푸시 서비스 생성
	 */
	function createPlatformService(): BasicPushNotificationService {
		const platform = getCurrentPlatform();
		state.platform = platform;

		console.log('[PushServiceManager] 플랫폼별 서비스 생성:', platform);

		switch (platform) {
			case 'desktop':
				if (isDesktop()) {
					console.log('[PushServiceManager] 데스크탑 푸시 서비스 생성');
					return createDesktopPushService();
				}
				break;
			case 'android':
				if (isAndroid()) {
					console.log('[PushServiceManager] 안드로이드 푸시 서비스 생성');
					return createAndroidPushService();
				}
				break;
			default:
				throw createPushServiceError(
					'PLATFORM_NOT_SUPPORTED',
					`지원되지 않는 플랫폼입니다: ${platform}`
				);
		}

		throw createPushServiceError('PLATFORM_NOT_SUPPORTED', `플랫폼 감지 오류: ${platform}`);
	}

	/**
	 * 서비스 초기화 (기존 platformService 활용)
	 */
	async function initialize(): Promise<void> {
		try {
			console.log('[PushServiceManager] 통합 푸시 서비스 매니저 초기화 시작');

			// 성능 최적화 매니저들 초기화
			console.log('[PushServiceManager] 성능 최적화 초기화 시작');
			await Promise.all([performanceManager.initialize(), notificationMemoryManager.initialize()]);
			console.log('[PushServiceManager] 성능 최적화 초기화 완료');

			// 에러 핸들러들 초기화
			const errorHandler = getPushServiceErrorHandler();
			const platformErrorHandler = getPushServicePlatformErrorHandler();
			const dataSyncHandler = getPushServiceDataSyncHandler();

			// 데이터 동기화 핸들러 초기화
			await dataSyncHandler.initialize();

			// 기존 platformService로 플랫폼 감지
			const platform = getCurrentPlatform();
			console.log('[PushServiceManager] 감지된 플랫폼:', platform);

			// 플랫폼별 서비스 생성
			state.currentService = createPlatformService();

			// 플랫폼별 서비스 초기화 (에러 처리 포함)
			await initializeServiceWithErrorHandling();

			// 알림 수신 콜백 연결 (성능 최적화 포함)
			state.currentService.onNotificationReceived(handleNotificationReceivedWithOptimization);

			// 정기적인 성능 최적화 작업 스케줄링
			setupPerformanceOptimizationSchedule();

			state.isInitialized = true;
			state.lastError = null;

			console.log('[PushServiceManager] 통합 푸시 서비스 매니저 초기화 완료:', {
				platform: state.platform,
				hasService: !!state.currentService,
				isInitialized: state.isInitialized
			});
		} catch (error) {
			console.error('[PushServiceManager] 초기화 실패:', error);

			const pushError = await handleInitializationError(error);
			state.lastError = pushError;
			throw pushError;
		}
	}

	/**
	 * 에러 처리를 포함한 서비스 초기화
	 */
	async function initializeServiceWithErrorHandling(): Promise<void> {
		const maxRetries = 3;
		const platformErrorHandler = getPushServicePlatformErrorHandler();

		for (let attempt = 1; attempt <= maxRetries; attempt++) {
			try {
				await state.currentService!.initialize();
				console.log(`[PushServiceManager] 서비스 초기화 성공 (시도 ${attempt})`);
				return;
			} catch (error) {
				console.warn(
					`[PushServiceManager] 서비스 초기화 실패 (시도 ${attempt}/${maxRetries}):`,
					error
				);

				// 플랫폼별 에러 처리
				if (platformErrorHandler.isPlatformError(error)) {
					try {
						await platformErrorHandler.handlePlatformError(error);
						console.log(`[PushServiceManager] 플랫폼 에러 복구 완료 (시도 ${attempt})`);
						continue; // 다시 시도
					} catch (recoveryError) {
						console.error(
							`[PushServiceManager] 플랫폼 에러 복구 실패 (시도 ${attempt}):`,
							recoveryError
						);
					}
				}

				// 마지막 시도인 경우 에러 throw
				if (attempt === maxRetries) {
					throw error;
				}

				// 재시도 전 대기
				await new Promise((resolve) => setTimeout(resolve, 1000 * attempt));
			}
		}
	}

	/**
	 * 초기화 에러 처리
	 */
	async function handleInitializationError(error: any): Promise<PushServiceError> {
		const errorHandler = getPushServiceErrorHandler();
		const platformErrorHandler = getPushServicePlatformErrorHandler();

		// 토큰 에러 확인
		if (errorHandler.isTokenError(error)) {
			console.log('[PushServiceManager] 초기화 중 토큰 에러 감지');
			try {
				const recoveryResult = await errorHandler.handleTokenError(error);
				if (recoveryResult.success) {
					console.log('[PushServiceManager] 토큰 에러 복구 성공 - 재초기화 시도');
					// 복구 성공 시 재초기화 시도는 상위에서 처리
				}
			} catch (tokenError) {
				console.error('[PushServiceManager] 토큰 에러 복구 실패:', tokenError);
			}
		}

		// 플랫폼별 에러 확인
		if (platformErrorHandler.isPlatformError(error)) {
			console.log('[PushServiceManager] 초기화 중 플랫폼 에러 감지');
			try {
				await platformErrorHandler.handlePlatformError(error);
				console.log('[PushServiceManager] 플랫폼 에러 복구 완료');
			} catch (platformError) {
				console.error('[PushServiceManager] 플랫폼 에러 복구 실패:', platformError);
			}
		}

		// 최종 에러 생성
		return error instanceof Error
			? createPushServiceError('SERVICE_INIT_FAILED', '푸시 서비스 초기화에 실패했습니다.', error)
			: createPushServiceError('SERVICE_INIT_FAILED', '푸시 서비스 초기화에 실패했습니다.');
	}

	/**
	 * 통합 디바이스 등록 (기존 JWT 토큰 사용)
	 * 실패 시 재시도 및 폴백 처리 포함
	 */
	async function registerDevice(): Promise<void> {
		const errorHandler = getPushServiceErrorHandler();
		const platformErrorHandler = getPushServicePlatformErrorHandler();

		try {
			if (!state.currentService) {
				throw createPushServiceError('SERVICE_INIT_FAILED', '서비스가 초기화되지 않았습니다.');
			}

			console.log('[PushServiceManager] 디바이스 등록 시작');
			state.registrationStatus = 'in_progress';

			// 토큰 상태 확인 및 갱신
			await ensureValidTokenForRegistration();

			// 플랫폼별 에러 처리를 포함한 등록 시도
			await platformErrorHandler.retryWithBackoff(async () => {
				return await performDeviceRegistration();
			}, 'device_registration');

			state.registrationStatus = 'completed';
			state.lastError = null;

			console.log('[PushServiceManager] 디바이스 등록 완료');
		} catch (error) {
			console.error('[PushServiceManager] 디바이스 등록 실패:', error);
			state.registrationStatus = 'failed';

			// 에러 타입별 처리
			const handledError = await handleRegistrationError(error);
			state.lastError = handledError;

			// 폴백 처리 시도
			try {
				await handleRegistrationFallback();
				console.log('[PushServiceManager] 폴백 처리 완료 - 제한된 기능으로 동작');
			} catch (fallbackError) {
				console.error('[PushServiceManager] 폴백 처리도 실패:', fallbackError);
				throw handledError;
			}
		}
	}

	/**
	 * 등록을 위한 유효한 토큰 확보
	 */
	async function ensureValidTokenForRegistration(): Promise<void> {
		const errorHandler = getPushServiceErrorHandler();

		try {
			// 기존 tokenService로 인증 상태 확인
			const isAuthenticated = await tokenService.isAuthenticated();
			if (!isAuthenticated) {
				throw createPushServiceError('AUTH_REQUIRED', '사용자 인증이 필요합니다.');
			}

			// 토큰 만료 임박 시 갱신
			const isExpiringSoon = await tokenService.isAccessTokenExpiringSoon(5); // 5분 여유
			if (isExpiringSoon) {
				console.log('[PushServiceManager] 토큰 만료 임박 - 갱신 시도');
				const recoveryResult = await errorHandler.handleTokenExpiration();
				if (!recoveryResult.success) {
					throw createPushServiceError('TOKEN_REFRESH_FAILED', '토큰 갱신에 실패했습니다.');
				}
			}

			console.log('[PushServiceManager] 유효한 토큰 확보 완료');
		} catch (error) {
			console.error('[PushServiceManager] 토큰 확보 실패:', error);
			throw error;
		}
	}

	/**
	 * 실제 디바이스 등록 수행
	 */
	async function performDeviceRegistration(): Promise<void> {
		const registrationStartTime = Date.now();

		// 플랫폼별 디바이스 등록 처리
		if (state.platform === 'desktop') {
			const desktopService = state.currentService as any;
			if (desktopService.registerDevice) {
				const deviceId = await desktopService.registerDevice();
				console.log('[PushServiceManager] 데스크탑 디바이스 등록 완료:', deviceId);
			}
		} else if (state.platform === 'android') {
			const androidService = state.currentService as any;
			if (androidService.registerWithBeams) {
				await androidService.registerWithBeams();
				console.log('[PushServiceManager] 안드로이드 Beams 등록 완료');
			}
		}

		const registrationTime = Date.now() - registrationStartTime;
		console.log(`[PushServiceManager] 등록 완료 (${registrationTime}ms)`);
	}

	/**
	 * 등록 에러 처리
	 */
	async function handleRegistrationError(error: any): Promise<PushServiceError> {
		const errorHandler = getPushServiceErrorHandler();
		const platformErrorHandler = getPushServicePlatformErrorHandler();

		// 토큰 에러 처리
		if (errorHandler.isTokenError(error)) {
			console.log('[PushServiceManager] 등록 중 토큰 에러 감지');
			try {
				const recoveryResult = await errorHandler.handleTokenError(error);
				if (recoveryResult.success && recoveryResult.shouldRetry) {
					console.log('[PushServiceManager] 토큰 에러 복구 성공 - 재등록 시도');
					// 재등록은 상위에서 처리
				}
			} catch (tokenError) {
				console.error('[PushServiceManager] 토큰 에러 복구 실패:', tokenError);
			}
		}

		// 플랫폼별 에러 처리
		if (platformErrorHandler.isPlatformError(error)) {
			console.log('[PushServiceManager] 등록 중 플랫폼 에러 감지');
			try {
				await platformErrorHandler.handlePlatformError(error);
				console.log('[PushServiceManager] 플랫폼 에러 복구 완료');
			} catch (platformError) {
				console.error('[PushServiceManager] 플랫폼 에러 복구 실패:', platformError);
			}
		}

		// 최종 에러 반환
		return error instanceof Error
			? createPushServiceError('DEVICE_REGISTRATION_FAILED', '디바이스 등록에 실패했습니다.', error)
			: createPushServiceError('DEVICE_REGISTRATION_FAILED', '디바이스 등록에 실패했습니다.');
	}

	/**
	 * 등록 실패 시 폴백 처리
	 */
	async function handleRegistrationFallback(): Promise<void> {
		try {
			// 로컬 저장소에 폴백 상태 저장
			const fallbackInfo = {
				platform: state.platform,
				fallbackMode: true,
				timestamp: new Date().toISOString(),
				userId: await tokenService.getCurrentUserId()
			};

			localStorage.setItem('cnsprowms_push_fallback', JSON.stringify(fallbackInfo));

			console.log('[PushServiceManager] 폴백 정보 저장 완료:', fallbackInfo);
		} catch (error) {
			console.error('[PushServiceManager] 폴백 처리 실패:', error);
			throw error;
		}
	}

	/**
	 * 사용자 로그인 시 처리 (기존 tokenService 연동)
	 */
	async function onUserLogin(): Promise<void> {
		try {
			console.log('[PushServiceManager] 사용자 로그인 처리 시작');

			// 기존 tokenService로 인증 상태 및 사용자 ID 확인
			const isAuthenticated = await tokenService.isAuthenticated();
			const userId = await tokenService.getCurrentUserId();

			if (!isAuthenticated || !userId) {
				console.warn('[PushServiceManager] 사용자가 인증되지 않았거나 사용자 ID가 없습니다.');
				return;
			}

			console.log('[PushServiceManager] 인증된 사용자 로그인:', { userId, isAuthenticated });

			// 세션 상태와 푸시 서비스 동기화
			await synchronizeSessionState(userId, true);

			// 디바이스가 등록되지 않았다면 먼저 등록
			if (state.registrationStatus !== 'completed') {
				console.log('[PushServiceManager] 디바이스 미등록 상태 - 등록 시도');
				await registerDevice();
			}

			// 플랫폼별 사용자 ID 연결 처리 (디바이스 등록 후)
			if (state.currentService && state.platform === 'desktop') {
				const desktopService = state.currentService as any;
				if (desktopService.setUserId) {
					console.log('[PushServiceManager] 데스크탑 사용자 ID 연결 시작');
					await desktopService.setUserId();
					console.log('[PushServiceManager] 데스크탑 사용자 ID 연결 완료');
				}
			}

			console.log('[PushServiceManager] 사용자 로그인 처리 완료');
		} catch (error) {
			console.error('[PushServiceManager] 사용자 로그인 처리 실패:', error);

			const pushError =
				error instanceof Error
					? createPushServiceError(
							'USER_ID_CONNECTION_FAILED',
							'사용자 로그인 처리에 실패했습니다.',
							error
						)
					: createPushServiceError(
							'USER_ID_CONNECTION_FAILED',
							'사용자 로그인 처리에 실패했습니다.'
						);

			state.lastError = pushError;
			// 로그인 처리 실패는 throw하지 않음 (앱 사용에 지장 없도록)
		}
	}

	/**
	 * 사용자 로그아웃 시 처리 (기존 tokenService 연동)
	 */
	async function onUserLogout(): Promise<void> {
		try {
			console.log('[PushServiceManager] 사용자 로그아웃 처리 시작');

			// 세션 상태와 푸시 서비스 동기화
			await synchronizeSessionState(null, false);

			// 플랫폼별 사용자 ID 연결 해제 처리
			if (state.currentService && state.platform === 'desktop') {
				const desktopService = state.currentService as any;
				if (desktopService.clearUserId) {
					await desktopService.clearUserId();
					console.log('[PushServiceManager] 데스크탑 사용자 ID 연결 해제 완료');
				}
			}

			// 등록 상태 초기화 (재로그인 시 다시 등록하도록)
			state.registrationStatus = 'not_started';

			console.log('[PushServiceManager] 사용자 로그아웃 처리 완료');
		} catch (error) {
			console.error('[PushServiceManager] 사용자 로그아웃 처리 실패:', error);

			const pushError =
				error instanceof Error
					? createPushServiceError(
							'USER_ID_CLEAR_FAILED',
							'사용자 로그아웃 처리에 실패했습니다.',
							error
						)
					: createPushServiceError('USER_ID_CLEAR_FAILED', '사용자 로그아웃 처리에 실패했습니다.');

			state.lastError = pushError;
			// 로그아웃 처리 실패는 throw하지 않음
		}
	}

	/**
	 * 알림 수신 통합 처리 (성능 최적화 포함)
	 */
	function handleNotificationReceivedWithOptimization(notification: NotificationData): void {
		console.log('[PushServiceManager] 알림 수신 통합 처리 (최적화):', {
			id: notification.id,
			priority: notification.priority,
			platform: state.platform
		});

		// 성능 측정 시작
		const startTime = performance.now();

		try {
			// 메모리 효율적인 알림 데이터 압축
			const compressedNotification =
				notificationMemoryManager.optimizer.compressNotificationData(notification);

			// 캐시 무효화 (새 알림 수신 시)
			performanceManager.cache.invalidateCache('active');
			performanceManager.cache.invalidateCache('unread_count');

			// 등록된 모든 콜백에게 알림 전달 (원본 데이터 사용)
			state.callbacks.forEach((callback) => {
				try {
					callback(notification);
				} catch (error) {
					console.error('[PushServiceManager] 콜백 실행 중 오류:', error);
				}
			});

			// 성능 측정 완료
			const processingTime = performance.now() - startTime;
			console.log(`[PushServiceManager] 알림 처리 완료 (${processingTime.toFixed(2)}ms)`);

			// 메모리 사용량이 높으면 정리
			const memoryUsage = notificationMemoryManager.optimizer.getMemoryUsage();
			if (memoryUsage.estimatedMemoryUsage > 30 * 1024 * 1024) {
				// 30MB 초과 시
				console.log('[PushServiceManager] 메모리 사용량 높음 - 정리 실행');
				notificationMemoryManager.optimizer.cleanupMemory();
			}
		} catch (error) {
			console.error('[PushServiceManager] 최적화된 알림 처리 중 오류:', error);

			// 폴백: 기본 처리 방식 사용
			handleNotificationReceived(notification);
		}
	}

	/**
	 * 기본 알림 수신 처리 (폴백용)
	 */
	function handleNotificationReceived(notification: NotificationData): void {
		console.log('[PushServiceManager] 알림 수신 기본 처리:', {
			id: notification.id,
			priority: notification.priority,
			platform: state.platform
		});

		// 등록된 모든 콜백에게 알림 전달
		state.callbacks.forEach((callback) => {
			try {
				callback(notification);
			} catch (error) {
				console.error('[PushServiceManager] 콜백 실행 중 오류:', error);
			}
		});
	}

	/**
	 * 알림 수신 콜백 등록
	 */
	function onNotificationReceived(callback: NotificationCallback): void {
		state.callbacks.add(callback);
		console.log('[PushServiceManager] 알림 콜백 등록됨. 총 콜백 수:', state.callbacks.size);
	}

	/**
	 * 알림 수신 콜백 제거
	 */
	function offNotificationReceived(callback: NotificationCallback): void {
		state.callbacks.delete(callback);
		console.log('[PushServiceManager] 알림 콜백 제거됨. 총 콜백 수:', state.callbacks.size);
	}

	/**
	 * 권한 요청 (플랫폼별 서비스에 위임)
	 */
	async function requestPermission(): Promise<PermissionStatus> {
		if (!state.currentService) {
			throw createPushServiceError('SERVICE_INIT_FAILED', '서비스가 초기화되지 않았습니다.');
		}

		try {
			const permission = await state.currentService.requestPermission();
			console.log('[PushServiceManager] 권한 요청 결과:', permission);
			return permission;
		} catch (error) {
			console.error('[PushServiceManager] 권한 요청 실패:', error);
			throw error;
		}
	}

	/**
	 * 권한 상태 확인 (플랫폼별 서비스에 위임)
	 */
	async function getPermissionStatus(): Promise<PermissionStatus> {
		if (!state.currentService) {
			return 'unavailable';
		}

		try {
			const permission = await state.currentService.getPermissionStatus();
			return permission;
		} catch (error) {
			console.error('[PushServiceManager] 권한 상태 확인 실패:', error);
			return 'unavailable';
		}
	}

	/**
	 * 로컬 알림 표시 (플랫폼별 서비스에 위임)
	 */
	async function showNotification(notification: CreateNotificationData): Promise<void> {
		if (!state.currentService) {
			throw createPushServiceError('SERVICE_INIT_FAILED', '서비스가 초기화되지 않았습니다.');
		}

		try {
			await state.currentService.showNotification(notification);
			console.log('[PushServiceManager] 로컬 알림 표시 완료:', notification.content);
		} catch (error) {
			console.error('[PushServiceManager] 로컬 알림 표시 실패:', error);
			throw error;
		}
	}

	/**
	 * 서비스 상태 조회
	 */
	async function getServiceStatus(): Promise<ServiceStatus> {
		const permissionStatus = await getPermissionStatus();

		// 디바이스 정보 수집
		let deviceInfo: ServiceStatus['deviceInfo'] = {};

		try {
			const isAuthenticated = await tokenService.isAuthenticated();
			const userId = await tokenService.getCurrentUserId();

			deviceInfo = {
				userId: userId || undefined,
				isAuthenticated
			};

			// 플랫폼별 디바이스 ID 수집
			if (state.platform === 'desktop') {
				const desktopService = state.currentService as any;
				if (desktopService.getServiceStatus) {
					const desktopStatus = desktopService.getServiceStatus();
					deviceInfo.deviceId = desktopStatus.deviceId;
				}
			}
		} catch (error) {
			console.warn('[PushServiceManager] 디바이스 정보 수집 실패:', error);
		}

		return {
			platform: state.platform,
			isInitialized: state.isInitialized,
			hasService: !!state.currentService,
			registrationStatus: state.registrationStatus,
			permissionStatus,
			lastError: state.lastError || undefined,
			deviceInfo
		};
	}

	/**
	 * 성능 최적화 스케줄링 설정
	 */
	function setupPerformanceOptimizationSchedule(): void {
		// 정기적인 성능 최적화 작업 (10분마다)
		const performanceInterval = setInterval(
			async () => {
				try {
					console.log('[PushServiceManager] 정기 성능 최적화 실행');
					await Promise.all([
						performanceManager.performMaintenance(),
						notificationMemoryManager.performMaintenance()
					]);
				} catch (error) {
					console.error('[PushServiceManager] 정기 성능 최적화 실패:', error);
				}
			},
			10 * 60 * 1000
		);

		// 메모리 정리 작업 (5분마다)
		const memoryCleanupInterval = setInterval(
			async () => {
				try {
					const memoryUsage = notificationMemoryManager.optimizer.getMemoryUsage();
					if (memoryUsage.estimatedMemoryUsage > 20 * 1024 * 1024) {
						// 20MB 초과 시
						console.log('[PushServiceManager] 메모리 정리 실행');
						await notificationMemoryManager.optimizer.cleanupMemory();
					}
				} catch (error) {
					console.error('[PushServiceManager] 메모리 정리 실패:', error);
				}
			},
			5 * 60 * 1000
		);

		// cleanup 시 인터벌 정리를 위해 상태에 저장
		(state as any).performanceInterval = performanceInterval;
		(state as any).memoryCleanupInterval = memoryCleanupInterval;

		console.log('[PushServiceManager] 성능 최적화 스케줄링 설정 완료');
	}

	/**
	 * 서비스 정리
	 */
	async function cleanup(): Promise<void> {
		try {
			console.log('[PushServiceManager] 서비스 정리 시작');

			// 성능 최적화 인터벌 정리
			if ((state as any).performanceInterval) {
				clearInterval((state as any).performanceInterval);
				(state as any).performanceInterval = null;
				console.log('[PushServiceManager] 성능 최적화 인터벌 정리 완료');
			}

			if ((state as any).memoryCleanupInterval) {
				clearInterval((state as any).memoryCleanupInterval);
				(state as any).memoryCleanupInterval = null;
				console.log('[PushServiceManager] 메모리 정리 인터벌 정리 완료');
			}

			// 토큰 체크 인터벌 정리
			if ((state as any).tokenCheckInterval) {
				clearInterval((state as any).tokenCheckInterval);
				(state as any).tokenCheckInterval = null;
				console.log('[PushServiceManager] 토큰 체크 인터벌 정리 완료');
			}

			// 플랫폼별 서비스 정리
			if (state.currentService) {
				if (state.currentService.cleanup) {
					await state.currentService.cleanup();
				}
				state.currentService = null;
			}

			// 에러 핸들러들 정리
			try {
				const dataSyncHandler = getPushServiceDataSyncHandler();
				await dataSyncHandler.cleanup();
				console.log('[PushServiceManager] 데이터 동기화 핸들러 정리 완료');
			} catch (error) {
				console.warn('[PushServiceManager] 데이터 동기화 핸들러 정리 실패:', error);
			}

			// 성능 최적화 매니저들 정리
			try {
				// 캐시 정리
				performanceManager.cache.invalidateCache();

				// 메모리 최적화 정리
				notificationMemoryManager.lazyLoader.cleanupUnusedData();
				notificationMemoryManager.search.clearSearchIndex();

				console.log('[PushServiceManager] 성능 최적화 매니저 정리 완료');
			} catch (error) {
				console.warn('[PushServiceManager] 성능 최적화 매니저 정리 실패:', error);
			}

			// 세션 관련 로컬 데이터 정리
			localStorage.removeItem('cnsprowms_push_session');
			localStorage.removeItem('cnsprowms_push_fallback');

			// 상태 초기화
			state.callbacks.clear();
			state.isInitialized = false;
			state.registrationStatus = 'not_started';
			state.lastError = null;

			console.log('[PushServiceManager] 서비스 정리 완료');
		} catch (error) {
			console.error('[PushServiceManager] 서비스 정리 중 오류:', error);
		}
	}

	/**
	 * 세션 상태와 푸시 서비스 동기화
	 */
	async function synchronizeSessionState(
		userId: string | null,
		isLoggedIn: boolean
	): Promise<void> {
		try {
			console.log('[PushServiceManager] 세션 상태 동기화 시작:', { userId, isLoggedIn });

			// 세션 정보를 로컬 저장소에 저장
			const sessionInfo = {
				userId,
				isLoggedIn,
				platform: state.platform,
				timestamp: new Date().toISOString(),
				registrationStatus: state.registrationStatus
			};

			localStorage.setItem('cnsprowms_push_session', JSON.stringify(sessionInfo));

			// 로그인 상태에 따른 처리
			if (isLoggedIn && userId) {
				// 로그인 시: 푸시 서비스 활성화
				console.log('[PushServiceManager] 푸시 서비스 활성화');

				// 토큰 갱신 이벤트 리스너 등록 (필요시)
				await setupTokenRefreshListener();
			} else {
				// 로그아웃 시: 푸시 서비스 비활성화
				console.log('[PushServiceManager] 푸시 서비스 비활성화');

				// 세션 관련 로컬 데이터 정리
				localStorage.removeItem('cnsprowms_push_fallback');
			}

			console.log('[PushServiceManager] 세션 상태 동기화 완료');
		} catch (error) {
			console.error('[PushServiceManager] 세션 상태 동기화 실패:', error);
		}
	}

	/**
	 * 토큰 갱신 이벤트 리스너 설정
	 */
	async function setupTokenRefreshListener(): Promise<void> {
		try {
			// 토큰 갱신 시 사용자 ID 재연결이 필요한지 확인
			const checkAndReconnectUserId = async () => {
				try {
					const isAuthenticated = await tokenService.isAuthenticated();
					const userId = await tokenService.getCurrentUserId();

					if (isAuthenticated && userId && state.platform === 'desktop') {
						const desktopService = state.currentService as any;
						if (desktopService.setUserId) {
							console.log('[PushServiceManager] 토큰 갱신 후 사용자 ID 재연결');
							await desktopService.setUserId();
						}
					}
				} catch (error) {
					console.warn('[PushServiceManager] 토큰 갱신 후 사용자 ID 재연결 실패:', error);
				}
			};

			// 주기적으로 토큰 상태 확인 (5분마다)
			const tokenCheckInterval = setInterval(checkAndReconnectUserId, 5 * 60 * 1000);

			// cleanup 시 인터벌 정리를 위해 상태에 저장
			(state as any).tokenCheckInterval = tokenCheckInterval;

			console.log('[PushServiceManager] 토큰 갱신 리스너 설정 완료');
		} catch (error) {
			console.error('[PushServiceManager] 토큰 갱신 리스너 설정 실패:', error);
		}
	}

	/**
	 * 자동 재연결 시도
	 */
	async function attemptAutoReconnect(): Promise<void> {
		try {
			console.log('[PushServiceManager] 자동 재연결 시도');

			// 현재 인증 상태 확인
			const isAuthenticated = await tokenService.isAuthenticated();
			if (!isAuthenticated) {
				console.log('[PushServiceManager] 인증되지 않은 상태 - 재연결 중단');
				return;
			}

			// 등록 상태가 실패인 경우에만 재시도
			if (state.registrationStatus === 'failed') {
				console.log('[PushServiceManager] 등록 실패 상태 - 재등록 시도');
				await registerDevice();
			}

			// 사용자 ID 연결 상태 확인 및 재연결
			const userId = await tokenService.getCurrentUserId();
			if (userId && state.platform === 'desktop') {
				const desktopService = state.currentService as any;
				if (desktopService.setUserId) {
					await desktopService.setUserId();
					console.log('[PushServiceManager] 자동 재연결 완료');
				}
			}
		} catch (error) {
			console.error('[PushServiceManager] 자동 재연결 실패:', error);
		}
	}

	return {
		// 기본 서비스 메서드들
		initialize,
		requestPermission,
		getPermissionStatus,
		onNotificationReceived,
		offNotificationReceived,
		showNotification,
		cleanup,

		// 통합 디바이스 등록
		registerDevice,

		// 사용자 세션 관리
		onUserLogin,
		onUserLogout,

		// 서비스 상태 조회
		getServiceStatus,

		// 세션 상태 동기화 및 자동화
		synchronizeSessionState,
		attemptAutoReconnect,

		// 성능 최적화 관련 메서드들
		getPerformanceStats: () => ({
			cache: performanceManager.monitor.getCacheStats(),
			memory: notificationMemoryManager.generateMemoryReport()
		}),

		performManualOptimization: async () => {
			console.log('[PushServiceManager] 수동 성능 최적화 실행');
			await Promise.all([
				performanceManager.performMaintenance(),
				notificationMemoryManager.performMaintenance()
			]);
		},

		clearAllCaches: () => {
			console.log('[PushServiceManager] 모든 캐시 정리');
			performanceManager.cache.invalidateCache();
			notificationMemoryManager.lazyLoader.cleanupUnusedData();
			notificationMemoryManager.search.clearSearchIndex();
		},

		// 내부 상태 접근 (테스트용)
		getCurrentService: () => state.currentService,
		getPlatform: () => state.platform,
		getRegistrationStatus: () => state.registrationStatus
	};
}

/**
 * 전역 푸시 서비스 매니저 인스턴스
 */
let globalPushServiceManager: ReturnType<typeof createPushServiceManager> | null = null;

/**
 * 전역 푸시 서비스 매니저 인스턴스 획득
 */
export function getPushServiceManager(): ReturnType<typeof createPushServiceManager> {
	if (!globalPushServiceManager) {
		globalPushServiceManager = createPushServiceManager();
	}
	return globalPushServiceManager;
}

/**
 * 전역 푸시 서비스 매니저 초기화
 */
export async function initializePushServiceManager(): Promise<void> {
	const manager = getPushServiceManager();
	await manager.initialize();
}

/**
 * 전역 푸시 서비스 매니저 정리
 */
export async function cleanupPushServiceManager(): Promise<void> {
	if (globalPushServiceManager) {
		await globalPushServiceManager.cleanup();
		globalPushServiceManager = null;
	}
}

/**
 * 사용자 로그인 시 푸시 서비스 자동 연결
 */
export async function handleUserLogin(): Promise<void> {
	const manager = getPushServiceManager();
	if (manager) {
		await manager.onUserLogin();
	}
}

/**
 * 사용자 로그아웃 시 푸시 서비스 자동 해제
 */
export async function handleUserLogout(): Promise<void> {
	const manager = getPushServiceManager();
	if (manager) {
		await manager.onUserLogout();
	}
}

/**
 * 푸시 서비스 자동 재연결 시도
 */
export async function attemptPushServiceReconnect(): Promise<void> {
	const manager = getPushServiceManager();
	if (manager) {
		await manager.attemptAutoReconnect();
	}
}

/**
 * 푸시 서비스 상태 조회
 */
export async function getPushServiceStatus(): Promise<ServiceStatus> {
	const manager = getPushServiceManager();
	return await manager.getServiceStatus();
}
