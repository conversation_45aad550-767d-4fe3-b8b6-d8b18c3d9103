/**
 * Pusher Beams 기기 등록 사용 예시
 * 실제 애플리케이션에서 사용할 수 있는 패턴들
 */

import { createDesktopPushService } from '../desktopPushService';
import { createBeamsTestUtils } from '../../utils/beamsTestUtils';

/**
 * 기본 Beams 등록 플로우
 */
export async function basicBeamsRegistration() {
	try {
		console.log('[BeamsExample] 기본 등록 플로우 시작');

		// 1. 서비스 생성 및 초기화
		const pushService = createDesktopPushService();
		await pushService.initialize();

		// 2. 권한 확인 및 요청
		let permissionStatus = await pushService.getPermissionStatus();
		if (permissionStatus !== 'granted') {
			console.log('[BeamsExample] 알림 권한 요청 중...');
			permissionStatus = await pushService.requestPermission();

			if (permissionStatus !== 'granted') {
				throw new Error('알림 권한이 거부되었습니다.');
			}
		}

		// 3. Beams 클라이언트 시작
		const deviceId = await pushService.startBeamsClient();
		console.log('[BeamsExample] 기기 등록 완료:', deviceId);

		// 4. 등록 상태 확인
		const registrationState = await pushService.getRegistrationState();
		console.log('[BeamsExample] 등록 상태:', registrationState);

		return { success: true, deviceId, pushService };
	} catch (error) {
		console.error('[BeamsExample] 기본 등록 실패:', error);
		return { success: false, error };
	}
}

/**
 * 사용자 로그인 시 Beams 등록
 */
export async function registerBeamsOnLogin(userId: string) {
	try {
		console.log('[BeamsExample] 사용자 로그인 등록 시작:', userId);

		// 기본 등록 먼저 수행
		const basicResult = await basicBeamsRegistration();
		if (!basicResult.success || !basicResult.pushService) {
			throw new Error('기본 등록에 실패했습니다.');
		}

		const pushService = basicResult.pushService;

		// 사용자 ID와 기기 연결
		await pushService.registerWithBeams(userId);
		console.log('[BeamsExample] 사용자 연결 완료:', userId);

		// 기본 Interest 구독
		const defaultInterests = [
			'general', // 일반 알림
			`user-${userId}`, // 개인 알림
			'system' // 시스템 알림
		];

		await pushService.subscribeToInterests(defaultInterests);
		console.log('[BeamsExample] 기본 Interest 구독 완료:', defaultInterests);

		// 기기 정보 확인
		const deviceInfo = await pushService.getDeviceInfo();
		console.log('[BeamsExample] 최종 기기 정보:', deviceInfo);

		return {
			success: true,
			deviceId: basicResult.deviceId,
			userId,
			interests: defaultInterests,
			pushService
		};
	} catch (error) {
		console.error('[BeamsExample] 사용자 등록 실패:', error);
		return { success: false, error };
	}
}

/**
 * 역할별 Interest 구독
 */
export async function subscribeByUserRole(
	pushService: ReturnType<typeof createDesktopPushService>,
	userId: string,
	userRole: 'admin' | 'manager' | 'operator' | 'viewer'
) {
	try {
		console.log('[BeamsExample] 역할별 구독 시작:', { userId, userRole });

		const roleInterests: Record<string, string[]> = {
			admin: ['admin-alerts', 'system-maintenance', 'security-alerts', 'all-notifications'],
			manager: ['manager-reports', 'workflow-alerts', 'team-notifications'],
			operator: ['task-assignments', 'workflow-updates', 'urgent-alerts'],
			viewer: ['general-info', 'announcements']
		};

		const interests = [...roleInterests[userRole], `user-${userId}`, 'general'];

		await pushService.subscribeToInterests(interests);
		console.log('[BeamsExample] 역할별 구독 완료:', interests);

		return { success: true, interests };
	} catch (error) {
		console.error('[BeamsExample] 역할별 구독 실패:', error);
		return { success: false, error };
	}
}

/**
 * 개발 환경용 테스트 등록
 */
export async function developmentTestRegistration() {
	try {
		console.log('[BeamsExample] 개발 테스트 등록 시작');

		// 기본 등록
		const result = await registerBeamsOnLogin(`dev_user_${Date.now()}`);
		if (!result.success || !result.pushService) {
			throw new Error('개발 등록 실패');
		}

		// 테스트 유틸리티 생성
		const testUtils = createBeamsTestUtils(result.pushService);

		// 개발자 콘솔 함수 등록
		testUtils.registerGlobalTestFunctions();

		// 테스트 Interest 추가 구독
		const testInterests = ['test', 'development', 'debug'];
		await result.pushService.subscribeToInterests(testInterests);

		console.log('[BeamsExample] 개발 테스트 등록 완료');
		console.log('브라우저 콘솔에서 beamsTest.test() 실행 가능');

		return { success: true, ...result, testUtils };
	} catch (error) {
		console.error('[BeamsExample] 개발 테스트 등록 실패:', error);
		return { success: false, error };
	}
}

/**
 * 애플리케이션 시작 시 자동 등록
 */
export async function autoRegisterOnAppStart() {
	try {
		console.log('[BeamsExample] 앱 시작 시 자동 등록');

		// 로컬 스토리지에서 사용자 정보 확인
		const storedUserId = localStorage.getItem('user_id');
		const storedUserRole = localStorage.getItem('user_role') as
			| 'admin'
			| 'manager'
			| 'operator'
			| 'viewer';

		if (!storedUserId) {
			console.log('[BeamsExample] 로그인된 사용자가 없습니다. 등록 건너뜀');
			return { success: false, reason: 'no_user' };
		}

		// 사용자 등록
		const result = await registerBeamsOnLogin(storedUserId);
		if (!result.success || !result.pushService) {
			throw new Error('자동 등록 실패');
		}

		// 역할별 구독 (역할 정보가 있는 경우)
		if (storedUserRole) {
			await subscribeByUserRole(result.pushService, storedUserId, storedUserRole);
		}

		// 개발 환경에서는 테스트 함수도 등록
		if (import.meta.env.DEV) {
			const testUtils = createBeamsTestUtils(result.pushService);
			testUtils.registerGlobalTestFunctions();
		}

		console.log('[BeamsExample] 자동 등록 완료');
		return { success: true, ...result };
	} catch (error) {
		console.error('[BeamsExample] 자동 등록 실패:', error);
		return { success: false, error };
	}
}

/**
 * 로그아웃 시 정리
 */
export async function cleanupOnLogout(pushService: ReturnType<typeof createDesktopPushService>) {
	try {
		console.log('[BeamsExample] 로그아웃 정리 시작');

		// 모든 사용자별 Interest 구독 해제
		const currentInterests = await pushService.getSubscribedInterests();
		const userSpecificInterests = currentInterests.filter(
			(interest) =>
				interest.startsWith('user-') || interest.includes('admin') || interest.includes('manager')
		);

		if (userSpecificInterests.length > 0) {
			await pushService.unsubscribeFromInterests(userSpecificInterests);
			console.log('[BeamsExample] 사용자별 Interest 구독 해제:', userSpecificInterests);
		}

		// 일반적인 Interest만 유지
		const generalInterests = ['general', 'announcements'];
		await pushService.subscribeToInterests(generalInterests);

		console.log('[BeamsExample] 로그아웃 정리 완료');
		return { success: true };
	} catch (error) {
		console.error('[BeamsExample] 로그아웃 정리 실패:', error);
		return { success: false, error };
	}
}
