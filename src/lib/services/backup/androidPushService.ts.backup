/**
 * 안드로이드용 푸시 알림 서비스 - 백업 파일
 * 백업 날짜: 2025-01-20
 * 원본 파일: src/lib/services/androidPushService.ts
 * 
 * 이 파일은 Pusher Beams 클라이언트 직접 연동 구현을 위해 백업되었습니다.
 * 기존 Interest 관리 및 백엔드 연동 코드가 포함되어 있습니다.
 */

/**
 * 안드로이드용 푸시 알림 서비스
 * tauri-plugin-remote-push-api 기반 푸시 알림 처리
 * Requirements: 2.1, 2.2, 2.3, 2.4 - 안드로이드 푸시 알림
 */

import { platform } from '@tauri-apps/plugin-os';
import {
	getToken,
	requestPermission,
	onNotificationReceived,
	onTokenRefresh
} from 'tauri-plugin-remote-push-api';
import type { NotificationData, CreateNotificationData } from '$lib/types/notification';
import type {
	PushNotificationService,
	PermissionStatus,
	NotificationCallback,
	BeamsDeviceInfo
} from '../types/pushNotificationTypes';
import { createPushServiceError, BeamsRegistrationState } from '../types/pushNotificationTypes';
// pushServiceUtils 대신 직접 구현된 유틸리티 함수들 사용

/**
 * 유틸리티 함수들
 */
const utils = {
	generateDeviceId: (platform: string) =>
		`${platform}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
	getStorageKey: (platform: string, type: string) => `cnsprowms_${platform}_${type}_device_info`,
	validateDeviceInfo: (deviceInfo: BeamsDeviceInfo) => {
		return (
			deviceInfo &&
			typeof deviceInfo.deviceId === 'string' &&
			Array.isArray(deviceInfo.interests) &&
			typeof deviceInfo.registeredAt === 'string'
		);
	},
	normalizeInterests: (interests: string[]) => [...new Set(interests.filter(Boolean))]
};

/**
 * 안드로이드용 푸시 알림 서비스 상태
 */
interface AndroidPushServiceState {
	callbacks: Set<NotificationCallback>;
	isInitialized: boolean;
	deviceToken: string | null;
	beamsDeviceInfo: BeamsDeviceInfo | null;
	beamsRegistrationState: BeamsRegistrationState;
	unsubscribeNotifications?: () => void;
	unsubscribeTokenRefresh?: () => void;
}

/**
 * 안드로이드용 푸시 알림 서비스 생성
 */
export function createAndroidPushService(): PushNotificationService {
	const state: AndroidPushServiceState = {
		callbacks: new Set(),
		isInitialized: false,
		deviceToken: null,
		beamsDeviceInfo: null,
		beamsRegistrationState: BeamsRegistrationState.NOT_INITIALIZED
	};

	/**
	 * 알림 수신 처리
	 */
	function handleNotification(notification: any): void {
		try {
			console.log('[AndroidPushService] 알림 수신:', notification);

			const notificationData: NotificationData = {
				id:
					notification.messageId ||
					notification.id ||
					`android_notification_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
				content: notification.body || notification.content || '새 알림',
				expire_day:
					notification.expire_day ||
					new Date(Date.now() + 6 * 30 * 24 * 60 * 60 * 1000).toISOString(),
				created_at: new Date().toISOString(),
				read: false,
				success: notification.success === 'true' || notification.success === true,
				message: notification.title || notification.message,
				redirect: notification.redirect,
				priority: notification.priority || 'normal',
				category: notification.category,
				action_url: notification.action_url,
				image_url: notification.image_url
			};

			state.callbacks.forEach((callback) => {
				try {
					callback(notificationData);
				} catch (error) {
					console.error('[AndroidPushService] 콜백 실행 중 오류:', error);
				}
			});

			console.log('[AndroidPushService] 알림 처리 완료');
		} catch (error) {
			console.error('[AndroidPushService] 알림 처리 실패:', error);
		}
	}

	/**
	 * 안드로이드 푸시 서비스 초기화
	 */
	async function initialize(): Promise<void> {
		try {
			console.log('[AndroidPushService] 안드로이드 푸시 서비스 초기화 시작');

			const currentPlatform = await platform();
			if (currentPlatform !== 'android') {
				throw createPushServiceError('PLATFORM_NOT_SUPPORTED', '안드로이드 플랫폼이 아닙니다.');
			}

			// FCM 토큰 획득
			try {
				const token = await getToken();
				state.deviceToken = token;
				console.log('[AndroidPushService] FCM 토큰 획득 성공:', token.substring(0, 20) + '...');
			} catch (error) {
				console.error('[AndroidPushService] FCM 토큰 획득 실패:', error);
				if (import.meta.env.DEV) {
					state.deviceToken = `android_dev_token_${Date.now()}`;
					console.log('[AndroidPushService] 개발용 더미 토큰 생성:', state.deviceToken);
				} else {
					throw error;
				}
			}

			// 알림 수신 리스너 등록
			try {
				const notificationListener = await onNotificationReceived((notification) => {
					handleNotification(notification);
				});
				state.unsubscribeNotifications = () => void notificationListener.unregister();

				const tokenRefreshListener = await onTokenRefresh((newToken) => {
					console.log('[AndroidPushService] FCM 토큰 갱신:', newToken.substring(0, 20) + '...');
					state.deviceToken = newToken;
				});
				state.unsubscribeTokenRefresh = () => void tokenRefreshListener.unregister();

				console.log('[AndroidPushService] 알림 리스너 등록 완료');
			} catch (error) {
				console.error('[AndroidPushService] 알림 리스너 등록 실패:', error);
				if (!import.meta.env.DEV) {
					throw error;
				}
			}

			state.isInitialized = true;
			console.log('[AndroidPushService] 안드로이드 푸시 서비스가 초기화되었습니다.');
		} catch (error) {
			console.error('[AndroidPushService] 초기화 실패:', error);
			throw createPushServiceError(
				'SERVICE_INIT_FAILED',
				'안드로이드 푸시 서비스 초기화에 실패했습니다.',
				error as Error
			);
		}
	}

	/**
	 * 알림 권한 요청
	 */
	async function requestPermissionStatus(): Promise<PermissionStatus> {
		try {
			console.log('[AndroidPushService] 알림 권한 요청');

			if (import.meta.env.DEV) {
				console.log('[AndroidPushService] 개발 환경 - 권한 자동 승인');
				return 'granted';
			}

			const permission = await requestPermission();
			console.log('[AndroidPushService] 권한 요청 결과:', permission);

			return permission.granted ? 'granted' : 'denied';
		} catch (error) {
			console.error('[AndroidPushService] 권한 요청 실패:', error);
			throw createPushServiceError(
				'PERMISSION_REQUEST_FAILED',
				'안드로이드 알림 권한 요청에 실패했습니다.',
				error as Error
			);
		}
	}

	/**
	 * 현재 권한 상태 확인
	 */
	async function getPermissionStatus(): Promise<PermissionStatus> {
		try {
			if (import.meta.env.DEV) {
				return 'granted';
			}
			return 'granted';
		} catch (error) {
			console.error('[AndroidPushService] 권한 상태 확인 실패:', error);
			return 'unavailable';
		}
	}

	/**
	 * 알림 수신 콜백 등록
	 */
	function onNotificationReceivedCallback(callback: NotificationCallback): void {
		state.callbacks.add(callback);
	}

	/**
	 * 알림 수신 콜백 제거
	 */
	function offNotificationReceivedCallback(callback: NotificationCallback): void {
		state.callbacks.delete(callback);
	}

	/**
	 * 로컬 알림 표시
	 */
	async function showNotification(notification: CreateNotificationData): Promise<void> {
		try {
			console.log('[AndroidPushService] 안드로이드 알림 표시:', notification.content);

			const fullNotification: NotificationData = {
				id: utils.generateDeviceId('android'),
				created_at: new Date().toISOString(),
				read: false,
				...notification
			};

			state.callbacks.forEach((callback) => {
				try {
					callback(fullNotification);
				} catch (error) {
					console.error('[AndroidPushService] 콜백 실행 중 오류:', error);
				}
			});

			console.log('[AndroidPushService] 알림 표시 완료');
		} catch (error) {
			console.error('[AndroidPushService] 알림 표시 실패:', error);
			throw createPushServiceError(
				'NOTIFICATION_SEND_FAILED',
				'안드로이드 알림 표시에 실패했습니다.',
				error as Error
			);
		}
	}

	/**
	 * FCM 디바이스 토큰 획득
	 */
	async function getDeviceToken(): Promise<string> {
		try {
			if (!state.isInitialized) {
				throw createPushServiceError('SERVICE_INIT_FAILED', '서비스가 초기화되지 않았습니다.');
			}

			if (state.deviceToken) {
				return state.deviceToken;
			}

			try {
				const token = await getToken();
				state.deviceToken = token;
				console.log('[AndroidPushService] FCM 토큰 재획득 성공:', token.substring(0, 20) + '...');
				return token;
			} catch (error) {
				console.error('[AndroidPushService] FCM 토큰 재획득 실패:', error);

				if (import.meta.env.DEV) {
					state.deviceToken = `android_fcm_token_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
					console.log('[AndroidPushService] 개발용 FCM 토큰 생성:', state.deviceToken);
					return state.deviceToken;
				}

				throw createPushServiceError('TOKEN_REG_FAILED', 'FCM 토큰을 획득할 수 없습니다.');
			}
		} catch (error) {
			console.error('[AndroidPushService] FCM 토큰 획득 실패:', error);
			throw createPushServiceError(
				'TOKEN_REG_FAILED',
				'FCM 토큰 획득에 실패했습니다.',
				error as Error
			);
		}
	}

	/**
	 * Pusher Beams 클라이언트 시작
	 */
	async function startBeamsClient(): Promise<string> {
		try {
			console.log('[AndroidPushService] Pusher Beams 클라이언트 시작');

			const storageKey = utils.getStorageKey('android', 'beams');
			const existingDeviceInfo = localStorage.getItem(storageKey);

			if (existingDeviceInfo) {
				const deviceInfo: BeamsDeviceInfo = JSON.parse(existingDeviceInfo);
				if (utils.validateDeviceInfo(deviceInfo)) {
					state.beamsDeviceInfo = deviceInfo;
					state.beamsRegistrationState =
						BeamsRegistrationState.PERMISSION_GRANTED_REGISTERED_WITH_BEAMS;

					console.log('[AndroidPushService] 기존 Beams 기기 정보 로드:', deviceInfo.deviceId);
					return deviceInfo.deviceId;
				}
			}

			const fcmToken = await getDeviceToken();
			const deviceId = `android_beams_${fcmToken.slice(-12)}`;

			const deviceInfo: BeamsDeviceInfo = {
				deviceId,
				interests: [],
				registeredAt: new Date().toISOString(),
				lastUpdated: new Date().toISOString()
			};

			localStorage.setItem(storageKey, JSON.stringify(deviceInfo));
			state.beamsDeviceInfo = deviceInfo;
			state.beamsRegistrationState =
				BeamsRegistrationState.PERMISSION_GRANTED_REGISTERED_WITH_BEAMS;

			console.log('[AndroidPushService] 새로운 Beams 기기 등록 완료:', deviceId);
			return deviceId;
		} catch (error) {
			console.error('[AndroidPushService] Beams 클라이언트 시작 실패:', error);
			state.beamsRegistrationState = BeamsRegistrationState.PERMISSION_DENIED;
			throw createPushServiceError(
				'SERVICE_INIT_FAILED',
				'Pusher Beams 클라이언트 시작에 실패했습니다.',
				error as Error
			);
		}
	}

	/**
	 * 사용자 ID와 기기 연결
	 */
	async function registerWithBeams(userId?: string): Promise<void> {
		try {
			if (!state.beamsDeviceInfo) {
				throw createPushServiceError(
					'SERVICE_INIT_FAILED',
					'Beams 클라이언트가 시작되지 않았습니다.'
				);
			}

			console.log('[AndroidPushService] 사용자 ID와 기기 연결:', userId);

			if (state.deviceToken && userId) {
				await registerDeviceWithBeams(state.deviceToken, userId);
			}

			const updatedDeviceInfo: BeamsDeviceInfo = {
				...state.beamsDeviceInfo,
				userId,
				lastUpdated: new Date().toISOString()
			};

			const storageKey = utils.getStorageKey('android', 'beams');
			localStorage.setItem(storageKey, JSON.stringify(updatedDeviceInfo));
			state.beamsDeviceInfo = updatedDeviceInfo;

			console.log('[AndroidPushService] 사용자 연결 완료');
		} catch (error) {
			console.error('[AndroidPushService] 사용자 연결 실패:', error);
			throw createPushServiceError(
				'TOKEN_REG_FAILED',
				'사용자 ID 연결에 실패했습니다.',
				error as Error
			);
		}
	}

	/**
	 * FCM 토큰을 Pusher Beams에 등록
	 */
	async function registerDeviceWithBeams(fcmToken: string, userId: string): Promise<void> {
		try {
			console.log('[AndroidPushService] FCM 토큰을 Pusher Beams에 등록 중...');

			// pushNotificationApi를 사용하여 백엔드에 등록
			const { pushNotificationApi, deviceRegistrationHelpers } = await import(
				'./pushNotificationApi'
			);

			const deviceInfo = await deviceRegistrationHelpers.getDeviceInfo();
			const defaultInterests = deviceRegistrationHelpers.getDefaultInterests();

			const response = await pushNotificationApi.registerDevice({
				token: fcmToken,
				platform: 'android',
				userId: userId,
				deviceInfo,
				interests: defaultInterests
			});

			if (response.success) {
				console.log('[AndroidPushService] FCM 토큰 Beams 등록 성공:', response.message);
			} else {
				throw new Error(response.message || 'Beams 등록에 실패했습니다.');
			}
		} catch (error) {
			console.error('[AndroidPushService] FCM 토큰 Beams 등록 실패:', error);

			if (import.meta.env.DEV) {
				console.log('[AndroidPushService] 개발 환경 - Beams 등록 시뮬레이션');
				return;
			}

			throw error;
		}
	}

	/**
	 * Interest 구독
	 */
	async function subscribeToInterests(interests: string[]): Promise<void> {
		try {
			if (!state.beamsDeviceInfo) {
				throw createPushServiceError(
					'SERVICE_INIT_FAILED',
					'Beams 클라이언트가 시작되지 않았습니다.'
				);
			}

			console.log('[AndroidPushService] Interest 구독:', interests);

			const currentInterests = state.beamsDeviceInfo.interests || [];
			const newInterests = utils.normalizeInterests([...currentInterests, ...interests]);

			const updatedDeviceInfo: BeamsDeviceInfo = {
				...state.beamsDeviceInfo,
				interests: newInterests,
				lastUpdated: new Date().toISOString()
			};

			const storageKey = utils.getStorageKey('android', 'beams');
			localStorage.setItem(storageKey, JSON.stringify(updatedDeviceInfo));
			state.beamsDeviceInfo = updatedDeviceInfo;

			console.log('[AndroidPushService] Interest 구독 완료:', newInterests);
		} catch (error) {
			console.error('[AndroidPushService] Interest 구독 실패:', error);
			throw createPushServiceError(
				'SERVICE_INIT_FAILED',
				'Interest 구독에 실패했습니다.',
				error as Error
			);
		}
	}

	/**
	 * Interest 구독 해제
	 */
	async function unsubscribeFromInterests(interests: string[]): Promise<void> {
		try {
			if (!state.beamsDeviceInfo) {
				throw createPushServiceError(
					'SERVICE_INIT_FAILED',
					'Beams 클라이언트가 시작되지 않았습니다.'
				);
			}

			console.log('[AndroidPushService] Interest 구독 해제:', interests);

			const currentInterests = state.beamsDeviceInfo.interests || [];
			const remainingInterests = currentInterests.filter(
				(interest) => !interests.includes(interest)
			);

			const updatedDeviceInfo: BeamsDeviceInfo = {
				...state.beamsDeviceInfo,
				interests: remainingInterests,
				lastUpdated: new Date().toISOString()
			};

			const storageKey = utils.getStorageKey('android', 'beams');
			localStorage.setItem(storageKey, JSON.stringify(updatedDeviceInfo));
			state.beamsDeviceInfo = updatedDeviceInfo;

			console.log(
				'[AndroidPushService] Interest 구독 해제 완료. 남은 Interest:',
				remainingInterests
			);
		} catch (error) {
			console.error('[AndroidPushService] Interest 구독 해제 실패:', error);
			throw createPushServiceError(
				'SERVICE_INIT_FAILED',
				'Interest 구독 해제에 실패했습니다.',
				error as Error
			);
		}
	}

	/**
	 * 구독 중인 Interest 목록 조회
	 */
	async function getSubscribedInterests(): Promise<string[]> {
		try {
			if (!state.beamsDeviceInfo) {
				return [];
			}
			return state.beamsDeviceInfo.interests || [];
		} catch (error) {
			console.error('[AndroidPushService] Interest 목록 조회 실패:', error);
			return [];
		}
	}

	/**
	 * Beams 등록 상태 확인
	 */
	async function getRegistrationState(): Promise<BeamsRegistrationState> {
		try {
			const permissionStatus = await getPermissionStatus();

			if (permissionStatus === 'denied') {
				state.beamsRegistrationState = BeamsRegistrationState.PERMISSION_DENIED;
			} else if (permissionStatus === 'default') {
				state.beamsRegistrationState = BeamsRegistrationState.PERMISSION_PROMPT_REQUIRED;
			} else if (permissionStatus === 'granted') {
				if (state.beamsDeviceInfo) {
					state.beamsRegistrationState =
						BeamsRegistrationState.PERMISSION_GRANTED_REGISTERED_WITH_BEAMS;
				} else {
					state.beamsRegistrationState =
						BeamsRegistrationState.PERMISSION_GRANTED_NOT_REGISTERED_WITH_BEAMS;
				}
			}

			return state.beamsRegistrationState;
		} catch (error) {
			console.error('[AndroidPushService] 등록 상태 확인 실패:', error);
			return BeamsRegistrationState.NOT_INITIALIZED;
		}
	}

	/**
	 * 기기 정보 조회
	 */
	async function getDeviceInfo(): Promise<BeamsDeviceInfo | null> {
		return state.beamsDeviceInfo;
	}

	/**
	 * Beams 데이터 정리
	 */
	async function clearBeamsData(): Promise<void> {
		try {
			const storageKey = utils.getStorageKey('android', 'beams');
			localStorage.removeItem(storageKey);
			state.beamsDeviceInfo = null;
			state.beamsRegistrationState = BeamsRegistrationState.NOT_INITIALIZED;

			console.log('[AndroidPushService] Beams 데이터 정리 완료');
		} catch (error) {
			console.error('[AndroidPushService] Beams 데이터 정리 실패:', error);
		}
	}

	/**
	 * 서비스 정리
	 */
	function cleanup(): void {
		if (state.unsubscribeNotifications) {
			state.unsubscribeNotifications();
			state.unsubscribeNotifications = undefined;
		}
		if (state.unsubscribeTokenRefresh) {
			state.unsubscribeTokenRefresh();
			state.unsubscribeTokenRefresh = undefined;
		}

		state.callbacks.clear();
		state.deviceToken = null;
		state.isInitialized = false;
		state.beamsDeviceInfo = null;
		state.beamsRegistrationState = BeamsRegistrationState.NOT_INITIALIZED;
		console.log('[AndroidPushService] 안드로이드 푸시 서비스가 정리되었습니다.');
	}

	// 호환성을 위한 더미 메서드들
	async function registerServiceWorker(): Promise<void> {
		console.warn(
			'[AndroidPushService] Service workers are not applicable on the Android platform.'
		);
	}

	async function unregisterServiceWorker(): Promise<void> {
		console.warn(
			'[AndroidPushService] Service workers are not applicable on the Android platform.'
		);
	}

	function getServiceWorkerRegistration(): ServiceWorkerRegistration | null {
		console.warn(
			'[AndroidPushService] Service workers are not applicable on the Android platform.'
		);
		return null;
	}

	function getBeamsClient(): any {
		console.warn('[AndroidPushService] Beams client instance is not directly exposed on Android.');
		return null;
	}

	async function clearBeamsClient(): Promise<void> {
		console.log('[AndroidPushService] clearBeamsClient called, executing clearBeamsData.');
		return clearBeamsData();
	}

	async function registerDevice(): Promise<void> {
		console.warn(
			'[AndroidPushService] registerDevice is deprecated. Use registerWithBeams instead.'
		);
	}

	async function registerDeviceUnified(): Promise<any> {
		console.warn(
			'[AndroidPushService] registerDeviceUnified is deprecated. Use registerWithBeams instead.'
		);
		return { success: false, method: 'PUSHER_BEAMS', message: 'Use registerWithBeams instead.' };
	}

	return {
		// 기본 서비스 메서드들
		initialize,
		requestPermission: requestPermissionStatus,
		getPermissionStatus,
		onNotificationReceived: onNotificationReceivedCallback,
		offNotificationReceived: offNotificationReceivedCallback,
		showNotification,
		cleanup,

		// Beams 관련 메서드들
		startBeamsClient,
		registerWithBeams,
		subscribeToInterests,
		unsubscribeFromInterests,
		getSubscribedInterests,
		getRegistrationState,
		getDeviceInfo,
		clearBeamsData,

		// FCM 토큰 관련
		getDeviceToken,

		// 호환성을 위한 더미 메서드들
		registerDevice,
		registerDeviceUnified,
		registerServiceWorker,
		unregisterServiceWorker,
		getServiceWorkerRegistration,
		getBeamsClient,
		clearBeamsClient
	};
}