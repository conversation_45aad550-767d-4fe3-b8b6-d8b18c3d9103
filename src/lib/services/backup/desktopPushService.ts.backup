/**
 * 데스크탑용 푸시 알림 서비스 - 백업 파일
 * 백업 날짜: 2025-01-20
 * 원본 파일: src/lib/services/desktopPushService.ts
 * 
 * 이 파일은 Pusher Beams 클라이언트 직접 연동 구현을 위해 백업되었습니다.
 * 기존 Interest 관리 및 백엔드 연동 코드가 포함되어 있습니다.
 */

/**
 * 데스크탑용 푸시 알림 서비스
 * Pusher Beams Web SDK와 Tauri Notification Plugin 사용
 * Requirements: 1.1, 1.2, 1.3 - 데스크탑 네이티브 푸시 알림
 */

import { isPermissionGranted, sendNotification } from '@tauri-apps/plugin-notification';
import * as PushNotifications from '@pusher/push-notifications-web';
import type { NotificationData, CreateNotificationData } from '$lib/types/notification';
import type {
	PermissionStatus,
	NotificationCallback,
	BeamsDeviceInfo
} from '$lib/types/pushNotificationTypes';
import { createPushServiceError, BeamsRegistrationState } from '$lib/types/pushNotificationTypes';
import { pushServiceUtils } from '$lib/utils/pushServiceUtils';
import { Client } from '@pusher/push-notifications-web';

/**
 * 유틸리티 함수들
 */
const utils = {
	generateDeviceId: (platform: string) =>
		`${platform}_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
	getStorageKey: (platform: string, type: string) => `cnsprowms_${platform}_${type}_device_info`,
	validateDeviceInfo: (deviceInfo: BeamsDeviceInfo) => {
		return (deviceInfo && Array.isArray(deviceInfo.interests));
	},
	normalizeInterests: (interests: string[]) => [...new Set(interests.filter(Boolean))]
};

/**
 * 데스크탑용 푸시 알림 서비스 상태
 */
interface DesktopPushServiceState {
	callbacks: Set<NotificationCallback>;
	isInitialized: boolean;
	beamsDeviceInfo: BeamsDeviceInfo | null;
	beamsRegistrationState: BeamsRegistrationState;
	beamsClient: PushNotifications.Client | null;
	serviceWorkerRegistration: ServiceWorkerRegistration | null;
}

/**
 * 데스크탑용 푸시 알림 서비스 생성
 */
export function createDesktopPushService(): {
	initialize: () => Promise<void>;
	requestPermission: () => Promise<PermissionStatus>;
	getPermissionStatus: () => Promise<PermissionStatus>;
	onNotificationReceived: (callback: NotificationCallback) => void;
	offNotificationReceived: (callback: NotificationCallback) => void;
	showNotification: (notification: CreateNotificationData) => Promise<void>;
	cleanup: () => Promise<void>;
	registerServiceWorker: () => Promise<void>;
	unregisterServiceWorker: () => Promise<void>;
	startBeamsClient: () => Promise<string>;
	registerWithBeams: (userId?: string) => Promise<void>;
	subscribeToInterests: (interests: string[]) => Promise<void>;
	unsubscribeFromInterests: (interests: string[]) => Promise<void>;
	getSubscribedInterests: () => Promise<string[]>;
	getRegistrationState: () => Promise<BeamsRegistrationState>;
	getDeviceInfo: () => Promise<BeamsDeviceInfo | null>;
	clearBeamsData: () => Promise<void>;
	clearBeamsClient: () => Promise<void>;
	getDeviceToken: () => Promise<string>;
	registerDevice: () => Promise<void>;
	getBeamsClient: () => Client | null;
	getServiceWorkerRegistration: () => ServiceWorkerRegistration | null
} {
	const state: DesktopPushServiceState = {
		callbacks: new Set(),
		isInitialized: false,
		beamsDeviceInfo: null,
		beamsRegistrationState: BeamsRegistrationState.NOT_INITIALIZED,
		beamsClient: null,
		serviceWorkerRegistration: null
	};

	/**
	 * Service Worker 등록
	 * Requirements: 1.2 - Service Worker를 통한 백그라운드 알림 수신
	 */
	async function registerServiceWorker(): Promise<void> {
		try {
			if (typeof window === 'undefined' || !('serviceWorker' in navigator)) {
				console.warn('[DesktopPushService] Service Worker를 지원하지 않는 환경입니다.');
				return;
			}

			if (!('PushManager' in window)) {
				console.warn('[DesktopPushService] Push Manager를 지원하지 않는 환경입니다.');
				return;
			}

			// Service Worker 등록
			const registration = await navigator.serviceWorker.register('/service-worker.js');
			state.serviceWorkerRegistration = registration;

			console.log('[DesktopPushService] Service Worker 등록 완료:', registration.scope);

			// Service Worker 상태 확인
			if (registration.installing) {
				console.log('[DesktopPushService] Service Worker 설치 중...');
			} else if (registration.waiting) {
				console.log('[DesktopPushService] Service Worker 대기 중...');
			} else if (registration.active) {
				console.log('[DesktopPushService] Service Worker 활성화됨');
			}

			// Service Worker 업데이트 확인
			registration.addEventListener('updatefound', () => {
				console.log('[DesktopPushService] Service Worker 업데이트 발견');
			});
		} catch (error) {
			console.error('[DesktopPushService] Service Worker 등록 실패:', error);
			throw createPushServiceError(
				'SERVICE_INIT_FAILED',
				'Service Worker 등록에 실패했습니다.',
				error as Error
			);
		}
	}

	/**
	 * 서비스 초기화
	 * Requirements: 1.1 - 데스크탑 앱에서 네이티브 푸시 알림 표시
	 */
	async function initialize(): Promise<void> {
		try {
			// Service Worker 등록 (웹 환경에서만)
			if (typeof window !== 'undefined') {
				await registerServiceWorker();
			}

			const hasPermission = await isPermissionGranted();

			if (!hasPermission) {
				console.warn('[DesktopPushService] 알림 권한이 없습니다. 권한 요청이 필요합니다.');
			}

			state.isInitialized = true;
			console.log('[DesktopPushService] 데스크탑 푸시 서비스가 초기화되었습니다.');
		} catch (error) {
			console.error('[DesktopPushService] 초기화 실패:', error);
			throw createPushServiceError(
				'SERVICE_INIT_FAILED',
				'데스크탑 푸시 서비스 초기화에 실패했습니다.',
				error as Error
			);
		}
	}

	/**
	 * 알림 권한 요청
	 * Requirements: 1.3 - 권한 요청 및 상태 관리
	 */
	async function requestPermission(): Promise<PermissionStatus> {
		try {
			const hasPermission = await isPermissionGranted();
			if (hasPermission) {
				return 'granted';
			}

			// Tauri에서는 권한 요청이 자동으로 처리됨
			console.log('[DesktopPushService] 알림 권한 요청 완료');
			return 'granted';
		} catch (error) {
			console.error('[DesktopPushService] 권한 요청 실패:', error);
			throw createPushServiceError(
				'PERMISSION_REQUEST_FAILED',
				'알림 권한 요청에 실패했습니다.',
				error as Error
			);
		}
	}

	/**
	 * 현재 권한 상태 확인
	 */
	async function getPermissionStatus(): Promise<PermissionStatus> {
		try {
			const hasPermission = await isPermissionGranted();
			return hasPermission ? 'granted' : 'default';
		} catch (error) {
			console.error('[DesktopPushService] 권한 상태 확인 실패:', error);
			return 'unavailable';
		}
	}

	/**
	 * 알림 수신 콜백 등록
	 * Requirements: 1.2 - 백그라운드 알림 수신 처리
	 */
	function onNotificationReceived(callback: NotificationCallback): void {
		state.callbacks.add(callback);
	}

	/**
	 * 알림 수신 콜백 제거
	 */
	function offNotificationReceived(callback: NotificationCallback): void {
		state.callbacks.delete(callback);
	}

	/**
	 * 알림 표시
	 * Requirements: 1.1 - 네이티브 푸시 알림 표시
	 */
	async function showNotification(notification: CreateNotificationData): Promise<void> {
		try {
			const hasPermission = await isPermissionGranted();
			if (!hasPermission) {
				throw createPushServiceError(
					'PERMISSION_DENIED',
					'알림 권한이 없습니다. 권한을 먼저 요청해주세요.'
				);
			}

			sendNotification({
				title: '새 알림',
				body: notification.content,
				icon: notification.image_url
			});

			console.log('[DesktopPushService] 알림이 표시되었습니다:', notification.content);

			// 콜백 호출
			const fullNotification: NotificationData = {
				id: utils.generateDeviceId('desktop'),
				created_at: new Date().toISOString(),
				read: false,
				...notification
			};

			state.callbacks.forEach((callback) => {
				try {
					callback(fullNotification);
				} catch (error) {
					console.error('[DesktopPushService] 콜백 실행 중 오류:', error);
				}
			});
		} catch (error) {
			console.error('[DesktopPushService] 알림 표시 실패:', error);
			if ('code' in (error as any)) {
				throw error;
			}
			throw createPushServiceError(
				'NOTIFICATION_SEND_FAILED',
				'알림 표시에 실패했습니다.',
				error as Error
			);
		}
	}

	// === Beams 관련 메서드들 ===

	/**
	 * Pusher Beams 클라이언트 시작
	 * Requirements: 5.1 - Beams 클라이언트 초기화 및 기기 등록
	 */
	async function startBeamsClient(): Promise<string> {
		try {
			console.log('[DesktopPushService] Pusher Beams 클라이언트 시작');

			// 환경 변수에서 Instance ID 가져오기
			const instanceId = import.meta.env.VITE_PUSHER_BEAMS_INSTANCE_ID;
			if (!instanceId) {
				throw new Error('VITE_PUSHER_BEAMS_INSTANCE_ID 환경 변수가 설정되지 않았습니다.');
			}

			// 웹 환경에서만 실제 Beams 클라이언트 생성
			if (typeof window !== 'undefined' && 'serviceWorker' in navigator) {
				try {
					// Pusher Beams 클라이언트 생성
					state.beamsClient = new PushNotifications.Client({
						instanceId: instanceId
					});

					// 클라이언트 시작 및 기기 등록
					await state.beamsClient.start();
					console.log('[DesktopPushService] Pusher Beams 클라이언트 시작 완료');

					// 기기 ID 가져오기 (Beams에서 자동 생성)
					const deviceId = await state.beamsClient.getDeviceId();
					console.log('[DesktopPushService] Beams 기기 ID:', deviceId);

					// 기기 정보 저장
					const deviceInfo: BeamsDeviceInfo = {
						deviceId,
						interests: [],
						registeredAt: new Date().toISOString(),
						lastUpdated: new Date().toISOString()
					};

					const storageKey = pushServiceUtils.getStorageKey('desktop', 'beams');
					localStorage.setItem(storageKey, JSON.stringify(deviceInfo));
					state.beamsDeviceInfo = deviceInfo;
					state.beamsRegistrationState =
						BeamsRegistrationState.PERMISSION_GRANTED_REGISTERED_WITH_BEAMS;

					console.log('[DesktopPushService] Beams 기기 등록 완료:', deviceId);
					return deviceId;
				} catch (beamsError) {
					console.error('[DesktopPushService] Pusher Beams 초기화 실패:', beamsError);

					// 개발 환경에서는 시뮬레이션으로 폴백
					if (import.meta.env.DEV) {
						console.log('[DesktopPushService] 개발 환경 - 시뮬레이션 모드로 폴백');
						return await startBeamsClientSimulation();
					}

					throw beamsError;
				}
			} else {
				// Tauri 환경이나 Service Worker 미지원 환경에서는 시뮬레이션
				console.log('[DesktopPushService] 웹 푸시 미지원 환경 - 시뮬레이션 모드');
				return await startBeamsClientSimulation();
			}
		} catch (error) {
			console.error('[DesktopPushService] Beams 클라이언트 시작 실패:', error);
			state.beamsRegistrationState = BeamsRegistrationState.PERMISSION_DENIED;
			throw createPushServiceError(
				'SERVICE_INIT_FAILED',
				'Pusher Beams 클라이언트 시작에 실패했습니다.',
				error as Error
			);
		}
	}

	/**
	 * Beams 클라이언트 시뮬레이션 (개발 환경 또는 폴백용)
	 */
	async function startBeamsClientSimulation(): Promise<string> {
		const storageKey = pushServiceUtils.getStorageKey('desktop', 'beams');
		const existingDeviceInfo = localStorage.getItem(storageKey);

		if (existingDeviceInfo) {
			const deviceInfo: BeamsDeviceInfo = JSON.parse(existingDeviceInfo);
			if (pushServiceUtils.validateDeviceInfo(deviceInfo)) {
				state.beamsDeviceInfo = deviceInfo;
				state.beamsRegistrationState =
					BeamsRegistrationState.PERMISSION_GRANTED_REGISTERED_WITH_BEAMS;

				console.log('[DesktopPushService] 기존 시뮬레이션 기기 정보 로드:', deviceInfo.deviceId);
				return deviceInfo.deviceId;
			}
		}

		const deviceId = pushServiceUtils.generateDeviceId('desktop_beams_sim');
		const deviceInfo: BeamsDeviceInfo = {
			deviceId,
			interests: [],
			registeredAt: new Date().toISOString(),
			lastUpdated: new Date().toISOString()
		};

		localStorage.setItem(storageKey, JSON.stringify(deviceInfo));
		state.beamsDeviceInfo = deviceInfo;
		state.beamsRegistrationState = BeamsRegistrationState.PERMISSION_GRANTED_REGISTERED_WITH_BEAMS;

		console.log('[DesktopPushService] 시뮬레이션 기기 등록 완료:', deviceId);
		return deviceId;
	}

	/**
	 * 사용자 ID와 기기 연결
	 * Requirements: 5.3 - 사용자 로그인 시 기기와 사용자 ID 연결
	 */
	async function registerWithBeams(userId?: string): Promise<void> {
		try {
			if (!state.beamsDeviceInfo) {
				throw createPushServiceError(
					'SERVICE_INIT_FAILED',
					'Beams 클라이언트가 시작되지 않았습니다.'
				);
			}

			console.log('[DesktopPushService] 사용자 ID와 기기 연결:', userId);

			// 실제 Beams 클라이언트가 있는 경우 사용자 ID 설정
			if (state.beamsClient && userId) {
				try {
					// Beams 인증 제공자 설정 (실제 구현에서는 백엔드 JWT 토큰 사용)
					const beamsAuthProvider = new PushNotifications.TokenProvider({
						url: '/api/beams-auth', // 백엔드 인증 엔드포인트
						headers: {
							Authorization: `Bearer ${getAuthToken()}` // 현재 사용자 토큰
						}
					});

					await state.beamsClient.setUserId(userId, beamsAuthProvider);
					console.log('[DesktopPushService] Beams 사용자 ID 설정 완료:', userId);
				} catch (beamsError) {
					console.error('[DesktopPushService] Beams 사용자 ID 설정 실패:', beamsError);

					// 개발 환경에서는 시뮬레이션으로 계속 진행
					if (!import.meta.env.DEV) {
						throw beamsError;
					}
				}
			}

			// 로컬 기기 정보 업데이트
			const updatedDeviceInfo: BeamsDeviceInfo = {
				...state.beamsDeviceInfo,
				userId,
				lastUpdated: new Date().toISOString()
			};

			const storageKey = pushServiceUtils.getStorageKey('desktop', 'beams');
			localStorage.setItem(storageKey, JSON.stringify(updatedDeviceInfo));
			state.beamsDeviceInfo = updatedDeviceInfo;

			console.log('[DesktopPushService] 사용자 연결 완료');
		} catch (error) {
			console.error('[DesktopPushService] 사용자 연결 실패:', error);
			throw createPushServiceError(
				'TOKEN_REG_FAILED',
				'사용자 ID 연결에 실패했습니다.',
				error as Error
			);
		}
	}

	/**
	 * 현재 인증 토큰 가져오기 (헬퍼 함수)
	 */
	function getAuthToken(): string {
		// 실제 구현에서는 토큰 스토어에서 가져오기
		if (typeof window !== 'undefined') {
			return localStorage.getItem('auth_token') || '';
		}
		return '';
	}

	/**
	 * Interest 구독
	 * Requirements: 5.4 - Interest 구독/구독 해제 기능
	 */
	async function subscribeToInterests(interests: string[]): Promise<void> {
		try {
			if (!state.beamsDeviceInfo) {
				throw createPushServiceError(
					'SERVICE_INIT_FAILED',
					'Beams 클라이언트가 시작되지 않았습니다.'
				);
			}

			console.log('[DesktopPushService] Interest 구독:', interests);

			// 실제 Beams 클라이언트가 있는 경우 Interest 구독
			if (state.beamsClient) {
				try {
					for (const interest of interests) {
						await state.beamsClient.addDeviceInterest(interest);
						console.log('[DesktopPushService] Beams Interest 구독 완료:', interest);
					}
				} catch (beamsError) {
					console.error('[DesktopPushService] Beams Interest 구독 실패:', beamsError);

					// 개발 환경에서는 시뮬레이션으로 계속 진행
					if (!import.meta.env.DEV) {
						throw beamsError;
					}
				}
			}

			// 로컬 상태 업데이트
			const currentInterests = state.beamsDeviceInfo.interests || [];
			const newInterests = pushServiceUtils.normalizeInterests([...currentInterests, ...interests]);

			const updatedDeviceInfo: BeamsDeviceInfo = {
				...state.beamsDeviceInfo,
				interests: newInterests,
				lastUpdated: new Date().toISOString()
			};

			const storageKey = pushServiceUtils.getStorageKey('desktop', 'beams');
			localStorage.setItem(storageKey, JSON.stringify(updatedDeviceInfo));
			state.beamsDeviceInfo = updatedDeviceInfo;

			console.log('[DesktopPushService] Interest 구독 완료:', newInterests);
		} catch (error) {
			console.error('[DesktopPushService] Interest 구독 실패:', error);
			throw createPushServiceError(
				'SERVICE_INIT_FAILED',
				'Interest 구독에 실패했습니다.',
				error as Error
			);
		}
	}

	/**
	 * Interest 구독 해제
	 * Requirements: 5.4 - Interest 구독/구독 해제 기능
	 */
	async function unsubscribeFromInterests(interests: string[]): Promise<void> {
		try {
			if (!state.beamsDeviceInfo) {
				throw createPushServiceError(
					'SERVICE_INIT_FAILED',
					'Beams 클라이언트가 시작되지 않았습니다.'
				);
			}

			console.log('[DesktopPushService] Interest 구독 해제:', interests);

			// 실제 Beams 클라이언트가 있는 경우 Interest 구독 해제
			if (state.beamsClient) {
				try {
					for (const interest of interests) {
						await state.beamsClient.removeDeviceInterest(interest);
						console.log('[DesktopPushService] Beams Interest 구독 해제 완료:', interest);
					}
				} catch (beamsError) {
					console.error('[DesktopPushService] Beams Interest 구독 해제 실패:', beamsError);

					// 개발 환경에서는 시뮬레이션으로 계속 진행
					if (!import.meta.env.DEV) {
						throw beamsError;
					}
				}
			}

			// 로컬 상태 업데이트
			const currentInterests = state.beamsDeviceInfo.interests || [];
			const remainingInterests = currentInterests.filter(
				(interest) => !interests.includes(interest)
			);

			const updatedDeviceInfo: BeamsDeviceInfo = {
				...state.beamsDeviceInfo,
				interests: remainingInterests,
				lastUpdated: new Date().toISOString()
			};

			const storageKey = pushServiceUtils.getStorageKey('desktop', 'beams');
			localStorage.setItem(storageKey, JSON.stringify(updatedDeviceInfo));
			state.beamsDeviceInfo = updatedDeviceInfo;

			console.log(
				'[DesktopPushService] Interest 구독 해제 완료. 남은 Interest:',
				remainingInterests
			);
		} catch (error) {
			console.error('[DesktopPushService] Interest 구독 해제 실패:', error);
			throw createPushServiceError(
				'SERVICE_INIT_FAILED',
				'Interest 구독 해제에 실패했습니다.',
				error as Error
			);
		}
	}

	/**
	 * 구독 중인 Interest 목록 조회
	 */
	async function getSubscribedInterests(): Promise<string[]> {
		try {
			if (!state.beamsDeviceInfo) {
				return [];
			}
			return state.beamsDeviceInfo.interests || [];
		} catch (error) {
			console.error('[DesktopPushService] Interest 목록 조회 실패:', error);
			return [];
		}
	}

	/**
	 * Beams 등록 상태 확인
	 * Requirements: 5.1, 5.2 - 등록 상태 확인 및 관리 로직
	 */
	async function getRegistrationState(): Promise<BeamsRegistrationState> {
		try {
			const permissionStatus = await getPermissionStatus();

			if (permissionStatus === 'denied') {
				state.beamsRegistrationState = BeamsRegistrationState.PERMISSION_DENIED;
			} else if (permissionStatus === 'default') {
				state.beamsRegistrationState = BeamsRegistrationState.PERMISSION_PROMPT_REQUIRED;
			} else if (permissionStatus === 'granted') {
				if (state.beamsDeviceInfo) {
					state.beamsRegistrationState =
						BeamsRegistrationState.PERMISSION_GRANTED_REGISTERED_WITH_BEAMS;
				} else {
					state.beamsRegistrationState =
						BeamsRegistrationState.PERMISSION_GRANTED_NOT_REGISTERED_WITH_BEAMS;
				}
			}

			return state.beamsRegistrationState;
		} catch (error) {
			console.error('[DesktopPushService] 등록 상태 확인 실패:', error);
			return BeamsRegistrationState.NOT_INITIALIZED;
		}
	}

	/**
	 * 기기 정보 조회
	 */
	async function getDeviceInfo(): Promise<BeamsDeviceInfo | null> {
		return state.beamsDeviceInfo;
	}

	/**
	 * Beams 데이터 정리
	 */
	async function clearBeamsData(): Promise<void> {
		try {
			const storageKey = pushServiceUtils.getStorageKey('desktop', 'beams');
			localStorage.removeItem(storageKey);
			state.beamsDeviceInfo = null;
			state.beamsRegistrationState = BeamsRegistrationState.NOT_INITIALIZED;

			console.log('[DesktopPushService] Beams 데이터 정리 완료');
		} catch (error) {
			console.error('[DesktopPushService] Beams 데이터 정리 실패:', error);
		}
	}

	/**
	 * 디바이스 토큰 획득 (Beams 기반)
	 */
	async function getDeviceToken(): Promise<string> {
		try {
			// Beams 기기 정보가 있으면 해당 ID 사용
			if (state.beamsDeviceInfo) {
				return `desktop_beams_${state.beamsDeviceInfo.deviceId}`;
			}

			// 폴백으로 임시 토큰 생성
			return pushServiceUtils.generateDeviceId('desktop_fallback');
		} catch (error) {
			console.error('[DesktopPushService] 디바이스 토큰 생성 실패:', error);
			throw createPushServiceError(
				'TOKEN_REG_FAILED',
				'데스크탑 디바이스 토큰 생성에 실패했습니다.',
				error as Error
			);
		}
	}

	/**
	 * 레거시 디바이스 등록 (호환성용)
	 */
	async function registerDevice(): Promise<void> {
		console.warn(
			'[DesktopPushService] registerDevice is deprecated. Use registerWithBeams instead.'
		);
	}

	/**
	 * Service Worker 해제
	 */
	async function unregisterServiceWorker(): Promise<void> {
		try {
			if (state.serviceWorkerRegistration) {
				const result = await state.serviceWorkerRegistration.unregister();
				if (result) {
					console.log('[DesktopPushService] Service Worker 해제 완료');
				} else {
					console.warn('[DesktopPushService] Service Worker 해제 실패');
				}
				state.serviceWorkerRegistration = null;
			}
		} catch (error) {
			console.error('[DesktopPushService] Service Worker 해제 중 오류:', error);
		}
	}

	/**
	 * Beams 클라이언트 정리
	 */
	async function clearBeamsClient(): Promise<void> {
		try {
			if (state.beamsClient) {
				// Beams 클라이언트 정리 (필요시 구독 해제 등)
				console.log('[DesktopPushService] Beams 클라이언트 정리 중...');

				// 모든 Interest 구독 해제
				if (state.beamsDeviceInfo?.interests) {
					await unsubscribeFromInterests(state.beamsDeviceInfo.interests);
				}

				state.beamsClient = null;
				console.log('[DesktopPushService] Beams 클라이언트 정리 완료');
			}
		} catch (error) {
			console.error('[DesktopPushService] Beams 클라이언트 정리 중 오류:', error);
		}
	}

	/**
	 * 서비스 정리
	 */
	async function cleanup(): Promise<void> {
		try {
			// Beams 클라이언트 정리
			await clearBeamsClient();

			// Service Worker 해제
			await unregisterServiceWorker();

			// 상태 초기화
			state.callbacks.clear();
			state.isInitialized = false;
			state.beamsDeviceInfo = null;
			state.beamsRegistrationState = BeamsRegistrationState.NOT_INITIALIZED;

			console.log('[DesktopPushService] 서비스가 정리되었습니다.');
		} catch (error) {
			console.error('[DesktopPushService] 서비스 정리 중 오류:', error);
		}
	}

	return {
		// 기본 서비스 메서드들
		initialize,
		requestPermission,
		getPermissionStatus,
		onNotificationReceived,
		offNotificationReceived,
		showNotification,
		cleanup,

		// Service Worker 관련 메서드들
		registerServiceWorker,
		unregisterServiceWorker,

		// Beams 관련 메서드들
		startBeamsClient,
		registerWithBeams,
		subscribeToInterests,
		unsubscribeFromInterests,
		getSubscribedInterests,
		getRegistrationState,
		getDeviceInfo,
		clearBeamsData,
		clearBeamsClient,

		// 토큰 관련
		getDeviceToken,
		registerDevice,

		// 내부 상태 접근 (디버깅용)
		getBeamsClient: () => state.beamsClient,
		getServiceWorkerRegistration: () => state.serviceWorkerRegistration
	};
}