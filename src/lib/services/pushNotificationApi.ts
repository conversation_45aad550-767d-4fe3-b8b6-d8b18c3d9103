/**
 * Push Notification API 서비스
 *
 * 백엔드에서 알림 발송 및 알림 목록 관리를 위한 API입니다.
 *
 * 주의: 디바이스 등록은 클라이언트에서 Pusher Beams에 직접 처리해야 합니다.
 * 이 API는 알림 발송과 알림 목록 관리만 담당합니다.
 */

import { authClient } from './AxiosBackend';
import type { AxiosResponse } from 'axios';

/**
 * 알림 발송 요청 데이터
 */
export interface SendNotificationRequest {
	/** 수신자 타입 */
	type: 'interest' | 'user' | 'users';
	/** 수신자 목록 (Interest 이름 또는 사용자 ID) */
	targets: string[];
	/** 알림 제목 */
	title: string;
	/** 알림 내용 */
	body: string;
	/** 추가 데이터 (선택사항) */
	data?: Record<string, any>;
	/** 우선순위 */
	priority?: 'low' | 'normal' | 'high';
}

/**
 * 알림 발송 응답 데이터
 */
export interface SendNotificationResponse {
	success: boolean;
	message: string;
	publishId?: string;
}

/**
 * 알림 목록 조회 요청 파라미터
 */
export interface NotificationListRequest {
	/** 사용자 ID */
	userId: string;
	/** 페이지 번호 (선택사항) */
	page?: number;
	/** 페이지당 항목 수 (선택사항) */
	limit?: number;
	/** 읽지 않은 알림만 조회 (선택사항) */
	unreadOnly?: boolean;
}

/**
 * 알림 데이터
 */
export interface NotificationData {
	/** 알림 고유 ID */
	id: string;
	/** 알림 내용 */
	content: string;
	/** 만료일 (ISO 8601 형식) */
	expire_day: string;
	/** 생성 시간 */
	created_at: string;
	/** 읽음 여부 */
	read: boolean;
	/** 우선순위 */
	priority?: 'low' | 'normal' | 'high' | 'urgent';
	/** 카테고리 */
	category?: string;
	/** 액션 URL */
	action_url?: string;
	/** 이미지 URL */
	image_url?: string;
}

/**
 * 알림 목록 조회 응답 데이터
 */
export interface NotificationListResponse {
	/** 조회 성공 여부 */
	success: boolean;
	/** 응답 메시지 */
	message: string;
	/** 알림 목록 */
	notifications: NotificationData[];
	/** 페이지네이션 정보 */
	pagination?: {
		/** 현재 페이지 */
		currentPage: number;
		/** 전체 페이지 수 */
		totalPages: number;
		/** 전체 항목 수 */
		totalItems: number;
		/** 페이지당 항목 수 */
		itemsPerPage: number;
	};
}

/**
 * 알림 읽음 처리 요청 데이터
 */
export interface NotificationReadRequest {
	/** 알림 ID 목록 */
	notificationIds: string[];
	/** 사용자 ID */
	userId: string;
}

/**
 * 알림 읽음 처리 응답 데이터
 */
export interface NotificationReadResponse {
	/** 처리 성공 여부 */
	success: boolean;
	/** 응답 메시지 */
	message: string;
	/** 처리된 알림 ID 목록 */
	processedIds: string[];
}

/**
 * Push Notification API 서비스
 */
export const pushNotificationApi = {
	/**
	 * 알림을 발송합니다 (백엔드에서 Pusher Beams로 발송)
	 */
	async sendNotification(request: SendNotificationRequest): Promise<SendNotificationResponse> {
		const response: AxiosResponse<SendNotificationResponse> = await authClient.post(
			'/api/push-notifications/send',
			request
		);
		return response.data;
	},

	/**
	 * Interest로 알림을 발송합니다
	 */
	async sendToInterest(
		interest: string,
		title: string,
		body: string,
		data?: Record<string, any>
	): Promise<SendNotificationResponse> {
		return this.sendNotification({
			type: 'interest',
			targets: [interest],
			title,
			body,
			data
		});
	},

	/**
	 * 특정 사용자에게 알림을 발송합니다
	 */
	async sendToUser(
		userId: string,
		title: string,
		body: string,
		data?: Record<string, any>
	): Promise<SendNotificationResponse> {
		return this.sendNotification({
			type: 'user',
			targets: [userId],
			title,
			body,
			data
		});
	},

	/**
	 * 여러 사용자에게 알림을 발송합니다
	 */
	async sendToUsers(
		userIds: string[],
		title: string,
		body: string,
		data?: Record<string, any>
	): Promise<SendNotificationResponse> {
		return this.sendNotification({
			type: 'users',
			targets: userIds,
			title,
			body,
			data
		});
	},

	/**
	 * 사용자의 알림 목록을 조회합니다
	 */
	async getNotifications(request: NotificationListRequest): Promise<NotificationListResponse> {
		const params = new URLSearchParams();
		params.append('userId', request.userId);

		if (request.page) params.append('page', request.page.toString());
		if (request.limit) params.append('limit', request.limit.toString());
		if (request.unreadOnly) params.append('unreadOnly', request.unreadOnly.toString());

		const response: AxiosResponse<NotificationListResponse> = await authClient.get(
			`/api/push-notifications/notifications?${params.toString()}`
		);
		return response.data;
	},

	/**
	 * 알림을 읽음 처리합니다
	 */
	async markNotificationsAsRead(
		request: NotificationReadRequest
	): Promise<NotificationReadResponse> {
		const response: AxiosResponse<NotificationReadResponse> = await authClient.post(
			'/api/push-notifications/notifications/mark-read',
			request
		);
		return response.data;
	},

	/**
	 * 단일 알림을 읽음 처리합니다
	 */
	async markNotificationAsRead(
		notificationId: string,
		userId: string
	): Promise<NotificationReadResponse> {
		return this.markNotificationsAsRead({
			notificationIds: [notificationId],
			userId
		});
	}
};

/**
 * 디바이스 등록 헬퍼 함수들
 *
 * 주의: 실제 디바이스 등록은 Pusher Beams SDK를 사용해 클라이언트에서 직접 처리해야 합니다.
 * 이 함수들은 플랫폼 감지 및 디바이스 정보 수집용입니다.
 */
export const deviceRegistrationHelpers = {
	/**
	 * 현재 플랫폼을 감지합니다
	 */
	detectPlatform(): 'android' | 'desktop' | 'web' {
		if (typeof window !== 'undefined' && window.__TAURI__) {
			// Tauri 환경에서 플랫폼 감지
			const userAgent = navigator.userAgent.toLowerCase();
			if (userAgent.includes('android')) {
				return 'android';
			}
			return 'desktop';
		}
		return 'web';
	},

	/**
	 * 디바이스 정보를 수집합니다
	 */
	async getDeviceInfo() {
		const platform = this.detectPlatform();

		const deviceInfo = {
			os: navigator.platform ?? 'unknown',
			appVersion: '1.0.0', // 실제 앱 버전으로 교체 필요
			deviceId: await this.generateDeviceId()
		};

		// Tauri 환경에서 추가 정보 수집
		if (platform !== 'web' && window.__TAURI__) {
			try {
				// Tauri API를 사용하여 시스템 정보 수집 (필요시)
				const osInfo = await window.__TAURI__.invoke('get_os_info');
				deviceInfo.os = osInfo.name;
			} catch (error) {
				console.warn('시스템 정보 수집 실패:', error);
			}
		}

		return deviceInfo;
	},

	/**
	 * 고유한 디바이스 ID를 생성합니다
	 */
	async generateDeviceId(): Promise<string> {
		// 기존에 저장된 디바이스 ID가 있는지 확인
		const existingId = localStorage.getItem('device_id');
		if (existingId) {
			return existingId;
		}

		// 새로운 디바이스 ID 생성
		const deviceId = `device_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
		localStorage.setItem('device_id', deviceId);

		return deviceId;
	},

	/**
	 * 기본 Interest 목록을 가져옵니다
	 */
	getDefaultInterests(): string[] {
		return ['general', 'system', 'updates'];
	}
};
