/**
 * 페이지 복원 디버깅 유틸리티
 * 개발 환경에서 페이지 복원 로직의 동작을 확인하고 테스트하는 도구
 */

import { pageRestore } from '$lib/services/updateService';

/**
 * 페이지 복원 상태 디버그 정보
 */
export interface PageRestoreDebugInfo {
	hasStoredData: boolean;
	storedUrl: string | null;
	storedAt: string | null;
	ageMinutes: number | null;
	isValid: boolean;
	cacheStatus: string;
	logCount: number;
}

/**
 * 페이지 복원 상태 확인
 */
export function getPageRestoreDebugInfo(): PageRestoreDebugInfo {
	const isDev = import.meta.env.VITE_NODE_ENV === 'development';
	
	if (!isDev) {
		return {
			hasStoredData: false,
			storedUrl: null,
			storedAt: null,
			ageMinutes: null,
			isValid: false,
			cacheStatus: 'production_mode',
			logCount: 0
		};
	}

	if (typeof localStorage === 'undefined') {
		return {
			hasStoredData: false,
			storedUrl: null,
			storedAt: null,
			ageMinutes: null,
			isValid: false,
			cacheStatus: 'no_localStorage',
			logCount: 0
		};
	}

	const savedUrl = localStorage.getItem('cnsprowms-last-page');
	const savedAt = localStorage.getItem('cnsprowms-page-saved-at');
	
	let ageMinutes: number | null = null;
	let isValid = false;

	if (savedUrl && savedAt) {
		const savedTime = parseInt(savedAt);
		ageMinutes = (Date.now() - savedTime) / (60 * 1000);
		isValid = ageMinutes <= 30; // 30분 기본 유효 시간
	}

	return {
		hasStoredData: !!(savedUrl && savedAt),
		storedUrl,
		storedAt,
		ageMinutes,
		isValid,
		cacheStatus: 'active',
		logCount: 0 // 실제 로그 카운트는 런타임에서 추적 필요
	};
}

/**
 * 페이지 복원 테스트 데이터 생성
 */
export function createTestPageRestoreData(url: string = '/test-page'): void {
	if (import.meta.env.VITE_NODE_ENV !== 'development') {
		console.warn('테스트 데이터는 개발 환경에서만 생성할 수 있습니다.');
		return;
	}

	pageRestore.save(url);
	console.log('🧪 테스트 페이지 복원 데이터 생성:', url);
}

/**
 * 페이지 복원 캐시 초기화
 */
export function clearPageRestoreCache(): void {
	if (import.meta.env.VITE_NODE_ENV !== 'development') {
		console.warn('캐시 초기화는 개발 환경에서만 가능합니다.');
		return;
	}

	pageRestore.clearCache();
	console.log('🧹 페이지 복원 캐시 초기화 완료');
}

/**
 * 페이지 복원 전체 데이터 삭제
 */
export function clearAllPageRestoreData(): void {
	if (import.meta.env.VITE_NODE_ENV !== 'development') {
		console.warn('데이터 삭제는 개발 환경에서만 가능합니다.');
		return;
	}

	pageRestore.clear();
	pageRestore.clearCache();
	console.log('🗑️ 페이지 복원 전체 데이터 삭제 완료');
}

/**
 * 페이지 복원 상태 출력
 */
export function logPageRestoreStatus(): void {
	if (import.meta.env.VITE_NODE_ENV !== 'development') {
		return;
	}

	const debugInfo = getPageRestoreDebugInfo();
	
	console.group('📋 페이지 복원 상태');
	console.log('저장된 데이터 있음:', debugInfo.hasStoredData);
	console.log('저장된 URL:', debugInfo.storedUrl);
	console.log('저장 시간:', debugInfo.storedAt);
	console.log('경과 시간(분):', debugInfo.ageMinutes?.toFixed(1));
	console.log('유효성:', debugInfo.isValid);
	console.log('캐시 상태:', debugInfo.cacheStatus);
	console.groupEnd();
}

/**
 * 개발 환경에서 전역 디버그 함수 등록
 */
export function registerPageRestoreDebugFunctions(): void {
	if (import.meta.env.VITE_NODE_ENV !== 'development' || typeof window === 'undefined') {
		return;
	}

	// 전역 객체에 디버그 함수들 등록
	(window as any).pageRestoreDebug = {
		getInfo: getPageRestoreDebugInfo,
		createTestData: createTestPageRestoreData,
		clearCache: clearPageRestoreCache,
		clearAll: clearAllPageRestoreData,
		logStatus: logPageRestoreStatus,
		
		// 직접 pageRestore 함수들에 접근
		shouldRestore: () => pageRestore.shouldRestore(),
		get: (maxAge?: number) => pageRestore.get(maxAge),
		save: (url: string) => pageRestore.save(url),
		clear: () => pageRestore.clear()
	};

	console.log('🔧 페이지 복원 디버그 함수 등록 완료:');
	console.log('- window.pageRestoreDebug.getInfo()');
	console.log('- window.pageRestoreDebug.createTestData(url)');
	console.log('- window.pageRestoreDebug.clearCache()');
	console.log('- window.pageRestoreDebug.clearAll()');
	console.log('- window.pageRestoreDebug.logStatus()');
}
