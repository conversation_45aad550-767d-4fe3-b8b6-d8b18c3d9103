/**
 * Pusher Beams 디버깅 유틸리티
 * 개발 환경에서 Pusher Beams 초기화 순서와 상태를 확인하는 도구
 */

import { getPushServiceManager } from '$lib/services/pushServiceManager';
import { createDesktopPushService } from '$lib/services/desktopPushService';

/**
 * Pusher Beams 상태 정보
 */
export interface PusherBeamsDebugInfo {
	managerStatus: any;
	desktopServiceStatus: any;
	initializationOrder: string[];
	errors: string[];
	recommendations: string[];
}

/**
 * Pusher Beams 상태 확인
 */
export async function getPusherBeamsDebugInfo(): Promise<PusherBeamsDebugInfo> {
	const debugInfo: PusherBeamsDebugInfo = {
		managerStatus: null,
		desktopServiceStatus: null,
		initializationOrder: [],
		errors: [],
		recommendations: []
	};

	try {
		// Push Service Manager 상태 확인
		const manager = getPushServiceManager();
		if (manager) {
			debugInfo.managerStatus = await manager.getServiceStatus();
		} else {
			debugInfo.errors.push('Push Service Manager가 초기화되지 않았습니다.');
		}

		// Desktop Service 직접 상태 확인 (테스트용)
		try {
			const desktopService = createDesktopPushService();
			debugInfo.desktopServiceStatus = desktopService.getServiceStatus();
		} catch (error) {
			debugInfo.errors.push(`Desktop Service 상태 확인 실패: ${error}`);
		}

		// 초기화 순서 분석
		analyzeInitializationOrder(debugInfo);

		// 권장사항 생성
		generateRecommendations(debugInfo);

	} catch (error) {
		debugInfo.errors.push(`디버그 정보 수집 실패: ${error}`);
	}

	return debugInfo;
}

/**
 * 초기화 순서 분석
 */
function analyzeInitializationOrder(debugInfo: PusherBeamsDebugInfo): void {
	const order = [];

	// Manager 상태 기반 순서 분석
	if (debugInfo.managerStatus) {
		if (debugInfo.managerStatus.isInitialized) {
			order.push('✅ Push Service Manager 초기화됨');
		} else {
			order.push('❌ Push Service Manager 초기화 안됨');
		}

		if (debugInfo.managerStatus.registrationStatus === 'completed') {
			order.push('✅ 디바이스 등록 완료');
		} else {
			order.push(`❌ 디바이스 등록 상태: ${debugInfo.managerStatus.registrationStatus}`);
		}

		if (debugInfo.managerStatus.deviceInfo?.userId) {
			order.push('✅ 사용자 ID 연결됨');
		} else {
			order.push('❌ 사용자 ID 연결 안됨');
		}
	}

	// Desktop Service 상태 기반 순서 분석
	if (debugInfo.desktopServiceStatus) {
		if (debugInfo.desktopServiceStatus.isBeamsStarted) {
			order.push('✅ Pusher Beams .start() 호출됨');
		} else {
			order.push('❌ Pusher Beams .start() 호출 안됨');
		}

		if (debugInfo.desktopServiceStatus.hasBeamsClient) {
			order.push('✅ Pusher Beams 클라이언트 생성됨');
		} else {
			order.push('❌ Pusher Beams 클라이언트 생성 안됨');
		}
	}

	debugInfo.initializationOrder = order;
}

/**
 * 권장사항 생성
 */
function generateRecommendations(debugInfo: PusherBeamsDebugInfo): void {
	const recommendations = [];

	// .start() 호출 관련 권장사항
	if (debugInfo.desktopServiceStatus && !debugInfo.desktopServiceStatus.isBeamsStarted) {
		recommendations.push('Pusher Beams .start() 메서드를 먼저 호출해야 합니다.');
		recommendations.push('registerDevice() 메서드를 호출하여 .start()를 실행하세요.');
	}

	// 사용자 ID 연결 관련 권장사항
	if (debugInfo.managerStatus && !debugInfo.managerStatus.deviceInfo?.userId) {
		recommendations.push('사용자 로그인 후 pushServiceManager.onUserLogin()을 호출하세요.');
	}

	// 초기화 순서 관련 권장사항
	if (debugInfo.managerStatus && debugInfo.managerStatus.registrationStatus !== 'completed') {
		recommendations.push('디바이스 등록을 먼저 완료한 후 사용자 ID를 연결하세요.');
	}

	// 에러 관련 권장사항
	if (debugInfo.errors.length > 0) {
		recommendations.push('브라우저 콘솔에서 에러 로그를 확인하세요.');
		recommendations.push('환경 변수 VITE_PUSHER_BEAMS_INSTANCE_ID가 설정되어 있는지 확인하세요.');
	}

	debugInfo.recommendations = recommendations;
}

/**
 * Pusher Beams 상태 출력
 */
export async function logPusherBeamsStatus(): Promise<void> {
	if (import.meta.env.VITE_NODE_ENV !== 'development') {
		return;
	}

	const debugInfo = await getPusherBeamsDebugInfo();
	
	console.group('🔔 Pusher Beams 상태');
	
	console.group('📊 Manager 상태');
	console.log(debugInfo.managerStatus);
	console.groupEnd();
	
	console.group('🖥️ Desktop Service 상태');
	console.log(debugInfo.desktopServiceStatus);
	console.groupEnd();
	
	console.group('🔄 초기화 순서');
	debugInfo.initializationOrder.forEach(step => console.log(step));
	console.groupEnd();
	
	if (debugInfo.errors.length > 0) {
		console.group('❌ 에러');
		debugInfo.errors.forEach(error => console.error(error));
		console.groupEnd();
	}
	
	if (debugInfo.recommendations.length > 0) {
		console.group('💡 권장사항');
		debugInfo.recommendations.forEach(rec => console.log(rec));
		console.groupEnd();
	}
	
	console.groupEnd();
}

/**
 * Pusher Beams 초기화 순서 테스트
 */
export async function testPusherBeamsInitOrder(): Promise<void> {
	if (import.meta.env.VITE_NODE_ENV !== 'development') {
		console.warn('테스트는 개발 환경에서만 실행할 수 있습니다.');
		return;
	}

	console.log('🧪 Pusher Beams 초기화 순서 테스트 시작');

	try {
		// 1. Manager 초기화
		const manager = getPushServiceManager();
		await manager.initialize();
		console.log('✅ 1. Push Service Manager 초기화 완료');

		// 2. 디바이스 등록
		await manager.registerDevice();
		console.log('✅ 2. 디바이스 등록 완료');

		// 3. 사용자 로그인 처리
		await manager.onUserLogin();
		console.log('✅ 3. 사용자 로그인 처리 완료');

		// 4. 상태 확인
		const status = await manager.getServiceStatus();
		console.log('📊 최종 상태:', status);

	} catch (error) {
		console.error('❌ 테스트 실패:', error);
	}
}

/**
 * 개발 환경에서 전역 디버그 함수 등록
 */
export function registerPusherBeamsDebugFunctions(): void {
	if (import.meta.env.VITE_NODE_ENV !== 'development' || typeof window === 'undefined') {
		return;
	}

	// 전역 객체에 디버그 함수들 등록
	(window as any).pusherBeamsDebug = {
		getInfo: getPusherBeamsDebugInfo,
		logStatus: logPusherBeamsStatus,
		testInitOrder: testPusherBeamsInitOrder,
		
		// 직접 서비스 접근
		getManager: () => getPushServiceManager(),
		createDesktopService: () => createDesktopPushService()
	};

	console.log('🔧 Pusher Beams 디버그 함수 등록 완료:');
	console.log('- window.pusherBeamsDebug.getInfo()');
	console.log('- window.pusherBeamsDebug.logStatus()');
	console.log('- window.pusherBeamsDebug.testInitOrder()');
}
