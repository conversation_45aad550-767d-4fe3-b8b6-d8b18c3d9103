/**
 * 알림 성능 최적화 유틸리티
 * 기존 IndexedDB 최적화 기능들을 활용하여 성능 향상
 * Requirements: 6.1.3, 6.2.3
 */

import {
	initEmployeeDB,
	getDataByIndex,
	getDataByRange,
	getAllData,
	saveData,
	deleteData
} from './indexeddb-utils';
import type { NotificationData } from '$lib/types/notification';

const NOTIFICATION_STORE = 'push_notifications';

/**
 * 배치 처리를 위한 알림 캐시 관리자
 * 기존 IndexedDB 인덱스 구조를 활용한 빠른 조회
 */
export const notificationCache = {
	// 캐시된 데이터
	_cache: new Map<string, NotificationData[]>(),
	_lastUpdate: new Map<string, number>(),
	_cacheTimeout: 5 * 60 * 1000, // 5분 캐시 유지

	/**
	 * 캐시 키 생성
	 */
	_getCacheKey(type: string, params?: any): string {
		if (params) {
			return `${type}_${JSON.stringify(params)}`;
		}
		return type;
	},

	/**
	 * 캐시 유효성 확인
	 */
	_isCacheValid(key: string): boolean {
		const lastUpdate = this._lastUpdate.get(key);
		if (!lastUpdate) return false;
		return Date.now() - lastUpdate < this._cacheTimeout;
	},

	/**
	 * 캐시에서 데이터 조회
	 */
	_getFromCache(key: string): NotificationData[] | null {
		if (this._isCacheValid(key)) {
			return this._cache.get(key) || null;
		}
		return null;
	},

	/**
	 * 캐시에 데이터 저장
	 */
	_setCache(key: string, data: NotificationData[]): void {
		this._cache.set(key, data);
		this._lastUpdate.set(key, Date.now());
	},

	/**
	 * 캐시 무효화
	 */
	invalidateCache(pattern?: string): void {
		if (pattern) {
			// 특정 패턴의 캐시만 무효화
			for (const key of this._cache.keys()) {
				if (key.includes(pattern)) {
					this._cache.delete(key);
					this._lastUpdate.delete(key);
				}
			}
		} else {
			// 전체 캐시 무효화
			this._cache.clear();
			this._lastUpdate.clear();
		}
	},

	/**
	 * 활성 알림 캐시 조회 (기존 getActiveNotifications 성능 활용)
	 */
	async getActiveNotifications(): Promise<NotificationData[]> {
		const cacheKey = this._getCacheKey('active');
		const cached = this._getFromCache(cacheKey);
		if (cached) {
			return cached;
		}

		const db = await initEmployeeDB();
		const allNotifications = await getAllData<NotificationData>(db, NOTIFICATION_STORE);
		const now = new Date();

		const activeNotifications = allNotifications
			.filter((notification) => {
				if (!notification.expire_day) {
					// 만료일이 없으면 6개월간 표시
					const sixMonthsAgo = new Date();
					sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);
					return new Date(notification.created_at) > sixMonthsAgo;
				}
				return new Date(notification.expire_day) > now;
			})
			.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());

		this._setCache(cacheKey, activeNotifications);
		return activeNotifications;
	},

	/**
	 * 우선순위별 알림 캐시 조회 (인덱스 오류 해결)
	 */
	async getNotificationsByPriority(
		priority: 'low' | 'normal' | 'high' | 'urgent'
	): Promise<NotificationData[]> {
		const cacheKey = this._getCacheKey('priority', priority);
		const cached = this._getFromCache(cacheKey);
		if (cached) {
			return cached;
		}

		const db = await initEmployeeDB();

		try {
			// 우선순위 인덱스를 활용한 빠른 조회 시도
			const notifications = await getDataByIndex<NotificationData>(
				db,
				NOTIFICATION_STORE,
				'priority',
				priority
			);

			// priority가 null/undefined인 경우 'normal'로 처리
			if (priority === 'normal') {
				try {
					const nullPriorityNotifications = await getDataByIndex<NotificationData>(
						db,
						NOTIFICATION_STORE,
						'priority',
						undefined
					);
					notifications.push(...nullPriorityNotifications);
				} catch (error) {
					console.warn('[NotificationCache] null priority 조회 실패 (무시됨):', error);
				}
			}

			// 만료되지 않은 알림만 필터링하고 최신순 정렬
			const now = new Date();
			const result = notifications
				.filter((notification) => {
					if (!notification.expire_day) {
						const sixMonthsAgo = new Date();
						sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);
						return new Date(notification.created_at) > sixMonthsAgo;
					}
					return new Date(notification.expire_day) > now;
				})
				.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());

			this._setCache(cacheKey, result);
			return result;
		} catch (error) {
			console.warn('[NotificationCache] 인덱스 조회 실패, 전체 조회로 폴백:', error);

			// 폴백: 전체 데이터를 가져와서 메모리에서 필터링
			const allNotifications = await getAllData<NotificationData>(db, NOTIFICATION_STORE);

			// 우선순위로 필터링
			const filteredNotifications = allNotifications.filter((notification) => {
				const notificationPriority = notification.priority || 'normal';
				return notificationPriority === priority;
			});

			// 만료되지 않은 알림만 필터링하고 최신순 정렬
			const now = new Date();
			const result = filteredNotifications
				.filter((notification) => {
					if (!notification.expire_day) {
						const sixMonthsAgo = new Date();
						sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);
						return new Date(notification.created_at) > sixMonthsAgo;
					}
					return new Date(notification.expire_day) > now;
				})
				.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());

			this._setCache(cacheKey, result);
			return result;
		}
	},

	/**
	 * 읽지 않은 알림 개수 캐시 조회 (boolean 인덱스 문제 해결)
	 */
	async getUnreadCount(): Promise<number> {
		const cacheKey = this._getCacheKey('unread_count');
		const cached = this._getFromCache(cacheKey);
		if (cached && cached.length > 0) {
			return cached[0] as any; // 숫자를 배열로 저장했으므로 변환
		}

		const db = await initEmployeeDB();

		// 모든 알림을 가져와서 필터링하는 방식으로 변경 (boolean 인덱스 문제 해결)
		const allNotifications = await getAllData<NotificationData>(db, NOTIFICATION_STORE);
		const unreadNotifications = allNotifications.filter((notification) => !notification.read);

		// 만료되지 않은 알림만 카운트
		const now = new Date();
		const count = unreadNotifications.filter((notification) => {
			if (!notification.expire_day) {
				const sixMonthsAgo = new Date();
				sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);
				return new Date(notification.created_at) > sixMonthsAgo;
			}
			return new Date(notification.expire_day) > now;
		}).length;

		// 숫자를 배열로 저장 (캐시 구조 통일)
		this._setCache(cacheKey, [count as any]);
		return count;
	}
};

/**
 * 배치 처리 최적화 함수들
 * 기존 배치 처리 로직을 활용하여 성능 향상
 */
export const batchProcessor = {
	/**
	 * 배치 크기 설정
	 */
	_batchSize: 50,

	/**
	 * 배치 크기 설정
	 */
	setBatchSize(size: number): void {
		this._batchSize = Math.max(1, Math.min(size, 1000)); // 1-1000 범위로 제한
	},

	/**
	 * 배치 단위로 알림 읽음 처리
	 * 기존 markNotificationAsRead 함수를 배치로 활용
	 */
	async markNotificationsAsReadBatch(notificationIds: string[]): Promise<number> {
		const db = await initEmployeeDB();
		let processedCount = 0;

		// 배치 단위로 처리
		for (let i = 0; i < notificationIds.length; i += this._batchSize) {
			const batch = notificationIds.slice(i, i + this._batchSize);
			const transaction = db.transaction([NOTIFICATION_STORE], 'readwrite');
			const store = transaction.objectStore(NOTIFICATION_STORE);

			// 트랜잭션 내에서 배치 처리
			const promises = batch.map(async (id) => {
				try {
					const notification = await new Promise<NotificationData | null>((resolve, reject) => {
						const request = store.get(id);
						request.onsuccess = () => resolve(request.result || null);
						request.onerror = () => reject(request.error);
					});

					if (notification && !notification.read) {
						const updatedNotification = { ...notification, read: true };
						await new Promise<void>((resolve, reject) => {
							const request = store.put(updatedNotification);
							request.onsuccess = () => resolve();
							request.onerror = () => reject(request.error);
						});
						processedCount++;
					}
				} catch (error) {
					console.error(`알림 읽음 처리 실패 (${id}):`, error);
				}
			});

			await Promise.all(promises);

			// 트랜잭션 완료 대기
			await new Promise<void>((resolve, reject) => {
				transaction.oncomplete = () => resolve();
				transaction.onerror = () => reject(transaction.error);
			});
		}

		// 캐시 무효화
		notificationCache.invalidateCache();

		console.log(`배치 읽음 처리 완료: ${processedCount}개`);
		return processedCount;
	},

	/**
	 * 배치 단위로 알림 삭제
	 * 기존 deleteNotification 함수를 배치로 활용
	 */
	async deleteNotificationsBatch(notificationIds: string[]): Promise<number> {
		const db = await initEmployeeDB();
		let deletedCount = 0;

		// 배치 단위로 처리
		for (let i = 0; i < notificationIds.length; i += this._batchSize) {
			const batch = notificationIds.slice(i, i + this._batchSize);
			const transaction = db.transaction([NOTIFICATION_STORE], 'readwrite');
			const store = transaction.objectStore(NOTIFICATION_STORE);

			// 트랜잭션 내에서 배치 처리
			const promises = batch.map(async (id) => {
				try {
					await new Promise<void>((resolve, reject) => {
						const request = store.delete(id);
						request.onsuccess = () => resolve();
						request.onerror = () => reject(request.error);
					});
					deletedCount++;
				} catch (error) {
					console.error(`알림 삭제 실패 (${id}):`, error);
				}
			});

			await Promise.all(promises);

			// 트랜잭션 완료 대기
			await new Promise<void>((resolve, reject) => {
				transaction.oncomplete = () => resolve();
				transaction.onerror = () => reject(transaction.error);
			});
		}

		// 캐시 무효화
		notificationCache.invalidateCache();

		console.log(`배치 삭제 완료: ${deletedCount}개`);
		return deletedCount;
	},

	/**
	 * 만료된 알림 배치 삭제 (기존 deleteExpiredNotifications 최적화)
	 */
	async deleteExpiredNotificationsBatch(): Promise<number> {
		const db = await initEmployeeDB();
		const now = new Date();

		// expire_day 인덱스를 활용하여 만료된 알림 조회
		const expiredRange = IDBKeyRange.upperBound(now.toISOString().split('T')[0], false);
		const expiredNotifications = await getDataByRange<NotificationData>(
			db,
			NOTIFICATION_STORE,
			'expire_day',
			expiredRange
		);

		// 만료일이 없는 6개월 이상 된 알림도 조회
		const allNotifications = await getAllData<NotificationData>(db, NOTIFICATION_STORE);
		const sixMonthsAgo = new Date();
		sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);

		const oldNotifications = allNotifications.filter(
			(notification) =>
				!notification.expire_day && new Date(notification.created_at) <= sixMonthsAgo
		);

		// 삭제할 알림 ID 목록 생성
		const toDeleteIds = [
			...expiredNotifications.map((n) => n.id),
			...oldNotifications.map((n) => n.id)
		];

		if (toDeleteIds.length === 0) {
			return 0;
		}

		// 배치 삭제 실행
		return await this.deleteNotificationsBatch(toDeleteIds);
	}
};

/**
 * 인덱스 최적화 조회 함수들
 * 기존 인덱스 구조를 활용한 빠른 조회 성능 제공
 */
export const indexOptimizer = {
	/**
	 * 복합 조건 조회 최적화 (인덱스 오류 해결)
	 * 여러 인덱스를 조합하여 최적의 성능 제공
	 */
	async getNotificationsByMultipleConditions(conditions: {
		priority?: 'low' | 'normal' | 'high' | 'urgent';
		category?: string;
		read?: boolean;
		dateRange?: { start: Date; end: Date };
	}): Promise<NotificationData[]> {
		const db = await initEmployeeDB();
		let results: NotificationData[] = [];

		try {
			// 가장 선택적인 조건부터 적용하여 성능 최적화
			if (conditions.dateRange) {
				// 날짜 범위가 가장 선택적이므로 먼저 적용
				const range = IDBKeyRange.bound(
					conditions.dateRange.start.toISOString(),
					conditions.dateRange.end.toISOString(),
					false,
					false
				);
				results = await getDataByRange<NotificationData>(
					db,
					NOTIFICATION_STORE,
					'created_at',
					range
				);
			} else if (conditions.priority) {
				// 우선순위 인덱스 활용
				results = await getDataByIndex<NotificationData>(
					db,
					NOTIFICATION_STORE,
					'priority',
					conditions.priority
				);
			} else if (conditions.category) {
				// 카테고리 인덱스 활용
				results = await getDataByIndex<NotificationData>(
					db,
					NOTIFICATION_STORE,
					'category',
					conditions.category
				);
			} else if (conditions.read !== undefined) {
				// 읽음 상태 인덱스 활용
				results = await getDataByIndex<NotificationData>(
					db,
					NOTIFICATION_STORE,
					'read',
					conditions.read
				);
			} else {
				// 조건이 없으면 전체 조회
				results = await getAllData<NotificationData>(db, NOTIFICATION_STORE);
			}
		} catch (error) {
			console.warn('[IndexOptimizer] 인덱스 조회 실패, 전체 조회로 폴백:', error);
			// 폴백: 전체 데이터를 가져와서 메모리에서 필터링
			results = await getAllData<NotificationData>(db, NOTIFICATION_STORE);
		}

		// 나머지 조건들을 메모리에서 필터링
		let filteredResults = results;

		if (conditions.priority) {
			filteredResults = filteredResults.filter((n) => {
				const priority = n.priority || 'normal';
				return priority === conditions.priority;
			});
		}

		if (conditions.category) {
			filteredResults = filteredResults.filter((n) => n.category === conditions.category);
		}

		if (conditions.read !== undefined) {
			filteredResults = filteredResults.filter((n) => n.read === conditions.read);
		}

		if (conditions.dateRange) {
			filteredResults = filteredResults.filter((n) => {
				const createdAt = new Date(n.created_at);
				return createdAt >= conditions.dateRange!.start && createdAt <= conditions.dateRange!.end;
			});
		}

		// 만료되지 않은 알림만 필터링
		const now = new Date();
		filteredResults = filteredResults.filter((notification) => {
			if (!notification.expire_day) {
				const sixMonthsAgo = new Date();
				sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);
				return new Date(notification.created_at) > sixMonthsAgo;
			}
			return new Date(notification.expire_day) > now;
		});

		// 최신순 정렬
		return filteredResults.sort(
			(a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
		);
	},

	/**
	 * 페이지네이션 지원 조회
	 * 대용량 데이터 처리를 위한 페이지네이션
	 */
	async getNotificationsPaginated(
		page: number = 1,
		pageSize: number = 20,
		conditions?: {
			priority?: 'low' | 'normal' | 'high' | 'urgent';
			category?: string;
			read?: boolean;
		}
	): Promise<{ notifications: NotificationData[]; totalCount: number; hasMore: boolean }> {
		// 전체 조건에 맞는 알림 조회
		const allNotifications = conditions
			? await this.getNotificationsByMultipleConditions(conditions)
			: await notificationCache.getActiveNotifications();

		const totalCount = allNotifications.length;
		const startIndex = (page - 1) * pageSize;
		const endIndex = startIndex + pageSize;

		const notifications = allNotifications.slice(startIndex, endIndex);
		const hasMore = endIndex < totalCount;

		return {
			notifications,
			totalCount,
			hasMore
		};
	},

	/**
	 * 통계 정보 최적화 조회
	 * 인덱스를 활용한 빠른 통계 계산
	 */
	async getNotificationStats(): Promise<{
		total: number;
		unread: number;
		byPriority: Record<string, number>;
		byCategory: Record<string, number>;
	}> {
		const db = await initEmployeeDB();

		// 병렬로 통계 조회
		const [totalNotifications, allNotifications] = await Promise.all([
			notificationCache.getActiveNotifications(),
			getAllData<NotificationData>(db, NOTIFICATION_STORE)
		]);

		// 읽지 않은 알림 필터링 (boolean 인덱스 문제 해결)
		const unreadNotifications = allNotifications.filter((notification) => !notification.read);

		// 만료되지 않은 읽지 않은 알림만 카운트
		const now = new Date();
		const activeUnreadCount = unreadNotifications.filter((notification) => {
			if (!notification.expire_day) {
				const sixMonthsAgo = new Date();
				sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);
				return new Date(notification.created_at) > sixMonthsAgo;
			}
			return new Date(notification.expire_day) > now;
		}).length;

		// 우선순위별 통계
		const byPriority: Record<string, number> = {
			urgent: 0,
			high: 0,
			normal: 0,
			low: 0
		};

		// 카테고리별 통계
		const byCategory: Record<string, number> = {};

		totalNotifications.forEach((notification) => {
			// 우선순위별 카운트
			const priority = notification.priority || 'normal';
			byPriority[priority] = (byPriority[priority] || 0) + 1;

			// 카테고리별 카운트
			const category = notification.category || 'uncategorized';
			byCategory[category] = (byCategory[category] || 0) + 1;
		});

		return {
			total: totalNotifications.length,
			unread: activeUnreadCount,
			byPriority,
			byCategory
		};
	}
};

/**
 * 성능 모니터링 유틸리티
 */
export const performanceMonitor = {
	/**
	 * 함수 실행 시간 측정
	 */
	async measureExecutionTime<T>(
		name: string,
		fn: () => Promise<T>
	): Promise<{ result: T; executionTime: number }> {
		const startTime = performance.now();
		const result = await fn();
		const endTime = performance.now();
		const executionTime = endTime - startTime;

		console.log(`[성능] ${name}: ${executionTime.toFixed(2)}ms`);
		return { result, executionTime };
	},

	/**
	 * 캐시 히트율 모니터링
	 */
	getCacheStats(): {
		cacheSize: number;
		cacheKeys: string[];
		oldestCache: string | null;
		newestCache: string | null;
	} {
		const cacheKeys = Array.from(notificationCache._cache.keys());
		const timestamps = Array.from(notificationCache._lastUpdate.entries());

		let oldestCache: string | null = null;
		let newestCache: string | null = null;
		let oldestTime = Infinity;
		let newestTime = 0;

		timestamps.forEach(([key, time]) => {
			if (time < oldestTime) {
				oldestTime = time;
				oldestCache = key;
			}
			if (time > newestTime) {
				newestTime = time;
				newestCache = key;
			}
		});

		return {
			cacheSize: cacheKeys.length,
			cacheKeys,
			oldestCache,
			newestCache
		};
	}
};

/**
 * 통합 성능 최적화 매니저
 * 모든 최적화 기능을 통합 관리
 */
export const performanceManager = {
	cache: notificationCache,
	batch: batchProcessor,
	index: indexOptimizer,
	monitor: performanceMonitor,

	/**
	 * 성능 최적화 초기화
	 */
	async initialize(): Promise<void> {
		console.log('알림 성능 최적화 초기화 시작');

		// 캐시 워밍업 (자주 사용되는 데이터 미리 로드)
		await Promise.all([
			this.cache.getActiveNotifications(),
			this.cache.getUnreadCount(),
			this.cache.getNotificationsByPriority('urgent'),
			this.cache.getNotificationsByPriority('high')
		]);

		console.log('알림 성능 최적화 초기화 완료');
	},

	/**
	 * 정기적인 성능 최적화 작업
	 */
	async performMaintenance(): Promise<void> {
		console.log('알림 성능 최적화 유지보수 시작');

		const { result: deletedCount } = await this.monitor.measureExecutionTime(
			'만료된 알림 배치 삭제',
			() => this.batch.deleteExpiredNotificationsBatch()
		);

		// 캐시 정리 (오래된 캐시 제거)
		const cacheStats = this.monitor.getCacheStats();
		if (cacheStats.cacheSize > 20) {
			// 캐시가 너무 많으면 일부 정리
			this.cache.invalidateCache();
		}

		console.log(`알림 성능 최적화 유지보수 완료 (삭제된 알림: ${deletedCount}개)`);
	}
};
