/**
 * IndexedDB 공통 유틸리티 함수들
 * 프린터 설정과 알림 데이터베이스에서 공통으로 사용
 */

export interface DatabaseConfig {
	dbName: string;
	version: number;
	stores: StoreConfig[];
}

export interface StoreConfig {
	name: string;
	keyPath: string;
	indexes?: IndexConfig[];
	initialData?: any[];
}

export interface IndexConfig {
	name: string;
	keyPath: string;
	options?: IDBIndexParameters;
}

/**
 * IndexedDB 데이터베이스 초기화
 */
export function initIndexedDB(config: DatabaseConfig): Promise<IDBDatabase> {
	return new Promise((resolve, reject) => {
		const request = indexedDB.open(config.dbName, config.version);
		console.log(`IndexedDB init - ${config.dbName} version:`, config.version);

		request.onerror = () => {
			console.error(`IndexedDB 에러 (${config.dbName}):`, request.error);
			reject(request.error);
		};

		request.onsuccess = () => {
			console.log(`IndexedDB success - ${config.dbName}`);
			resolve(request.result);
		};

		request.onupgradeneeded = (event: IDBVersionChangeEvent) => {
			console.log(`IndexedDB upgrade needed - ${config.dbName}`);
			const db = (event.target as IDBOpenDBRequest).result;
			const oldVersion = event.oldVersion;
			const transaction = (event.target as IDBOpenDBRequest).transaction!;

			// 각 스토어 설정에 따라 생성 또는 업그레이드
			config.stores.forEach((storeConfig) => {
				let store: IDBObjectStore;

				// 스토어가 이미 존재하는지 확인
				if (!db.objectStoreNames.contains(storeConfig.name)) {
					// 새 스토어 생성
					store = db.createObjectStore(storeConfig.name, {
						keyPath: storeConfig.keyPath
					});

					// 인덱스 생성
					if (storeConfig.indexes) {
						storeConfig.indexes.forEach((indexConfig) => {
							store.createIndex(indexConfig.name, indexConfig.keyPath, indexConfig.options);
						});
					}

					// 초기 데이터 추가
					if (storeConfig.initialData) {
						storeConfig.initialData.forEach((data) => {
							store.add(data);
						});
					}

					console.log(`스토어 생성 완료: ${storeConfig.name}`);
				} else {
					// 기존 스토어 업그레이드
					store = transaction.objectStore(storeConfig.name);

					// 알림 스토어의 경우 인덱스 재구성 (모든 버전에서 확인)
					if (storeConfig.name === 'push_notifications') {
						console.log('알림 스토어 인덱스 확인 및 업그레이드 시작');

						// 기존 type 관련 인덱스 제거 (있다면)
						try {
							if (store.indexNames.contains('type')) {
								store.deleteIndex('type');
								console.log('type 인덱스 제거 완료');
							}
							if (store.indexNames.contains('target_user_id')) {
								store.deleteIndex('target_user_id');
								console.log('target_user_id 인덱스 제거 완료');
							}
						} catch (error) {
							console.warn('기존 인덱스 제거 중 오류 (무시됨):', error);
						}

						// 필요한 인덱스들을 강제로 재생성
						if (storeConfig.indexes) {
							storeConfig.indexes.forEach((indexConfig) => {
								try {
									// 기존 인덱스가 있으면 제거 후 재생성
									if (store.indexNames.contains(indexConfig.name)) {
										console.log(`기존 인덱스 제거 후 재생성: ${indexConfig.name}`);
										store.deleteIndex(indexConfig.name);
									}

									// 새 인덱스 생성
									store.createIndex(indexConfig.name, indexConfig.keyPath, indexConfig.options);
									console.log(
										`인덱스 생성 완료: ${indexConfig.name} (keyPath: ${indexConfig.keyPath})`
									);
								} catch (error) {
									console.warn(`인덱스 생성 실패 (${indexConfig.name}):`, error);
								}
							});
						}

						console.log('알림 스토어 인덱스 업그레이드 완료');
						console.log('현재 인덱스 목록:', Array.from(store.indexNames));
					}
				}
			});
		};
	});
}

/**
 * 데이터 저장 (PUT 방식)
 */
export function saveData<T>(db: IDBDatabase, storeName: string, data: T): Promise<void> {
	return new Promise((resolve, reject) => {
		const transaction = db.transaction([storeName], 'readwrite');
		const store = transaction.objectStore(storeName);

		// 안전한 데이터 복사 (순환 참조 방지)
		const safeData = JSON.parse(JSON.stringify(data));
		const request = store.put(safeData);

		request.onsuccess = () => resolve();
		request.onerror = () => reject(request.error);
	});
}

/**
 * 데이터 조회 (단일)
 */
export function getData<T>(
	db: IDBDatabase,
	storeName: string,
	key: string | number
): Promise<T | null> {
	return new Promise((resolve, reject) => {
		const transaction = db.transaction([storeName], 'readonly');
		const store = transaction.objectStore(storeName);
		const request = store.get(key);

		request.onsuccess = () => {
			resolve(request.result || null);
		};
		request.onerror = () => reject(request.error);
	});
}

/**
 * 모든 데이터 조회
 */
export function getAllData<T>(db: IDBDatabase, storeName: string): Promise<T[]> {
	return new Promise((resolve, reject) => {
		const transaction = db.transaction([storeName], 'readonly');
		const store = transaction.objectStore(storeName);
		const request = store.getAll();

		request.onsuccess = () => {
			resolve(request.result as T[]);
		};
		request.onerror = () => reject(request.error);
	});
}

/**
 * 모든 키 조회
 */
export function getAllKeys(db: IDBDatabase, storeName: string): Promise<(string | number)[]> {
	return new Promise((resolve, reject) => {
		const transaction = db.transaction([storeName], 'readonly');
		const store = transaction.objectStore(storeName);
		const request = store.getAllKeys();

		request.onsuccess = () => {
			resolve(request.result as (string | number)[]);
		};
		request.onerror = () => reject(request.error);
	});
}

/**
 * 데이터 삭제
 */
export function deleteData(
	db: IDBDatabase,
	storeName: string,
	key: string | number
): Promise<void> {
	return new Promise((resolve, reject) => {
		const transaction = db.transaction([storeName], 'readwrite');
		const store = transaction.objectStore(storeName);
		const request = store.delete(key);

		request.onsuccess = () => resolve();
		request.onerror = () => reject(request.error);
	});
}

/**
 * 인덱스를 사용한 데이터 조회
 */
export function getDataByIndex<T>(
	db: IDBDatabase,
	storeName: string,
	indexName: string,
	value: any
): Promise<T[]> {
	return new Promise((resolve, reject) => {
		const transaction = db.transaction([storeName], 'readonly');
		const store = transaction.objectStore(storeName);
		const index = store.index(indexName);
		const request = index.getAll(value);

		request.onsuccess = () => {
			resolve(request.result as T[]);
		};
		request.onerror = () => reject(request.error);
	});
}

/**
 * 범위 조건으로 데이터 조회
 */
export function getDataByRange<T>(
	db: IDBDatabase,
	storeName: string,
	indexName: string,
	range: IDBKeyRange
): Promise<T[]> {
	return new Promise((resolve, reject) => {
		const transaction = db.transaction([storeName], 'readonly');
		const store = transaction.objectStore(storeName);
		const index = store.index(indexName);
		const request = index.getAll(range);

		request.onsuccess = () => {
			resolve(request.result as T[]);
		};
		request.onerror = () => reject(request.error);
	});
}

/**
 * 데이터베이스 연결 종료
 */
export function closeDatabase(db: IDBDatabase | null): void {
	if (db) {
		db.close();
		console.log('IndexedDB 연결 종료');
	}
}

/**
 * EmployeeDB 통합 설정
 * 프린터 설정과 알림 데이터를 모두 포함하는 통합 데이터베이스 설정
 */
export const EMPLOYEE_DB_CONFIG: DatabaseConfig = {
	dbName: 'EmployeeDB',
	version: 4,
	stores: [
		// 프린터 설정 스토어
		{
			name: 'print_settings',
			keyPath: 'settingName',
			initialData: [
				{
					settingName: '기본(default)',
					settings: [
						{
							name: '폰트(글꼴)',
							code: 'font',
							settings: { fontFamily: 'Roboto' }
						},
						{
							name: '바코드',
							code: 'barcode',
							settings: { x: 0, y: 3 }
						},
						{
							name: 'QAID',
							code: 'qaid',
							settings: { fontSize: 10 }
						},
						{
							name: '출력일 형식',
							code: 'date',
							settings: {
								x: 71,
								y: 67,
								format: 'MM/DD/YY',
								fontSize: 6,
								textColor: '#000000',
								bold: true,
								italics: true
							}
						},
						{
							name: '번호',
							code: 'user',
							settings: {
								x: 120,
								y: 67,
								fontSize: 6,
								textColor: '#000000',
								bold: true,
								italics: true
							}
						}
					],
					updatedAt: new Date().toISOString()
				}
			]
		},
		// 알림 스토어
		{
			name: 'push_notifications',
			keyPath: 'id',
			indexes: [
				{ name: 'created_at', keyPath: 'created_at', options: { unique: false } },
				{ name: 'expire_day', keyPath: 'expire_day', options: { unique: false } },
				{ name: 'read', keyPath: 'read', options: { unique: false } },
				{ name: 'priority', keyPath: 'priority', options: { unique: false } },
				{ name: 'category', keyPath: 'category', options: { unique: false } }
			]
		}
	]
};

// 전역 데이터베이스 인스턴스 관리
let employeeDB: IDBDatabase | null = null;

/**
 * EmployeeDB 통합 초기화
 * 프린터 설정과 알림 데이터베이스를 통합 관리
 */
export async function initEmployeeDB(): Promise<IDBDatabase> {
	if (employeeDB) {
		return employeeDB;
	}

	try {
		employeeDB = await initIndexedDB(EMPLOYEE_DB_CONFIG);
		console.log('EmployeeDB 통합 초기화 완료');
		return employeeDB;
	} catch (error) {
		console.error('EmployeeDB 통합 초기화 실패:', error);
		throw error;
	}
}

/**
 * EmployeeDB 연결 종료
 */
export function closeEmployeeDB(): void {
	closeDatabase(employeeDB);
	employeeDB = null;
}
