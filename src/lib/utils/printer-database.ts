/**
 * 프린터 설정 데이터베이스 함수들
 * 함수형 프로그래밍 패턴으로 구현
 */

import {
	initEmployeeDB,
	saveData,
	getData,
	getAllKeys,
	deleteData,
	closeEmployeeDB,
	type DatabaseConfig
} from './indexeddb-utils';

export interface SettingItem {
	name: string;
	code: string;
	settings: Record<string, any>;
}

export interface PrinterSettingData {
	settingName: string;
	settings: SettingItem[];
	updatedAt: string;
}

// 데이터베이스 설정 제거 (통합 설정 사용)
const STORE_NAME = 'print_settings';

// 전역 데이터베이스 연결 관리 제거 (통합 관리)

/**
 * 프린터 데이터베이스 초기화
 */
export async function initPrinterDatabase(): Promise<IDBDatabase> {
	try {
		const db = await initEmployeeDB();
		console.log('프린터 데이터베이스 초기화 완료');
		return db;
	} catch (error) {
		console.error('프린터 데이터베이스 초기화 실패:', error);
		throw error;
	}
}

/**
 * 프린터 설정 저장
 */
export async function savePrinterSettings(
	settingName: string,
	settings: SettingItem[]
): Promise<void> {
	if (!settingName.trim()) {
		throw new Error('설정 이름은 필수입니다.');
	}

	const db = await initEmployeeDB();
	const settingData: PrinterSettingData = {
		settingName,
		settings,
		updatedAt: new Date().toISOString()
	};

	await saveData(db, STORE_NAME, settingData);
	console.log('프린터 설정 저장 완료:', settingName);
}

/**
 * 프린터 설정 조회
 */
export async function getPrinterSettings(settingName: string): Promise<SettingItem[] | undefined> {
	const db = await initEmployeeDB();
	const result = await getData<PrinterSettingData>(db, STORE_NAME, settingName);

	return result?.settings;
}

/**
 * 모든 프린터 설정 이름 조회
 */
export async function getAllPrinterSettingNames(): Promise<string[]> {
	const db = await initEmployeeDB();
	const keys = await getAllKeys(db, STORE_NAME);

	return keys as string[];
}

/**
 * 프린터 설정 삭제
 */
export async function deletePrinterSettings(settingName: string): Promise<void> {
	if (!settingName.trim()) {
		throw new Error('설정 이름은 필수입니다.');
	}

	if (settingName === '기본(default)') {
		throw new Error('기본 설정은 삭제할 수 없습니다.');
	}

	const db = await initEmployeeDB();
	await deleteData(db, STORE_NAME, settingName);
	console.log('프린터 설정 삭제 완료:', settingName);
}

/**
 * 프린터 데이터베이스 연결 종료
 */
export function closePrinterDatabase(): void {
	closeEmployeeDB();
}