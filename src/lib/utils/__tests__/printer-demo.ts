/**
 * 프린터 설정 데이터베이스 기능 데모 및 테스트
 * 브라우저 환경에서 실행하여 실제 IndexedDB 동작을 확인
 * 함수형 API 사용
 */

import {
	initPrinterDatabase,
	savePrinterSettings,
	getPrinterSettings,
	getAllPrinterSettingNames,
	deletePrinterSettings,
	getDefaultPrinterSettings,
	closePrinterDatabase,
	type SettingItem
} from '../printer-database';

export async function runPrinterDatabaseDemo() {
	console.log('=== 프린터 설정 데이터베이스 데모 시작 ===');

	try {
		// 1. 데이터베이스 초기화
		console.log('1. 데이터베이스 초기화 중...');
		await initPrinterDatabase();
		console.log('✅ 데이터베이스 초기화 완료');

		// 2. 기본 설정 조회
		console.log('2. 기본 설정 조회 중...');
		const defaultSettings = getDefaultPrinterSettings();
		console.log(`✅ 기본 설정 항목 개수: ${defaultSettings.length}`);
		defaultSettings.forEach((setting) => {
			console.log(`  - ${setting.name} (${setting.code})`);
		});

		// 3. 기본 설정 조회 (데이터베이스에서)
		console.log('3. 데이터베이스에서 기본 설정 조회 중...');
		const dbDefaultSettings = await getPrinterSettings('기본(default)');
		console.log(`✅ DB 기본 설정 항목 개수: ${dbDefaultSettings.length}`);

		// 4. 커스텀 설정 생성 및 저장
		console.log('4. 커스텀 설정 생성 및 저장 중...');
		const customSettings: SettingItem[] = [
			{
				name: '폰트(글꼴)',
				code: 'font',
				settings: { fontFamily: 'Arial' } // 기본값과 다르게 설정
			},
			{
				name: '바코드',
				code: 'barcode',
				settings: {
					x: 10, // 기본값과 다르게 설정
					y: 5
				}
			},
			{
				name: 'QAID',
				code: 'qaid',
				settings: { fontSize: 12 } // 기본값과 다르게 설정
			}
		];

		await savePrinterSettings('커스텀설정1', customSettings);
		console.log('✅ 커스텀설정1 저장 완료');

		await savePrinterSettings('커스텀설정2', defaultSettings);
		console.log('✅ 커스텀설정2 저장 완료');

		// 5. 모든 설정 이름 조회
		console.log('5. 모든 설정 이름 조회 중...');
		const allSettingNames = await getAllPrinterSettingNames();
		console.log(`✅ 전체 설정 개수: ${allSettingNames.length}`);
		allSettingNames.forEach((name) => {
			console.log(`  - ${name}`);
		});

		// 6. 특정 설정 조회
		console.log('6. 커스텀설정1 조회 중...');
		const customSetting1 = await getPrinterSettings('커스텀설정1');
		console.log(`✅ 커스텀설정1 항목 개수: ${customSetting1.length}`);
		customSetting1.forEach((setting) => {
			console.log(`  - ${setting.name}: ${JSON.stringify(setting.settings)}`);
		});

		// 7. 존재하지 않는 설정 조회 (기본값 반환 확인)
		console.log('7. 존재하지 않는 설정 조회 중...');
		const nonExistentSetting = await getPrinterSettings('존재하지않는설정');
		console.log(`✅ 존재하지 않는 설정 조회 시 기본값 반환: ${nonExistentSetting.length}개 항목`);

		// 8. 설정 삭제 테스트
		console.log('8. 커스텀설정2 삭제 중...');
		await deletePrinterSettings('커스텀설정2');
		console.log('✅ 커스텀설정2 삭제 완료');

		// 9. 기본 설정 삭제 시도 (에러 확인)
		console.log('9. 기본 설정 삭제 시도 중...');
		try {
			await deletePrinterSettings('기본(default)');
			console.log('❌ 기본 설정이 삭제되었습니다 (예상되지 않은 동작)');
		} catch (error) {
			console.log('✅ 기본 설정 삭제 방지 확인:', (error as Error).message);
		}

		// 10. 최종 상태 확인
		console.log('10. 최종 상태 확인 중...');
		const finalSettingNames = await getAllPrinterSettingNames();
		console.log(`✅ 최종 설정 개수: ${finalSettingNames.length}`);
		finalSettingNames.forEach((name) => {
			console.log(`  - ${name}`);
		});

		console.log('=== 프린터 설정 데이터베이스 데모 완료 ===');
		return true;
	} catch (error) {
		console.error('❌ 프린터 설정 데이터베이스 데모 실행 중 오류:', error);
		return false;
	} finally {
		closePrinterDatabase();
	}
}

// 브라우저 환경에서 실행할 수 있도록 전역 함수로 등록
if (typeof window !== 'undefined') {
	(window as any).runPrinterDatabaseDemo = runPrinterDatabaseDemo;
}
