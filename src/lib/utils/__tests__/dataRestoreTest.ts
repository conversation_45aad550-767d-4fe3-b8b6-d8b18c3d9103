/**
 * 데이터 복원 기능 테스트 유틸리티
 *
 * 데이터 복원 기능이 올바르게 작동하는지 테스트하기 위한 유틸리티 함수들을 제공합니다.
 */

import type { BatchProductData, BatchStorageState } from '../../types/batchTypes';
import { getBatchState, saveBatchState } from '../batchStorageUtils';

/**
 * 테스트용 배치 데이터 생성
 * 데이터 복원 기능을 테스트하기 위한 샘플 데이터를 생성합니다.
 *
 * @param palletCount 생성할 팔레트 수
 * @param productsPerPallet 팔레트당 상품 수
 * @returns 생성된 테스트 데이터 정보
 */
export function createTestBatchData(
	palletCount: number = 2,
	productsPerPallet: number = 3
): {
	success: boolean;
	message: string;
	createdPallets: string[];
	totalProducts: number;
} {
	try {
		const state = getBatchState();
		const createdPallets: string[] = [];
		let totalProducts = 0;

		// 테스트용 팔레트 및 상품 생성
		for (let i = 1; i <= palletCount; i++) {
			const palletId = `A-1-1-${i}-${i}`;
			createdPallets.push(palletId);

			// 팔레트가 존재하지 않으면 생성
			if (!state.pallets[palletId]) {
				state.pallets[palletId] = {
					products: [],
					palletInfo: {}
				};
			}

			// 테스트용 상품 생성
			for (let j = 1; j <= productsPerPallet; j++) {
				const product: BatchProductData = {
					id: `test_${i}_${j}_${Date.now()}`,
					qaid: `TEST${String(i).padStart(2, '0')}${String(j).padStart(3, '0')}`,
					timestamp: Date.now() - (palletCount - i) * 60000 - (productsPerPallet - j) * 10000,
					status: j === 1 ? 'failed' : j === productsPerPallet ? 'success' : 'pending',
					palletId: palletId,
					productInfo: {
						testData: true,
						palletIndex: i,
						productIndex: j,
						description: `테스트 상품 ${i}-${j}`
					},
					errorMessage: j === 1 ? '테스트 오류 메시지' : undefined
				};

				state.pallets[palletId].products.push(product);
				totalProducts++;
			}
		}

		// 첫 번째 팔레트를 현재 팔레트로 설정
		if (createdPallets.length > 0) {
			state.currentPalletId = createdPallets[0];
		}

		// 상태 저장
		saveBatchState(state);

		return {
			success: true,
			message: `${palletCount}개 팔레트에 총 ${totalProducts}개의 테스트 상품이 생성되었습니다.`,
			createdPallets,
			totalProducts
		};
	} catch (error) {
		console.error('테스트 배치 데이터 생성 중 오류 발생:', error);
		return {
			success: false,
			message: `테스트 데이터 생성 실패: ${error}`,
			createdPallets: [],
			totalProducts: 0
		};
	}
}

/**
 * 테스트 데이터 정리
 * 생성된 테스트 데이터를 정리합니다.
 *
 * @param palletIds 정리할 팔레트 ID 목록 (생략 시 모든 테스트 데이터 정리)
 * @returns 정리 결과
 */
export function cleanupTestData(palletIds?: string[]): {
	success: boolean;
	message: string;
	cleanedPallets: string[];
	cleanedProducts: number;
} {
	try {
		const state = getBatchState();
		const cleanedPallets: string[] = [];
		let cleanedProducts = 0;

		if (palletIds) {
			// 지정된 팔레트만 정리
			palletIds.forEach((palletId) => {
				if (state.pallets[palletId]) {
					cleanedProducts += state.pallets[palletId].products.length;
					delete state.pallets[palletId];
					cleanedPallets.push(palletId);
				}
			});
		} else {
			// 모든 테스트 데이터 정리 (testData: true인 상품들)
			Object.keys(state.pallets).forEach((palletId) => {
				const pallet = state.pallets[palletId];
				const originalLength = pallet.products.length;

				// 테스트 데이터가 아닌 상품만 유지
				pallet.products = pallet.products.filter((product) => !product.productInfo?.testData);

				const removedCount = originalLength - pallet.products.length;
				if (removedCount > 0) {
					cleanedProducts += removedCount;
					cleanedPallets.push(palletId);
				}

				// 팔레트가 비어있으면 삭제
				if (pallet.products.length === 0) {
					delete state.pallets[palletId];
				}
			});
		}

		// 현재 팔레트가 삭제된 경우 초기화
		if (state.currentPalletId && !state.pallets[state.currentPalletId]) {
			const remainingPallets = Object.keys(state.pallets);
			state.currentPalletId = remainingPallets.length > 0 ? remainingPallets[0] : '';
		}

		// 상태 저장
		saveBatchState(state);

		return {
			success: true,
			message: `${cleanedPallets.length}개 팔레트에서 ${cleanedProducts}개의 테스트 상품이 정리되었습니다.`,
			cleanedPallets,
			cleanedProducts
		};
	} catch (error) {
		console.error('테스트 데이터 정리 중 오류 발생:', error);
		return {
			success: false,
			message: `테스트 데이터 정리 실패: ${error}`,
			cleanedPallets: [],
			cleanedProducts: 0
		};
	}
}

/**
 * 데이터 복원 시뮬레이션
 * 페이지 새로고침을 시뮬레이션하여 데이터 복원 기능을 테스트합니다.
 *
 * @returns 시뮬레이션 결과
 */
export async function simulatePageRefresh(): Promise<{
	success: boolean;
	message: string;
	beforeRefresh: {
		palletCount: number;
		totalProducts: number;
		pendingProducts: number;
	};
	afterRefresh: {
		palletCount: number;
		totalProducts: number;
		pendingProducts: number;
	};
	dataIntegrity: boolean;
}> {
	try {
		// 새로고침 전 상태 저장
		const beforeState = getBatchState();
		const beforeStats = {
			palletCount: Object.keys(beforeState.pallets).length,
			totalProducts: beforeState.totalCount,
			pendingProducts: beforeState.pendingCount
		};

		// 데이터 복원 유틸리티 테스트
		const { checkDataRestore } = await import('../dataRestoreUtils');
		const restoreResult = await checkDataRestore();

		// 새로고침 후 상태 확인
		const afterState = getBatchState();
		const afterStats = {
			palletCount: Object.keys(afterState.pallets).length,
			totalProducts: afterState.totalCount,
			pendingProducts: afterState.pendingCount
		};

		// 데이터 무결성 확인
		const dataIntegrity =
			beforeStats.palletCount === afterStats.palletCount &&
			beforeStats.totalProducts === afterStats.totalProducts &&
			beforeStats.pendingProducts === afterStats.pendingProducts;

		return {
			success: restoreResult.success,
			message: restoreResult.message,
			beforeRefresh: beforeStats,
			afterRefresh: afterStats,
			dataIntegrity
		};
	} catch (error) {
		console.error('페이지 새로고침 시뮬레이션 중 오류 발생:', error);
		return {
			success: false,
			message: `시뮬레이션 실패: ${error}`,
			beforeRefresh: { palletCount: 0, totalProducts: 0, pendingProducts: 0 },
			afterRefresh: { palletCount: 0, totalProducts: 0, pendingProducts: 0 },
			dataIntegrity: false
		};
	}
}

/**
 * 브라우저 스토리지 상태 확인
 * 로컬스토리지의 배치 데이터 상태를 확인합니다.
 *
 * @returns 스토리지 상태 정보
 */
export function checkStorageState(): {
	hasData: boolean;
	storageKeys: string[];
	dataSize: number;
	lastUpdated: string;
	version: string;
} {
	try {
		const storageKeys: string[] = [];
		let dataSize = 0;

		// 배치 관련 키 찾기
		for (let i = 0; i < localStorage.length; i++) {
			const key = localStorage.key(i);
			if (key && key.startsWith('batch_pallet_')) {
				storageKeys.push(key);
				const value = localStorage.getItem(key) || '';
				dataSize += (key.length + value.length) * 2; // UTF-16 기준 바이트 계산
			}
		}

		// 마지막 업데이트 시간
		const lastCheck = localStorage.getItem('batch_pallet_last_check');
		const lastUpdated = lastCheck
			? new Date(parseInt(lastCheck)).toLocaleString('ko-KR')
			: '알 수 없음';

		// 버전 정보
		const version = localStorage.getItem('batch_pallet_storage_version') || '알 수 없음';

		return {
			hasData: storageKeys.length > 0,
			storageKeys,
			dataSize: Math.round(dataSize / 1024), // KB 단위
			lastUpdated,
			version
		};
	} catch (error) {
		console.error('스토리지 상태 확인 중 오류 발생:', error);
		return {
			hasData: false,
			storageKeys: [],
			dataSize: 0,
			lastUpdated: '오류',
			version: '오류'
		};
	}
}

/**
 * 전체 테스트 실행
 * 데이터 복원 기능의 전체 테스트를 실행합니다.
 *
 * @returns 테스트 결과
 */
export async function runFullTest(): Promise<{
	success: boolean;
	message: string;
	testResults: {
		dataCreation: any;
		storageState: any;
		dataRestore: any;
		simulation: any;
		cleanup: any;
	};
}> {
	try {
		console.log('=== 데이터 복원 기능 전체 테스트 시작 ===');

		// 1. 테스트 데이터 생성
		console.log('1. 테스트 데이터 생성...');
		const dataCreation = createTestBatchData(3, 5);
		console.log('데이터 생성 결과:', dataCreation);

		// 2. 스토리지 상태 확인
		console.log('2. 스토리지 상태 확인...');
		const storageState = checkStorageState();
		console.log('스토리지 상태:', storageState);

		// 3. 데이터 복원 테스트
		console.log('3. 데이터 복원 테스트...');
		const { checkDataRestore } = await import('../dataRestoreUtils');
		const dataRestore = await checkDataRestore();
		console.log('데이터 복원 결과:', dataRestore);

		// 4. 페이지 새로고침 시뮬레이션
		console.log('4. 페이지 새로고침 시뮬레이션...');
		const simulation = await simulatePageRefresh();
		console.log('시뮬레이션 결과:', simulation);

		// 5. 테스트 데이터 정리
		console.log('5. 테스트 데이터 정리...');
		const cleanup = cleanupTestData();
		console.log('정리 결과:', cleanup);

		console.log('=== 데이터 복원 기능 전체 테스트 완료 ===');

		const allTestsPassed =
			dataCreation.success &&
			storageState.hasData &&
			dataRestore.success &&
			simulation.success &&
			simulation.dataIntegrity &&
			cleanup.success;

		return {
			success: allTestsPassed,
			message: allTestsPassed
				? '모든 테스트가 성공적으로 완료되었습니다.'
				: '일부 테스트가 실패했습니다. 콘솔 로그를 확인해주세요.',
			testResults: {
				dataCreation,
				storageState,
				dataRestore,
				simulation,
				cleanup
			}
		};
	} catch (error) {
		console.error('전체 테스트 실행 중 오류 발생:', error);
		return {
			success: false,
			message: `전체 테스트 실행 실패: ${error}`,
			testResults: {
				dataCreation: null,
				storageState: null,
				dataRestore: null,
				simulation: null,
				cleanup: null
			}
		};
	}
}

// 개발 환경에서 전역 함수로 노출 (디버깅용)
if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
	(window as any).batchDataRestoreTest = {
		createTestData: createTestBatchData,
		cleanupTestData,
		simulatePageRefresh,
		checkStorageState,
		runFullTest
	};
}
