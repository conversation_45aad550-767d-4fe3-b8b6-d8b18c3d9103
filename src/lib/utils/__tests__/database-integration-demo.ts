/**
 * 프린터 설정과 알림 데이터베이스 통합 데모
 * 두 데이터베이스가 동일한 EmployeeDB를 사용하면서 독립적으로 작동하는지 확인
 */

import { runPrinterDatabaseDemo } from './printer-demo';
import { runNotificationDatabaseDemo } from './notification-demo';

export async function runDatabaseIntegrationDemo() {
	console.log('=== 데이터베이스 통합 데모 시작 ===');
	console.log(
		'프린터 설정과 알림 데이터베이스가 동일한 EmployeeDB를 공유하면서 독립적으로 작동하는지 확인합니다.'
	);
	console.log('');

	try {
		// 1. 프린터 데이터베이스 데모 실행
		console.log('🖨️ 프린터 데이터베이스 데모 실행...');
		const printerResult = await runPrinterDatabaseDemo();

		if (!printerResult) {
			console.error('❌ 프린터 데이터베이스 데모 실패');
			return false;
		}

		console.log('✅ 프린터 데이터베이스 데모 성공');
		console.log('');

		// 2. 알림 데이터베이스 데모 실행
		console.log('🔔 알림 데이터베이스 데모 실행...');
		const notificationResult = await runNotificationDatabaseDemo();

		if (!notificationResult) {
			console.error('❌ 알림 데이터베이스 데모 실패');
			return false;
		}

		console.log('✅ 알림 데이터베이스 데모 성공');
		console.log('');

		// 3. 동시 접근 테스트
		console.log('🔄 동시 접근 테스트...');

		// 프린터와 알림 데이터베이스를 동시에 초기화
		const { initPrinterDatabase } = await import('../printer-database');
		const { initNotificationDatabase } = await import('../notification-database');

		const [printerDB, notificationDB] = await Promise.all([
			initPrinterDatabase(),
			initNotificationDatabase()
		]);

		console.log('✅ 두 데이터베이스 동시 초기화 성공');
		console.log(`  - 프린터 DB: ${printerDB.name} v${printerDB.version}`);
		console.log(`  - 알림 DB: ${notificationDB.name} v${notificationDB.version}`);

		// 4. 스토어 확인
		console.log('📊 데이터베이스 스토어 확인...');
		const storeNames = Array.from(printerDB.objectStoreNames);
		console.log(`✅ EmployeeDB 스토어 목록: ${storeNames.join(', ')}`);

		// 예상되는 스토어들이 모두 있는지 확인
		const expectedStores = ['print_settings', 'push_notifications'];
		const hasAllStores = expectedStores.every((store) => storeNames.includes(store));

		if (hasAllStores) {
			console.log('✅ 모든 예상 스토어가 존재합니다');
		} else {
			console.log('❌ 일부 스토어가 누락되었습니다');
			return false;
		}

		console.log('');
		console.log('=== 데이터베이스 통합 데모 완료 ===');
		console.log('✅ 프린터 설정과 알림 데이터베이스가 성공적으로 통합되었습니다!');
		console.log('');
		console.log('📋 요약:');
		console.log('  - 동일한 EmployeeDB (버전 2) 사용');
		console.log('  - print_settings 스토어: 프린터 설정 관리');
		console.log('  - push_notifications 스토어: 알림 데이터 관리');
		console.log('  - 각 데이터베이스는 독립적으로 작동');
		console.log('  - 공통 IndexedDB 유틸리티 함수 활용');

		return true;
	} catch (error) {
		console.error('❌ 데이터베이스 통합 데모 실행 중 오류:', error);
		return false;
	}
}

// 브라우저 환경에서 실행할 수 있도록 전역 함수로 등록
if (typeof window !== 'undefined') {
	(window as any).runDatabaseIntegrationDemo = runDatabaseIntegrationDemo;
	(window as any).runPrinterDatabaseDemo = runPrinterDatabaseDemo;
	(window as any).runNotificationDatabaseDemo = runNotificationDatabaseDemo;
}
