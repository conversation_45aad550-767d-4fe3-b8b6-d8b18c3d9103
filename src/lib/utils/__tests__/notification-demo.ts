/**
 * 알림 데이터베이스 기능 데모 및 테스트
 * 브라우저 환경에서 실행하여 실제 IndexedDB 동작을 확인
 * 함수형 API 사용
 */

import {
	initNotificationDatabase,
	saveNotification,
	getAllNotifications,
	getActiveNotifications,
	getUnreadNotificationCount,
	getNotificationsForUser,
	markNotificationAsRead,
	deleteExpiredNotifications,
	closeNotificationDatabase
} from '../notification-database';
import type { NotificationData } from '$lib/types/notification';

export async function runNotificationDatabaseDemo() {
	console.log('=== 알림 데이터베이스 데모 시작 ===');

	try {
		// 1. 데이터베이스 초기화
		console.log('1. 데이터베이스 초기화 중...');
		await initNotificationDatabase();
		console.log('✅ 데이터베이스 초기화 완료');

		// 2. 테스트 알림 데이터 생성
		const testNotifications: NotificationData[] = [
			{
				id: 'test-1',
				content: '전체 발송 테스트 알림입니다.',
				expire_day: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(), // 7일 후
				created_at: new Date().toISOString(),
				read: false,
				type: 'broadcast'
			},
			{
				id: 'test-2',
				content: '그룹 발송 테스트 알림입니다.',
				expire_day: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000).toISOString(), // 3일 후
				created_at: new Date().toISOString(),
				read: false,
				type: 'group',
				target_groups: ['admin', 'manager']
			},
			{
				id: 'test-3',
				content: '개인 발송 테스트 알림입니다.',
				expire_day: new Date(Date.now() + 1 * 24 * 60 * 60 * 1000).toISOString(), // 1일 후
				created_at: new Date().toISOString(),
				read: true,
				type: 'personal',
				target_user_id: 'user123'
			},
			{
				id: 'test-4',
				content: '만료된 테스트 알림입니다.',
				expire_day: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(), // 1일 전 (만료됨)
				created_at: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
				read: false,
				type: 'broadcast'
			}
		];

		// 3. 알림 저장 테스트
		console.log('2. 테스트 알림 저장 중...');
		for (const notification of testNotifications) {
			await saveNotification(notification);
			console.log(`✅ 알림 저장 완료: ${notification.id}`);
		}

		// 4. 모든 알림 조회 테스트
		console.log('3. 모든 알림 조회 중...');
		const allNotifications = await getAllNotifications();
		console.log(`✅ 전체 알림 개수: ${allNotifications.length}`);
		allNotifications.forEach((notification) => {
			console.log(`  - ${notification.id}: ${notification.content} (읽음: ${notification.read})`);
		});

		// 5. 활성 알림 조회 테스트
		console.log('4. 활성 알림 조회 중...');
		const activeNotifications = await getActiveNotifications();
		console.log(`✅ 활성 알림 개수: ${activeNotifications.length}`);
		activeNotifications.forEach((notification) => {
			console.log(`  - ${notification.id}: ${notification.content}`);
		});

		// 6. 읽지 않은 알림 개수 조회 테스트
		console.log('5. 읽지 않은 알림 개수 조회 중...');
		const unreadCount = await getUnreadNotificationCount();
		console.log(`✅ 읽지 않은 알림 개수: ${unreadCount}`);

		// 7. 사용자별 알림 조회 테스트
		console.log('6. 사용자별 알림 조회 중...');
		const userNotifications = await getNotificationsForUser('user123', ['admin']);
		console.log(`✅ 사용자 user123 (admin 그룹) 알림 개수: ${userNotifications.length}`);
		userNotifications.forEach((notification) => {
			console.log(`  - ${notification.id}: ${notification.content} (타입: ${notification.type})`);
		});

		// 8. 알림 읽음 처리 테스트
		console.log('7. 알림 읽음 처리 중...');
		await markNotificationAsRead('test-1');
		console.log('✅ test-1 알림을 읽음으로 표시 완료');

		// 9. 만료된 알림 삭제 테스트
		console.log('8. 만료된 알림 삭제 중...');
		const deletedCount = await deleteExpiredNotifications();
		console.log(`✅ 만료된 알림 ${deletedCount}개 삭제 완료`);

		// 10. 최종 상태 확인
		console.log('9. 최종 상태 확인 중...');
		const finalNotifications = await getAllNotifications();
		const finalUnreadCount = await getUnreadNotificationCount();
		console.log(`✅ 최종 전체 알림 개수: ${finalNotifications.length}`);
		console.log(`✅ 최종 읽지 않은 알림 개수: ${finalUnreadCount}`);

		console.log('=== 알림 데이터베이스 데모 완료 ===');
		return true;
	} catch (error) {
		console.error('❌ 알림 데이터베이스 데모 실행 중 오류:', error);
		return false;
	} finally {
		closeNotificationDatabase();
	}
}

// 브라우저 환경에서 실행할 수 있도록 전역 함수로 등록
if (typeof window !== 'undefined') {
	(window as any).runNotificationDatabaseDemo = runNotificationDatabaseDemo;
}
