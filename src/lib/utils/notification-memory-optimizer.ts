/**
 * 알림 메모리 사용량 최적화 유틸리티
 * 기존 캐싱 전략 활용 및 불필요한 데이터 로딩 방지
 * Requirements: 6.2.1, 6.2.2
 */

import type { NotificationData } from '$lib/types/notification';
import { notificationCache, performanceMonitor } from './notification-performance';
import { initEmployeeDB, getAllData } from './indexeddb-utils';

const NOTIFICATION_STORE = 'push_notifications';

/**
 * 메모리 효율적인 알림 데이터 관리자
 * 기존 캐싱 전략을 활용하여 메모리 사용량 최적화
 */
export const memoryOptimizer = {
	// 메모리 사용량 제한 설정
	_maxCacheSize: 100, // 최대 캐시할 알림 개수
	_maxMemoryUsage: 50 * 1024 * 1024, // 50MB 제한
	_compressionEnabled: true,

	/**
	 * 메모리 사용량 모니터링
	 */
	getMemoryUsage(): {
		cacheSize: number;
		estimatedMemoryUsage: number;
		compressionEnabled: boolean;
	} {
		const cacheSize = notificationCache._cache.size;
		let estimatedMemoryUsage = 0;

		// 캐시된 데이터의 대략적인 메모리 사용량 계산
		for (const [key, data] of notificationCache._cache.entries()) {
			const keySize = key.length * 2; // UTF-16 문자당 2바이트
			const dataSize = JSON.stringify(data).length * 2;
			estimatedMemoryUsage += keySize + dataSize;
		}

		return {
			cacheSize,
			estimatedMemoryUsage,
			compressionEnabled: this._compressionEnabled
		};
	},

	/**
	 * 메모리 정리 (기존 정리 로직 활용)
	 */
	async cleanupMemory(): Promise<void> {
		const memoryUsage = this.getMemoryUsage();

		// 메모리 사용량이 제한을 초과하면 정리
		if (
			memoryUsage.cacheSize > this._maxCacheSize ||
			memoryUsage.estimatedMemoryUsage > this._maxMemoryUsage
		) {
			console.log('메모리 정리 시작:', memoryUsage);

			// 오래된 캐시부터 제거
			const cacheEntries = Array.from(notificationCache._lastUpdate.entries()).sort(
				(a, b) => a[1] - b[1]
			);

			const toRemove = Math.ceil(cacheEntries.length * 0.3); // 30% 제거
			for (let i = 0; i < toRemove; i++) {
				const [key] = cacheEntries[i];
				notificationCache._cache.delete(key);
				notificationCache._lastUpdate.delete(key);
			}

			console.log(`메모리 정리 완료: ${toRemove}개 캐시 제거`);
		}
	},

	/**
	 * 압축된 알림 데이터 생성 (불필요한 필드 제거)
	 */
	compressNotificationData(notification: NotificationData): Partial<NotificationData> {
		if (!this._compressionEnabled) {
			return notification;
		}

		// 필수 필드만 유지하여 메모리 사용량 감소
		return {
			id: notification.id,
			content: notification.content,
			created_at: notification.created_at,
			read: notification.read,
			priority: notification.priority,
			expire_day: notification.expire_day,
			message: notification.message
			// image_url, action_url 등 선택적 필드는 필요시에만 로드
		};
	},

	/**
	 * 압축된 알림 목록 생성
	 */
	compressNotificationList(notifications: NotificationData[]): Partial<NotificationData>[] {
		return notifications.map((notification) => this.compressNotificationData(notification));
	},

	/**
	 * 필요시에만 전체 알림 데이터 로드
	 */
	async getFullNotificationData(id: string): Promise<NotificationData | null> {
		const db = await initEmployeeDB();
		const transaction = db.transaction([NOTIFICATION_STORE], 'readonly');
		const store = transaction.objectStore(NOTIFICATION_STORE);

		return new Promise((resolve, reject) => {
			const request = store.get(id);
			request.onsuccess = () => resolve(request.result || null);
			request.onerror = () => reject(request.error);
		});
	}
};

/**
 * 지연 로딩 관리자
 * 불필요한 데이터 로딩 방지
 */
export const lazyLoader = {
	// 로딩 상태 관리
	_loadingStates: new Map<string, boolean>(),
	_loadedData: new Map<string, any>(),

	/**
	 * 지연 로딩 상태 확인
	 */
	isLoading(key: string): boolean {
		return this._loadingStates.get(key) || false;
	},

	/**
	 * 데이터가 이미 로드되었는지 확인
	 */
	isLoaded(key: string): boolean {
		return this._loadedData.has(key);
	},

	/**
	 * 지연 로딩으로 알림 이미지 로드
	 */
	async loadNotificationImage(notification: NotificationData): Promise<string | null> {
		if (!notification.image_url) {
			return null;
		}

		const cacheKey = `image_${notification.id}`;

		// 이미 로드된 경우 캐시에서 반환
		if (this.isLoaded(cacheKey)) {
			return this._loadedData.get(cacheKey);
		}

		// 현재 로딩 중인 경우 대기
		if (this.isLoading(cacheKey)) {
			return new Promise((resolve) => {
				const checkLoaded = () => {
					if (this.isLoaded(cacheKey)) {
						resolve(this._loadedData.get(cacheKey));
					} else {
						setTimeout(checkLoaded, 100);
					}
				};
				checkLoaded();
			});
		}

		// 새로운 로딩 시작
		this._loadingStates.set(cacheKey, true);

		try {
			// 이미지 URL 유효성 검사 및 로드
			const response = await fetch(notification.image_url, { method: 'HEAD' });
			if (response.ok) {
				this._loadedData.set(cacheKey, notification.image_url);
				return notification.image_url;
			} else {
				this._loadedData.set(cacheKey, null);
				return null;
			}
		} catch (error) {
			console.warn(`이미지 로드 실패 (${notification.id}):`, error);
			this._loadedData.set(cacheKey, null);
			return null;
		} finally {
			this._loadingStates.set(cacheKey, false);
		}
	},

	/**
	 * 지연 로딩으로 알림 상세 데이터 로드
	 */
	async loadNotificationDetails(notificationId: string): Promise<NotificationData | null> {
		const cacheKey = `details_${notificationId}`;

		// 이미 로드된 경우 캐시에서 반환
		if (this.isLoaded(cacheKey)) {
			return this._loadedData.get(cacheKey);
		}

		// 현재 로딩 중인 경우 대기
		if (this.isLoading(cacheKey)) {
			return new Promise((resolve) => {
				const checkLoaded = () => {
					if (this.isLoaded(cacheKey)) {
						resolve(this._loadedData.get(cacheKey));
					} else {
						setTimeout(checkLoaded, 100);
					}
				};
				checkLoaded();
			});
		}

		// 새로운 로딩 시작
		this._loadingStates.set(cacheKey, true);

		try {
			const fullData = await memoryOptimizer.getFullNotificationData(notificationId);
			this._loadedData.set(cacheKey, fullData);
			return fullData;
		} catch (error) {
			console.error(`알림 상세 데이터 로드 실패 (${notificationId}):`, error);
			this._loadedData.set(cacheKey, null);
			return null;
		} finally {
			this._loadingStates.set(cacheKey, false);
		}
	},

	/**
	 * 사용하지 않는 지연 로딩 데이터 정리
	 */
	cleanupUnusedData(): void {
		const now = Date.now();
		const maxAge = 10 * 60 * 1000; // 10분

		// 오래된 데이터 제거
		for (const [key, data] of this._loadedData.entries()) {
			// 데이터에 타임스탬프가 있다면 확인
			if (data && typeof data === 'object' && data._loadedAt) {
				if (now - data._loadedAt > maxAge) {
					this._loadedData.delete(key);
				}
			}
		}

		// 완료된 로딩 상태 정리
		for (const [key, isLoading] of this._loadingStates.entries()) {
			if (!isLoading) {
				this._loadingStates.delete(key);
			}
		}
	}
};

/**
 * 가상화 지원 유틸리티
 * 대용량 알림 목록의 메모리 효율적 렌더링 지원
 */
export const virtualizationHelper = {
	/**
	 * 가상화를 위한 알림 데이터 슬라이싱
	 */
	getVirtualizedNotifications(
		notifications: NotificationData[],
		startIndex: number,
		endIndex: number
	): {
		visibleNotifications: Partial<NotificationData>[];
		totalCount: number;
		startIndex: number;
		endIndex: number;
	} {
		const visibleNotifications = notifications
			.slice(startIndex, endIndex)
			.map((notification) => memoryOptimizer.compressNotificationData(notification));

		return {
			visibleNotifications,
			totalCount: notifications.length,
			startIndex,
			endIndex
		};
	},

	/**
	 * 스크롤 위치 기반 가상화 계산
	 */
	calculateVirtualization(
		scrollTop: number,
		containerHeight: number,
		itemHeight: number,
		totalItems: number,
		overscan: number = 5
	): {
		startIndex: number;
		endIndex: number;
		visibleCount: number;
	} {
		const visibleCount = Math.ceil(containerHeight / itemHeight);
		const startIndex = Math.max(0, Math.floor(scrollTop / itemHeight) - overscan);
		const endIndex = Math.min(totalItems, startIndex + visibleCount + overscan * 2);

		return {
			startIndex,
			endIndex,
			visibleCount
		};
	}
};

/**
 * 메모리 효율적인 알림 검색 유틸리티
 */
export const efficientSearch = {
	// 검색 인덱스 캐시
	_searchIndex: new Map<string, Set<string>>(),
	_indexBuilt: false,

	/**
	 * 검색 인덱스 구축 (메모리 효율적)
	 */
	async buildSearchIndex(): Promise<void> {
		if (this._indexBuilt) {
			return;
		}

		console.log('검색 인덱스 구축 시작');
		const db = await initEmployeeDB();
		const allNotifications = await getAllData<NotificationData>(db, NOTIFICATION_STORE);

		// 메모리 효율적인 인덱스 구축
		for (const notification of allNotifications) {
			const searchableText = [notification.content, notification.message, notification.category]
				.filter(Boolean)
				.join(' ')
				.toLowerCase();

			// 단어별로 인덱스 구축
			const words = searchableText.split(/\s+/).filter((word) => word.length > 1);
			for (const word of words) {
				if (!this._searchIndex.has(word)) {
					this._searchIndex.set(word, new Set());
				}
				this._searchIndex.get(word)!.add(notification.id);
			}
		}

		this._indexBuilt = true;
		console.log(`검색 인덱스 구축 완료: ${this._searchIndex.size}개 단어`);
	},

	/**
	 * 메모리 효율적인 검색 실행
	 */
	async searchNotifications(
		query: string,
		maxResults: number = 50
	): Promise<{ ids: string[]; totalMatches: number }> {
		await this.buildSearchIndex();

		const searchTerms = query
			.toLowerCase()
			.split(/\s+/)
			.filter((term) => term.length > 1);
		if (searchTerms.length === 0) {
			return { ids: [], totalMatches: 0 };
		}

		// 교집합 계산으로 검색 결과 도출
		let resultIds: Set<string> | null = null;

		for (const term of searchTerms) {
			const matchingIds = this._searchIndex.get(term);
			if (!matchingIds || matchingIds.size === 0) {
				// 하나라도 매치되지 않으면 결과 없음
				return { ids: [], totalMatches: 0 };
			}

			if (resultIds === null) {
				resultIds = new Set(matchingIds);
			} else {
				// 교집합 계산
				resultIds = new Set([...resultIds].filter((id: string) => matchingIds.has(id)));
			}

			if (resultIds.size === 0) {
				break;
			}
		}

		const ids = Array.from(resultIds || []).slice(0, maxResults);
		return {
			ids,
			totalMatches: resultIds?.size || 0
		};
	},

	/**
	 * 검색 인덱스 정리
	 */
	clearSearchIndex(): void {
		this._searchIndex.clear();
		this._indexBuilt = false;
		console.log('검색 인덱스 정리 완료');
	}
};

/**
 * 통합 메모리 최적화 매니저
 * 모든 메모리 최적화 기능을 통합 관리
 */
export const memoryManager = {
	optimizer: memoryOptimizer,
	lazyLoader: lazyLoader,
	virtualization: virtualizationHelper,
	search: efficientSearch,

	/**
	 * 메모리 최적화 초기화
	 */
	async initialize(): Promise<void> {
		console.log('메모리 최적화 초기화 시작');

		// 압축 모드 활성화
		this.optimizer._compressionEnabled = true;

		// 정기적인 메모리 정리 스케줄링
		setInterval(
			() => {
				this.performMaintenance();
			},
			5 * 60 * 1000
		); // 5분마다 실행

		console.log('메모리 최적화 초기화 완료');
	},

	/**
	 * 정기적인 메모리 최적화 작업
	 */
	async performMaintenance(): Promise<void> {
		const { result: memoryUsage } = await performanceMonitor.measureExecutionTime(
			'메모리 사용량 확인',
			async () => this.optimizer.getMemoryUsage()
		);

		console.log('메모리 사용량:', memoryUsage);

		// 메모리 정리
		await this.optimizer.cleanupMemory();

		// 지연 로딩 데이터 정리
		this.lazyLoader.cleanupUnusedData();

		// 검색 인덱스가 너무 크면 정리
		if (this.search._searchIndex.size > 10000) {
			this.search.clearSearchIndex();
		}
	},

	/**
	 * 메모리 사용량 보고서 생성
	 */
	generateMemoryReport(): {
		cache: ReturnType<typeof memoryOptimizer.getMemoryUsage>;
		lazyLoader: {
			loadingStates: number;
			loadedData: number;
		};
		search: {
			indexSize: number;
			indexBuilt: boolean;
		};
	} {
		return {
			cache: this.optimizer.getMemoryUsage(),
			lazyLoader: {
				loadingStates: this.lazyLoader._loadingStates.size,
				loadedData: this.lazyLoader._loadedData.size
			},
			search: {
				indexSize: this.search._searchIndex.size,
				indexBuilt: this.search._indexBuilt
			}
		};
	}
};
