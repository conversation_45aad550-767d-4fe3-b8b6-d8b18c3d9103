import { authActions } from '$lib/stores/authStore';
import { tokenService } from '$lib/services/tokenService';

interface TokenAutoRefreshOptions {
	/** 몇 분 전에 갱신을 시도할지 (기본값: 5분) */
	warningMinutes?: number;
	/** 토큰 상태를 확인하는 주기(ms) (기본값: 30초) */
	checkInterval?: number;
}

/**
 * 액세스 토큰 만료가 임박했을 때 자동으로 갱신하는 Svelte Action.
 * @param node - Action이 적용된 HTML 요소
 * @param options - 갱신 옵션
 */
export function tokenAutoRefresh(node: HTMLElement, options?: TokenAutoRefreshOptions) {
	const warningMinutes = options?.warningMinutes ?? 5;
	const checkInterval = options?.checkInterval ?? 30000; // 30초

	let timer: NodeJS.Timeout | null = null;

	async function checkTokenExpiry() {
		try {
			const status = await tokenService.getTokenStatus();

			if (status.isAccessTokenValid && status.accessTokenRemainingTime > 0) {
				const remainingSeconds = status.accessTokenRemainingTime;
				const warningThreshold = warningMinutes * 60;

				if (remainingSeconds <= warningThreshold) {
					await extendSession();
				}
			}
		} catch (error) {
			console.error('[Token Auto Refresh Action] 토큰 상태 확인 실패:', error);
		}
	}

	async function extendSession() {
		try {
			await authActions.refreshAuth();
		} catch (error) {
			console.error('[Token Auto Refresh Action] 세션 자동 연장 실패:', error);
		}
	}

	// 초기 확인 및 주기적 확인 설정
	checkTokenExpiry();
	timer = setInterval(checkTokenExpiry, checkInterval);

	return {
		destroy() {
			if (timer) {
				clearInterval(timer);
			}
		}
	};
}