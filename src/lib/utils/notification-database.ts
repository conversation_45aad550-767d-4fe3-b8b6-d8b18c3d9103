/**
 * 알림 데이터베이스 함수들
 * 함수형 프로그래밍 패턴으로 구현
 */

import {
	initEmployeeDB,
	saveData,
	getData,
	getAllData,
	deleteData,
	closeEmployeeDB,
	getDataByIndex,
	getDataByRange
} from './indexeddb-utils';
import type { NotificationData } from '$lib/types/notification';
import type { PusherBeamsNotificationData } from '$lib/types/pushNotificationTypes';

// 데이터베이스 설정 제거 (통합 설정 사용)
const NOTIFICATION_STORE = 'push_notifications';

// 전역 데이터베이스 연결 관리 제거 (통합 관리)

/**
 * 알림 데이터베이스 초기화
 */
export async function initNotificationDatabase(): Promise<IDBDatabase> {
	try {
		const db = await initEmployeeDB();
		console.log('알림 데이터베이스 초기화 완료');
		return db;
	} catch (error) {
		console.error('알림 데이터베이스 초기화 실패:', error);
		throw error;
	}
}

/**
 * 알림 저장
 */
export async function saveNotification(notification: NotificationData): Promise<void> {
	const db = await initEmployeeDB();

	const safeNotification: NotificationData = {
		...notification,
		created_at: notification.created_at || new Date().toISOString()
	};

	await saveData(db, NOTIFICATION_STORE, safeNotification);
	console.log('알림 저장 완료:', safeNotification.id);
}

/**
 * Pusher Beams 수신 알림을 기존 NotificationData 타입으로 변환하여 저장
 * action_url/redirect 필드는 사용하지 않음 (요구사항에 따라 제거됨)
 * Requirements: 3.1.1, 2.2
 */
export async function saveReceivedNotification(
	pusherNotification: PusherBeamsNotificationData
): Promise<void> {
	// ID 생성 (없는 경우)
	const id =
		pusherNotification.id || `pusher_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

	// 만료일 처리 (expire_at을 expire_day로 변환)
	let expireDay: string;
	if (pusherNotification.expire_at) {
		// ISO 8601 형식을 YYYY-MM-DD 형식으로 변환
		expireDay = new Date(pusherNotification.expire_at).toISOString().split('T')[0];
	} else {
		// 기본 6개월 후 만료
		const defaultExpire = new Date();
		defaultExpire.setMonth(defaultExpire.getMonth() + 6);
		expireDay = defaultExpire.toISOString().split('T')[0];
	}

	// NotificationData 형식으로 변환 (action_url/redirect 필드는 사용하지 않음)
	const notification: NotificationData = {
		id,
		content: pusherNotification.body,
		expire_day: expireDay,
		created_at: new Date().toISOString(),
		read: false,
		success: true,
		message: pusherNotification.title,
		priority: pusherNotification.priority || 'normal',
		category: pusherNotification.category,
		image_url: pusherNotification.image_url,
		deletable: pusherNotification.deletable ?? true // 기본값은 삭제 가능
	};

	// 기존 saveNotification 함수 활용
	await saveNotification(notification);
	console.log('Pusher Beams 알림 저장 완료:', id);
}

/**
 * 모든 알림 조회 (최신순 정렬)
 */
export async function getAllNotifications(): Promise<NotificationData[]> {
	const db = await initEmployeeDB();
	const notifications = await getAllData<NotificationData>(db, NOTIFICATION_STORE);

	return notifications.sort(
		(a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
	);
}

/**
 * 활성 알림 조회 (만료되지 않은 알림만)
 */
export async function getActiveNotifications(): Promise<NotificationData[]> {
	const allNotifications = await getAllNotifications();
	const now = new Date();

	return allNotifications.filter((notification) => {
		if (!notification.expire_day) {
			// 만료일이 없으면 6개월간 표시
			const sixMonthsAgo = new Date();
			sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);
			return new Date(notification.created_at) > sixMonthsAgo;
		}
		return new Date(notification.expire_day) > now;
	});
}

/**
 * 특정 알림 조회
 */
export async function getNotification(id: string): Promise<NotificationData | null> {
	const db = await initEmployeeDB();
	return await getData<NotificationData>(db, NOTIFICATION_STORE, id);
}

/**
 * 알림 삭제 (기존 함수 활용)
 * Requirements: 3.2.2, 3.3.2
 */
export async function deleteNotification(id: string): Promise<void> {
	const db = await initEmployeeDB();
	await deleteData(db, NOTIFICATION_STORE, id);
	console.log('알림 삭제 완료:', id);
}

/**
 * 삭제 가능한 알림만 삭제 (deletable 필드 확인)
 * Requirements: 3.3.2
 */
export async function deleteDeletableNotification(id: string): Promise<boolean> {
	const notification = await getNotification(id);
	if (!notification) {
		console.warn('삭제하려는 알림을 찾을 수 없습니다:', id);
		return false;
	}

	// deletable 필드가 false인 경우 삭제 불가
	if (notification.deletable === false) {
		console.warn('삭제 불가능한 알림입니다:', id);
		return false;
	}

	// 기존 deleteNotification 함수 활용
	await deleteNotification(id);
	return true;
}

/**
 * 만료된 알림 삭제
 */
export async function deleteExpiredNotifications(): Promise<number> {
	const allNotifications = await getAllNotifications();
	const now = new Date();
	let deletedCount = 0;

	for (const notification of allNotifications) {
		let shouldDelete = false;

		if (notification.expire_day) {
			// 만료일이 설정된 경우
			shouldDelete = new Date(notification.expire_day) <= now;
		} else {
			// 만료일이 없는 경우 6개월 후 삭제
			const sixMonthsAgo = new Date();
			sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);
			shouldDelete = new Date(notification.created_at) <= sixMonthsAgo;
		}

		if (shouldDelete) {
			await deleteNotification(notification.id);
			deletedCount++;
		}
	}

	console.log(`만료된 알림 ${deletedCount}개 삭제 완료`);
	return deletedCount;
}

/**
 * 읽지 않은 알림 개수 조회 (기존 인덱스 활용하여 성능 최적화)
 * Requirements: 3.2.3, 3.3.4
 */
export async function getUnreadNotificationCount(): Promise<number> {
	try {
		const db = await initEmployeeDB();

		// 모든 알림을 가져와서 필터링하는 방식으로 변경 (IndexedDB boolean 인덱스 문제 해결)
		const allNotifications = await getAllData<NotificationData>(db, NOTIFICATION_STORE);

		// 읽지 않은 알림 필터링
		const unreadNotifications = allNotifications.filter((notification) => !notification.read);

		// 만료되지 않은 알림만 카운트
		const now = new Date();
		return unreadNotifications.filter((notification) => {
			if (!notification.expire_day) {
				// 만료일이 없으면 6개월간 표시
				const sixMonthsAgo = new Date();
				sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);
				return new Date(notification.created_at) > sixMonthsAgo;
			}
			return new Date(notification.expire_day) > now;
		}).length;
	} catch (error) {
		console.error('읽지 않은 알림 수 조회 실패:', error);
		return 0;
	}
}

/**
 * 알림을 읽음으로 표시
 */
export async function markNotificationAsRead(id: string): Promise<void> {
	const notification = await getNotification(id);
	if (!notification) {
		throw new Error('알림을 찾을 수 없습니다.');
	}

	const updatedNotification: NotificationData = {
		...notification,
		read: true
	};

	await saveNotification(updatedNotification);
	console.log('알림 읽음 처리 완료:', id);
}

/**
 * 모든 알림을 읽음으로 표시
 */
export async function markAllNotificationsAsRead(): Promise<void> {
	const unreadNotifications = await getActiveNotifications();
	const unreadList = unreadNotifications.filter((notification) => !notification.read);

	for (const notification of unreadList) {
		await markNotificationAsRead(notification.id);
	}

	console.log(`${unreadList.length}개 알림을 읽음으로 표시 완료`);
}

/**
 * 사용자별 알림 조회 (서버에서 이미 필터링되어 전송됨)
 */
export async function getNotificationsForUser(): Promise<NotificationData[]> {
	// 서버에서 이미 해당 사용자에게 필터링해서 보낸 알림들이므로
	// 모든 활성 알림을 반환
	return await getActiveNotifications();
}

/**
 * 읽지 않은 사용자별 알림 개수 조회 (단순화된 버전)
 */
export async function getUnreadCountForUser(): Promise<number> {
	// 서버에서 이미 필터링되어 전송된 알림들의 읽지 않은 개수 반환
	return await getUnreadNotificationCount();
}

/**
 * 우선순위별 알림 조회 (인덱스 오류 해결)
 * Requirements: 2.1.1, 2.1.2, 2.1.3, 2.1.4
 */
export async function getNotificationsByPriority(
	priority: 'low' | 'normal' | 'high' | 'urgent'
): Promise<NotificationData[]> {
	const db = await initEmployeeDB();

	try {
		// 우선순위 인덱스를 활용한 빠른 조회 시도
		const notifications = await getDataByIndex<NotificationData>(
			db,
			NOTIFICATION_STORE,
			'priority',
			priority
		);

		// priority가 null/undefined인 경우 'normal'로 처리
		if (priority === 'normal') {
			try {
				const nullPriorityNotifications = await getDataByIndex<NotificationData>(
					db,
					NOTIFICATION_STORE,
					'priority',
					undefined
				);
				notifications.push(...nullPriorityNotifications);
			} catch (error) {
				console.warn('[NotificationDB] null priority 조회 실패 (무시됨):', error);
			}
		}

		// 만료되지 않은 알림만 필터링하고 최신순 정렬
		const now = new Date();
		return notifications
			.filter((notification) => {
				if (!notification.expire_day) {
					// 만료일이 없으면 6개월간 표시
					const sixMonthsAgo = new Date();
					sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);
					return new Date(notification.created_at) > sixMonthsAgo;
				}
				return new Date(notification.expire_day) > now;
			})
			.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());
	} catch (error) {
		console.warn('[NotificationDB] 인덱스 조회 실패, 전체 조회로 폴백:', error);

		// 폴백: 전체 데이터를 가져와서 메모리에서 필터링
		const allNotifications = await getAllNotifications();

		// 우선순위로 필터링
		return allNotifications.filter((notification) => {
			const notificationPriority = notification.priority || 'normal';
			return notificationPriority === priority;
		});
	}
}

/**
 * 카테고리별 알림 조회 (인덱스 오류 해결)
 * Requirements: 2.1.1, 2.1.2, 2.1.3, 2.1.4
 */
export async function getNotificationsByCategory(category: string): Promise<NotificationData[]> {
	const db = await initEmployeeDB();

	try {
		// 카테고리 인덱스를 활용한 빠른 조회 시도
		const notifications = await getDataByIndex<NotificationData>(
			db,
			NOTIFICATION_STORE,
			'category',
			category
		);

		// 만료되지 않은 알림만 필터링하고 최신순 정렬
		const now = new Date();
		return notifications
			.filter((notification) => {
				if (!notification.expire_day) {
					// 만료일이 없으면 6개월간 표시
					const sixMonthsAgo = new Date();
					sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);
					return new Date(notification.created_at) > sixMonthsAgo;
				}
				return new Date(notification.expire_day) > now;
			})
			.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());
	} catch (error) {
		console.warn('[NotificationDB] 카테고리 인덱스 조회 실패, 전체 조회로 폴백:', error);

		// 폴백: 전체 데이터를 가져와서 메모리에서 필터링
		const allNotifications = await getAllNotifications();

		// 카테고리로 필터링
		return allNotifications.filter((notification) => notification.category === category);
	}
}

/**
 * 특정 기간 내 알림 조회 (기존 인덱스 활용)
 */
export async function getNotificationsByDateRange(
	startDate: Date,
	endDate: Date
): Promise<NotificationData[]> {
	const db = await initEmployeeDB();

	// created_at 인덱스를 활용한 범위 조회
	const range = IDBKeyRange.bound(startDate.toISOString(), endDate.toISOString(), false, false);

	const notifications = await getDataByRange<NotificationData>(
		db,
		NOTIFICATION_STORE,
		'created_at',
		range
	);

	return notifications.sort(
		(a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
	);
}

/**
 * 우선순위별 알림 통계 조회 (기존 인덱스 활용하여 성능 최적화)
 * Requirements: 2.1.1, 2.1.2, 2.1.3, 2.1.4
 */
export async function getNotificationStatsByPriority(): Promise<Record<string, number>> {
	const stats: Record<string, number> = {
		urgent: 0,
		high: 0,
		normal: 0,
		low: 0
	};

	// 각 우선순위별로 인덱스를 활용하여 카운트
	for (const priority of ['urgent', 'high', 'normal', 'low'] as const) {
		const notifications = await getNotificationsByPriority(priority);
		stats[priority] = notifications.length;
	}

	return stats;
}

/**
 * 카테고리별 알림 통계 조회 (기존 인덱스 활용하여 성능 최적화)
 * Requirements: 2.1.1, 2.1.2, 2.1.3, 2.1.4
 */
export async function getNotificationStatsByCategory(): Promise<Record<string, number>> {
	const activeNotifications = await getActiveNotifications();
	const stats: Record<string, number> = {};

	activeNotifications.forEach((notification) => {
		const category = notification.category || 'uncategorized';
		stats[category] = (stats[category] || 0) + 1;
	});

	return stats;
}

/**
 * 우선순위와 카테고리를 조합한 고급 조회 (기존 인덱스 활용)
 * Requirements: 2.1.1, 2.1.2, 2.1.3, 2.1.4
 */
export async function getNotificationsByPriorityAndCategory(
	priority: 'low' | 'normal' | 'high' | 'urgent',
	category: string
): Promise<NotificationData[]> {
	// 우선순위로 먼저 필터링 (인덱스 활용)
	const priorityNotifications = await getNotificationsByPriority(priority);

	// 카테고리로 추가 필터링
	return priorityNotifications.filter((notification) => notification.category === category);
}

/**
 * 읽지 않은 알림을 우선순위별로 조회 (기존 인덱스 활용)
 * Requirements: 2.1.1, 2.1.2, 2.1.3, 2.1.4
 */
export async function getUnreadNotificationsByPriority(
	priority: 'low' | 'normal' | 'high' | 'urgent'
): Promise<NotificationData[]> {
	const priorityNotifications = await getNotificationsByPriority(priority);
	return priorityNotifications.filter((notification) => !notification.read);
}

/**
 * 삭제 가능한 모든 알림 조회
 * Requirements: 3.3.2
 */
export async function getDeletableNotifications(): Promise<NotificationData[]> {
	const activeNotifications = await getActiveNotifications();
	return activeNotifications.filter((notification) => notification.deletable !== false);
}

/**
 * 읽은 알림들 중 삭제 가능한 알림 일괄 삭제
 * Requirements: 3.2.2, 3.3.2
 */
export async function deleteReadDeletableNotifications(): Promise<number> {
	const activeNotifications = await getActiveNotifications();
	const readDeletableNotifications = activeNotifications.filter(
		(notification) => notification.read && notification.deletable !== false
	);

	let deletedCount = 0;
	for (const notification of readDeletableNotifications) {
		try {
			await deleteNotification(notification.id);
			deletedCount++;
		} catch (error) {
			console.error('알림 삭제 실패:', notification.id, error);
		}
	}

	console.log(`읽은 알림 ${deletedCount}개 삭제 완료`);
	return deletedCount;
}

/**
 * 기존 CRUD 함수들을 활용한 통합 알림 관리 객체
 * Requirements: 3.2.1, 3.2.2, 3.2.3, 3.3.2, 3.3.4, 2.1.1, 2.1.2, 2.1.3, 2.1.4
 */
export const notificationManager = {
	// 기본 조회 함수들 (기존 함수 활용)
	getAll: getAllNotifications,
	getActive: getActiveNotifications,
	getById: getNotification,
	getByDateRange: getNotificationsByDateRange,
	getDeletable: getDeletableNotifications,

	// 우선순위별 조회 함수들 (기존 인덱스 활용하여 성능 최적화)
	getByPriority: getNotificationsByPriority,
	getByCategory: getNotificationsByCategory,
	getByPriorityAndCategory: getNotificationsByPriorityAndCategory,
	getUnreadByPriority: getUnreadNotificationsByPriority,

	// 통계 함수들 (기존 인덱스 활용하여 성능 최적화)
	getUnreadCount: getUnreadNotificationCount,
	getStatsByPriority: getNotificationStatsByPriority,
	getStatsByCategory: getNotificationStatsByCategory,

	// 저장 함수들 (기존 함수 활용)
	save: saveNotification,
	saveReceived: saveReceivedNotification,

	// 읽음 처리 함수들 (기존 함수 활용)
	markAsRead: markNotificationAsRead,
	markAllAsRead: markAllNotificationsAsRead,

	// 삭제 함수들 (기존 함수 활용)
	delete: deleteNotification,
	deleteDeletable: deleteDeletableNotification,
	deleteExpired: deleteExpiredNotifications,
	deleteReadDeletable: deleteReadDeletableNotifications,

	// 데이터베이스 관리 (기존 함수 활용)
	init: initNotificationDatabase,
	close: closeNotificationDatabase
};

/**
 * 알림 데이터베이스 연결 종료
 */
export function closeNotificationDatabase(): void {
	closeEmployeeDB();
}
