/**
 * Pusher Beams 환경 설정 관리
 * 클라이언트 직접 연동을 위한 설정 검증 및 관리
 */

/**
 * Pusher Beams 설정 인터페이스
 */
export interface PusherBeamsConfig {
	instanceId: string;
	// 클라이언트 직접 연동에서는 Instance ID만 필요
	// 다른 Pusher 설정들은 기존 시스템에서 사용
}

/**
 * Firebase 설정 인터페이스 (안드로이드 FCM용)
 */
export interface FirebaseConfig {
	projectId: string;
	senderId: string;
	apiKey: string;
}

/**
 * 환경 설정 검증 결과
 */
export interface ConfigValidationResult {
	isValid: boolean;
	errors: string[];
	warnings: string[];
	config?: {
		pusherBeams: PusherBeamsConfig;
		firebase: FirebaseConfig;
	};
}

/**
 * Pusher Beams 설정 가져오기
 */
export function getPusherBeamsConfig(): PusherBeamsConfig {
	const instanceId = import.meta.env.VITE_PUSHER_BEAMS_INSTANCE_ID;

	if (!instanceId) {
		throw new Error('VITE_PUSHER_BEAMS_INSTANCE_ID 환경 변수가 설정되지 않았습니다.');
	}

	return {
		instanceId
	};
}

/**
 * Firebase 설정 가져오기 (안드로이드 FCM용)
 */
export function getFirebaseConfig(): FirebaseConfig {
	const projectId = import.meta.env.VITE_FIREBASE_PROJECT_ID;
	const senderId = import.meta.env.VITE_FIREBASE_SENDER_ID;
	const apiKey = import.meta.env.VITE_FIREBASE_API_KEY;

	if (!projectId || !senderId || !apiKey) {
		throw new Error('Firebase 환경 변수가 완전히 설정되지 않았습니다.');
	}

	return {
		projectId,
		senderId,
		apiKey
	};
}

/**
 * 환경 설정 검증
 */
export function validatePusherBeamsConfig(): ConfigValidationResult {
	const errors: string[] = [];
	const warnings: string[] = [];

	try {
		// Pusher Beams 설정 검증
		const pusherBeamsConfig = getPusherBeamsConfig();

		// Instance ID 형식 검증 (UUID 형식)
		const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
		if (!uuidRegex.test(pusherBeamsConfig.instanceId)) {
			errors.push('Pusher Beams Instance ID가 올바른 UUID 형식이 아닙니다.');
		}

		// Firebase 설정 검증 (안드로이드용)
		let firebaseConfig: FirebaseConfig;
		try {
			firebaseConfig = getFirebaseConfig();

			// Firebase Project ID 검증
			if (!/^[a-z0-9-]+$/.test(firebaseConfig.projectId)) {
				errors.push('Firebase Project ID 형식이 올바르지 않습니다.');
			}

			// Firebase Sender ID 검증 (숫자만)
			if (!/^\d+$/.test(firebaseConfig.senderId)) {
				errors.push('Firebase Sender ID는 숫자만 포함해야 합니다.');
			}

			// Firebase API Key 검증 (기본적인 형식 확인)
			if (!firebaseConfig.apiKey.startsWith('AIza')) {
				warnings.push('Firebase API Key 형식이 일반적이지 않습니다.');
			}
		} catch (error) {
			errors.push(
				`Firebase 설정 오류: ${error instanceof Error ? error.message : '알 수 없는 오류'}`
			);
			firebaseConfig = { projectId: '', senderId: '', apiKey: '' };
		}

		// 환경별 설정 일관성 확인
		const currentEnv = import.meta.env.VITE_NODE_ENV || 'development';
		if (currentEnv === 'production') {
			// 프로덕션 환경에서는 프로덕션 도메인 사용 확인
			const apiEndpoint = import.meta.env.VITE_API_ENDPOINT;
			if (apiEndpoint && !apiEndpoint.includes('api.cnsprowms.com')) {
				warnings.push('프로덕션 환경에서 개발/스테이징 API 엔드포인트를 사용하고 있습니다.');
			}
		}

		const isValid = errors.length === 0;

		return {
			isValid,
			errors,
			warnings,
			config: isValid
				? {
						pusherBeams: pusherBeamsConfig,
						firebase: firebaseConfig
					}
				: undefined
		};
	} catch (error) {
		errors.push(`설정 검증 중 오류: ${error instanceof Error ? error.message : '알 수 없는 오류'}`);
		return {
			isValid: false,
			errors,
			warnings
		};
	}
}

/**
 * 환경 설정 디버그 정보 출력 (개발 환경에서만)
 */
export function debugPusherBeamsConfig(): void {
	if (import.meta.env.DEV) {
		console.log('[Pusher Beams Config] 환경 설정 디버그 정보');

		const validation = validatePusherBeamsConfig();
		console.log('설정 검증 결과:', validation);

		if (validation.config) {
			console.log('Pusher Beams 설정:', {
				instanceId: validation.config.pusherBeams.instanceId,
				instanceIdMasked: validation.config.pusherBeams.instanceId.replace(/.(?=.{4})/g, '*')
			});

			console.log('Firebase 설정:', {
				projectId: validation.config.firebase.projectId,
				senderId: validation.config.firebase.senderId,
				apiKeyMasked: validation.config.firebase.apiKey.replace(/.(?=.{4})/g, '*')
			});
		}

		// 환경 변수 전체 확인
		console.log('현재 환경:', import.meta.env.VITE_NODE_ENV);
		console.log('API 엔드포인트:', import.meta.env.VITE_API_ENDPOINT);
	}
}

/**
 * 설정 상태 확인 함수
 */
export function checkPusherBeamsSetup(): {
	ready: boolean;
	message: string;
	details?: ConfigValidationResult;
} {
	const validation = validatePusherBeamsConfig();

	if (validation.isValid) {
		return {
			ready: true,
			message: 'Pusher Beams 설정이 올바르게 구성되었습니다.',
			details: validation
		};
	} else {
		return {
			ready: false,
			message: `Pusher Beams 설정에 문제가 있습니다: ${validation.errors.join(', ')}`,
			details: validation
		};
	}
}

/**
 * 개발 환경에서 자동으로 설정 검증 실행
 */
if (import.meta.env.DEV) {
	// 모듈 로드 시 자동 검증 (선택적)
	const setupCheck = checkPusherBeamsSetup();
	if (!setupCheck.ready) {
		console.warn('[Pusher Beams Config]', setupCheck.message);
	} else {
		console.log('[Pusher Beams Config]', setupCheck.message);
	}
}
