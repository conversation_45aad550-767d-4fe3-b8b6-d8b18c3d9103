/**
 * 푸시 서비스 유틸리티 함수들
 */

import type { CreateNotificationData } from '$lib/types/notification';
import type { PushServiceError } from '../types/pushNotificationTypes';

/**
 * 푸시 서비스 유틸리티 함수들
 */
export const pushServiceUtils = {
	/**
	 * 에러 메시지를 사용자 친화적으로 변환
	 */
	getErrorMessage(error: PushServiceError): string {
		switch (error.code) {
			case 'PERMISSION_DENIED':
				return '알림 권한이 거부되었습니다. 설정에서 알림을 허용해주세요.';
			case 'PERMISSION_UNAVAILABLE':
				return '이 기기에서는 알림 기능을 사용할 수 없습니다.';
			case 'PERMISSION_REQUEST_FAILED':
				return '알림 권한 요청에 실패했습니다. 다시 시도해주세요.';
			case 'SERVICE_INIT_FAILED':
				return '푸시 알림 서비스를 시작할 수 없습니다.';
			case 'TOKEN_REG_FAILED':
				return '디바이스 등록에 실패했습니다.';
			case 'NOTIFICATION_SEND_FAILED':
				return '알림 전송에 실패했습니다.';
			case 'PLATFORM_NOT_SUPPORTED':
				return '현재 플랫폼에서는 푸시 알림을 지원하지 않습니다.';
			default:
				return '알 수 없는 오류가 발생했습니다.';
		}
	},

	/**
	 * 개발 환경에서 테스트 알림 생성
	 */
	createTestNotification(content: string = '테스트 알림입니다.'): CreateNotificationData {
		return {
			content,
			expire_day: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString().split('T')[0],
			priority: 'normal',
			category: 'test'
		};
	},

	/**
	 * 기기 ID 생성 (플랫폼별)
	 */
	generateDeviceId(platform: string): string {
		const timestamp = Date.now();
		const random = Math.random().toString(36).substr(2, 9);
		return `${platform}_${timestamp}_${random}`;
	},

	/**
	 * 로컬 스토리지 키 생성
	 */
	getStorageKey(platform: string, type: 'beams' | 'legacy'): string {
		return `${platform}_${type}_device_info`;
	},

	/**
	 * Interest 배열 정규화 (중복 제거, 정렬)
	 */
	normalizeInterests(interests: string[]): string[] {
		return [...new Set(interests)].sort();
	},

	/**
	 * 기기 정보 유효성 검사
	 */
	validateDeviceInfo(deviceInfo: any): boolean {
		return !!(
			deviceInfo &&
			typeof deviceInfo === 'object' &&
			deviceInfo.deviceId &&
			typeof deviceInfo.deviceId === 'string' &&
			Array.isArray(deviceInfo.interests)
		);
	}
};
