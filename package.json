{"name": "cnsprowms", "version": "0.1.0", "description": "", "type": "module", "scripts": {"dev": "vite dev", "build": "vite build", "preview": "vite preview", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "tauri": "tauri", "lint": "prettier --plugin-search-dir . --check . && eslint .", "format": "prettier --plugin-search-dir . --write .", "version-update": "node version-update.mjs", "sig-update": "node sig-update.mjs", "test": "vitest run", "test:watch": "vitest", "test:e2e": "vitest run --config vitest.config.ts src/lib/__tests__/push-service-e2e.test.ts", "test:e2e:platform": "vitest run --config vitest.config.ts src/lib/__tests__/platform-specific-e2e.test.ts", "test:e2e:all": "vitest run --config vitest.config.ts src/lib/__tests__/push-service-e2e.test.ts src/lib/__tests__/platform-specific-e2e.test.ts"}, "license": "MIT", "dependencies": {"@fortawesome/free-brands-svg-icons": "^6.7.2", "@fortawesome/free-regular-svg-icons": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@pusher/push-notifications-web": "^1.1.0", "@tauri-apps/api": "^2.8.0", "@tauri-apps/plugin-dialog": "~2.2.2", "@tauri-apps/plugin-fs": "~2.2.1", "@tauri-apps/plugin-log": "^2.7.0", "@tauri-apps/plugin-notification": "~2.2.0", "@tauri-apps/plugin-opener": "^2.5.0", "@tauri-apps/plugin-os": "~2.3.1", "@tauri-apps/plugin-process": "~2.2.2", "@tauri-apps/plugin-store": "~2.4.0", "@tauri-apps/plugin-updater": "~2.6.1", "@tiptap/core": "^2.26.1", "@tiptap/extension-color": "^2.26.1", "@tiptap/extension-image": "^2.26.1", "@tiptap/extension-link": "^2.26.1", "@tiptap/extension-list-item": "^2.26.1", "@tiptap/extension-placeholder": "^2.26.1", "@tiptap/extension-text-style": "^2.26.1", "@tiptap/extension-underline": "^2.26.1", "@tiptap/pm": "^2.26.1", "@tiptap/starter-kit": "^2.26.1", "@types/file-saver": "^2.0.7", "axios": "^1.12.2", "canvas": "^2.11.2", "file-saver": "^2.0.5", "html2canvas": "^1.4.1", "jsbarcode": "^3.12.1", "pdfmake": "^0.2.20", "svelte-awesome": "^3.3.5", "tauri-plugin-printer-api": "github:felipeejunges/tauri-plugin-printer", "tauri-plugin-remote-push-api": "^1.0.10", "uuid": "^11.1.0"}, "devDependencies": {"@sveltejs/adapter-static": "^3.0.9", "@sveltejs/kit": "^2.42.1", "@sveltejs/vite-plugin-svelte": "^6.2.0", "@tailwindcss/typography": "^0.5.16", "@tauri-apps/cli": "^2.8.4", "@testing-library/svelte": "^5.2.8", "@types/pdfmake": "^0.2.11", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^8.44.0", "@typescript-eslint/parser": "^8.44.0", "autoprefixer": "^10.4.21", "daisyui": "^4.12.24", "eslint": "^9.35.0", "eslint-config-prettier": "^9.1.2", "eslint-plugin-svelte": "^2.46.1", "eslint-plugin-tailwindcss": "^3.18.2", "jsdom": "^26.1.0", "postcss": "^8.5.6", "postcss-load-config": "^4.0.2", "prettier": "^3.6.2", "prettier-plugin-svelte": "^3.4.0", "prettier-plugin-tailwindcss": "^0.6.14", "pusher-js": "^8.4.0", "svelte": "^5.39.0", "svelte-check": "^4.3.1", "svelte-dnd-action": "^0.9.65", "svelte-preprocess": "^6.0.3", "svoast": "^3.0.3", "tailwindcss": "^3.4.17", "tslib": "^2.8.1", "typescript": "^5.9.2", "vite": "^6.3.6", "vitest": "^3.2.4"}, "pnpm": {"onlyBuiltDependencies": ["svelte-preprocess"]}}