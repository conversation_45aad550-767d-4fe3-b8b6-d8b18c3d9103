{"$schema": "../gen/schemas/mobile-schema.json", "identifier": "mobile-capability", "description": "Capability for mobile platforms (Android/iOS)", "windows": ["main", "print"], "platforms": ["iOS", "android"], "permissions": ["core:default", "opener:default", {"identifier": "opener:allow-open-path", "allow": [{"path": "$DOWNLOAD/**"}]}, "dialog:default", "fs:default", {"identifier": "fs:allow-exists", "allow": [{"path": "$DOCUMENT/CornerstoneWMS/**"}, {"path": "$DOWNLOAD/**"}]}, {"identifier": "fs:allow-document-write", "allow": [{"path": "$DOCUMENT/CornerstoneWMS/**"}, {"path": "$DOWNLOAD/**"}]}, {"identifier": "fs:allow-read-file", "allow": [{"path": "$DOCUMENT/CornerstoneWMS/**"}, {"path": "$DOWNLOAD/**"}]}, {"identifier": "fs:allow-remove", "allow": [{"path": "$DOCUMENT/CornerstoneWMS/**"}, {"path": "$DOWNLOAD/**"}]}, "process:default", "notification:default", "remote-push:default", "core:window:allow-start-dragging", "core:webview:allow-create-webview-window", "core:webview:allow-webview-close", "core:window:allow-close", "store:default", "os:default"], "allow": {"windows": ["^coupang-.*"]}}