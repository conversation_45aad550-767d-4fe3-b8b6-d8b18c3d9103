// 업데이트 관련 모듈
// 데스크탑 플랫폼에서만 사용

#[cfg(not(any(target_os = "android", target_os = "ios")))]
use std::time::Duration;
#[cfg(not(any(target_os = "android", target_os = "ios")))]
use tauri::Emitter;
#[cfg(not(any(target_os = "android", target_os = "ios")))]
use tauri_plugin_updater::UpdaterExt;

/// 수동 업데이트 확인 명령어
#[cfg(not(any(target_os = "android", target_os = "ios")))]
#[tauri::command]
pub async fn check_for_updates(app: tauri::AppHandle) -> Result<String, String> {
    match check_for_update(app).await {
        Ok(_) => Ok("업데이트 확인 완료".to_string()),
        Err(e) => Err(format!("업데이트 확인 실패: {}", e)),
    }
}

/// 업데이트 설치 진행 명령어
#[cfg(not(any(target_os = "android", target_os = "ios")))]
#[tauri::command]
pub async fn install_available_update(app: tauri::AppHandle) -> Result<String, String> {
    match app.updater() {
        Ok(updater) => match updater.check().await {
            Ok(Some(update)) => match install_update(app, update).await {
                Ok(_) => Ok("업데이트 설치 완료".to_string()),
                Err(e) => Err(format!("업데이트 설치 실패: {}", e)),
            },
            Ok(None) => Err("사용 가능한 업데이트가 없습니다".to_string()),
            Err(e) => Err(format!("업데이트 확인 실패: {}", e)),
        },
        Err(e) => Err(format!("업데이터 초기화 실패: {}", e)),
    }
}

/// 주기적 업데이트 확인 함수
#[cfg(not(any(target_os = "android", target_os = "ios")))]
pub async fn start_periodic_update_check(app: tauri::AppHandle) {
    let mut interval = tokio::time::interval(Duration::from_secs(30 * 60)); // 30분마다

    loop {
        interval.tick().await;

        if let Err(e) = check_for_update(app.clone()).await {
            eprintln!("Periodic update check error: {}", e);
        }
    }
}

/// 업데이트 확인 함수 (사용자에게 알림만 전송)
#[cfg(not(any(target_os = "android", target_os = "ios")))]
pub async fn check_for_update(app: tauri::AppHandle) -> tauri_plugin_updater::Result<()> {
    if let Some(update) = app.updater()?.check().await? {
        println!("Update available: {}", update.version);

        // 프론트엔드에 업데이트 알림 이벤트 전송 (사용자 확인 대기)
        if let Err(e) = app.emit("update-available", &update.version) {
            eprintln!("Failed to emit update-available event: {}", e);
        }

        // 사용자 확인을 기다리므로 자동 설치하지 않음
        // install_update는 별도 명령어로 호출됨
    }

    Ok(())
}

/// 업데이트 설치 함수
#[cfg(not(any(target_os = "android", target_os = "ios")))]
async fn install_update(
    app: tauri::AppHandle,
    update: tauri_plugin_updater::Update,
) -> tauri_plugin_updater::Result<()> {
    let mut downloaded = 0;

    // 다운로드 진행률을 프론트엔드에 전송
    update
        .download_and_install(
            |chunk_length, content_length| {
                downloaded += chunk_length;
                let progress = if let Some(total) = content_length {
                    (downloaded as f64 / total as f64 * 100.0) as u32
                } else {
                    0
                };

                // 프론트엔드에 다운로드 진행률 전송
                if let Err(e) = app.emit("update-download-progress", progress) {
                    eprintln!("Failed to emit download progress: {}", e);
                }

                println!("downloaded {downloaded} from {content_length:?}");
            },
            || {
                println!("download finished");
                // 다운로드 완료 이벤트 전송
                if let Err(e) = app.emit("update-download-finished", ()) {
                    eprintln!("Failed to emit download finished event: {}", e);
                }
            },
        )
        .await?;

    println!("update installed");

    // 업데이트 설치 완료 이벤트 전송
    if let Err(e) = app.emit("update-installed", ()) {
        eprintln!("Failed to emit update installed event: {}", e);
    }

    // 앱 재시작 (이 함수는 반환하지 않음)
    app.restart();
}

/// 앱 시작 시 업데이트 초기화
#[cfg(not(any(target_os = "android", target_os = "ios")))]
pub fn initialize_updater(app: &tauri::AppHandle) {
    let handle = app.clone();

    // 앱 시작 시 즉시 업데이트 확인
    tauri::async_runtime::spawn(async move {
        if let Err(e) = check_for_update(handle.clone()).await {
            eprintln!("Initial update check error: {}", e);
        }
    });

    // 주기적 업데이트 확인 (30분마다)
    let handle_periodic = app.clone();
    tauri::async_runtime::spawn(async move {
        start_periodic_update_check(handle_periodic).await;
    });
}
