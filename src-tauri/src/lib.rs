// 모듈 선언
#[cfg(not(any(target_os = "android", target_os = "ios")))]
mod updater;

// Learn more about Tauri commands at https://tauri.app/develop/calling-rust/
#[tauri::command]
fn greet(name: &str) -> String {
    format!("Hello, {}! You\'ve been greeted from Rust!", name)
}

// 데스크탑 전용 플러그인 import
#[cfg(not(any(target_os = "android", target_os = "ios")))]
use tauri_plugin_printer;

// Android 전용 플러그인 import
#[cfg(target_os = "android")]
use tauri_plugin_remote_push;

#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
    let mut builder = tauri::Builder::default();

    // 공통 플러그인 등록 (모든 플랫폼에서 사용)
    builder = builder
        .plugin(tauri_plugin_store::Builder::new().build())
        .plugin(tauri_plugin_process::init())
        .plugin(tauri_plugin_fs::init())
        .plugin(tauri_plugin_dialog::init())
        .plugin(tauri_plugin_notification::init())
        .plugin(tauri_plugin_opener::init())
        .plugin(tauri_plugin_os::init());

    // 데스크탑 전용 플러그인 등록 (안드로이드/iOS에서 제외)
    #[cfg(not(any(target_os = "android", target_os = "ios")))]
    {
        builder = builder
            .plugin(tauri_plugin_updater::Builder::new().build())
            .plugin(tauri_plugin_printer::init());
    }

    // Android 전용 플러그인 등록
    #[cfg(target_os = "android")]
    {
        builder = builder.plugin(tauri_plugin_remote_push::init());
    }

    builder
        .invoke_handler(tauri::generate_handler![
            greet,
            #[cfg(not(any(target_os = "android", target_os = "ios")))]
            updater::check_for_updates,
            #[cfg(not(any(target_os = "android", target_os = "ios")))]
            updater::install_available_update
        ])
        .setup(|_app| {
            // 데스크탑에서만 업데이터 실행
            #[cfg(not(any(target_os = "android", target_os = "ios")))]
            {
                updater::initialize_updater(_app.handle());
            }
            Ok(())
        })
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
